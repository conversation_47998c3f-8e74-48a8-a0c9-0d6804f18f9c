import { NextRequest } from "next/server";
import { createClient } from "@/utils/supabase/server";
import { verifyJWTToken, isWebClient } from "@/lib/auth/jwt";
import { User } from "@supabase/supabase-js";

export interface AuthContext {
  user: User | null;
  userId: string | null;
  deviceId?: string;
  isAuthenticated: boolean;
  isWeb: boolean;
  isMobile: boolean;
  supabase: any; // Supabase client with proper user context
  shouldLogout?: boolean; // For web clients with invalid JWT
}

/**
 * Unified authentication context for both web and mobile clients
 *
 * Web: Uses Supabase session from cookies
 * Mobile: Uses JWT + optional HMAC, creates user-scoped Supabase client
 *
 * Both approaches result in the same user context with RLS applied
 */
export async function getAuthContext(req: NextRequest): Promise<AuthContext> {
  const isWeb = isWebClient(req);

  // Try mobile authentication first (JWT in Authorization header)
  if (!isWeb) {
    return await getMobileAuthContext(req);
  }

  // Try web authentication (Supabase session from cookies)
  return await getWebAuthContext(req);
}

/**
 * Handle mobile authentication via JWT
 */
async function getMobileAuthContext(req: NextRequest): Promise<AuthContext> {
  const authHeader = req.headers.get("authorization");

  if (!authHeader || !authHeader.startsWith("Bearer ")) {
    return createUnauthenticatedContext(false, true);
  }

  const token = authHeader.substring(7);
  const jwtResult = await verifyJWTToken(token);

  if (!jwtResult.success || !jwtResult.payload) {
    return createUnauthenticatedContext(false, true);
  }

  const userId = jwtResult.payload.user_id || jwtResult.payload.sub;
  if (!userId) {
    return createUnauthenticatedContext(false, true);
  }

  // Create Supabase client with user context for RLS
  const supabase = await createUserScopedSupabaseClient(userId);

  // Get device ID if present (for HMAC verification later)
  const deviceId = req.headers.get("x-device-id") || undefined;

  return {
    user: null, // We don't need full user object for mobile, just ID
    userId,
    deviceId,
    isAuthenticated: true,
    isWeb: false,
    isMobile: true,
    supabase,
  };
}

/**
 * Handle web authentication via Supabase session
 */
async function getWebAuthContext(req: NextRequest): Promise<AuthContext> {
  try {
    const supabase = await createClient();
    const {
      data: { user },
      error,
    } = await supabase.auth.getUser();

    if (error || !user) {
      // Check if there's an invalid JWT that needs logout handling
      const authHeader = req.headers.get("authorization");
      if (authHeader && authHeader.startsWith("Bearer ")) {
        const token = authHeader.substring(7);
        const jwtResult = await verifyJWTToken(token);

        if (!jwtResult.success) {
          return {
            user: null,
            userId: null,
            isAuthenticated: false,
            isWeb: true,
            isMobile: false,
            supabase,
            shouldLogout: true, // Signal web client to logout
          };
        }
      }

      return createUnauthenticatedContext(true, false, supabase);
    }

    return {
      user,
      userId: user.id,
      isAuthenticated: true,
      isWeb: true,
      isMobile: false,
      supabase, // Already has user context from session
    };
  } catch (error) {
    console.error("Error getting web auth context:", error);
    const supabase = await createClient();
    return createUnauthenticatedContext(true, false, supabase);
  }
}

/**
 * Create a Supabase client scoped to a specific user for RLS
 */
async function createUserScopedSupabaseClient(userId: string) {
  // Create a regular Supabase client
  const supabase = await createClient();

  // For mobile API routes, we'll handle RLS by ensuring the user context
  // is properly set. Since we have the userId from JWT, we can create
  // a client that respects RLS policies.

  // Store the userId in the client for easy access in route handlers
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  (supabase as any)._userId = userId;

  return supabase;
}

/**
 * Create unauthenticated context
 */
function createUnauthenticatedContext(
  isWeb: boolean,
  isMobile: boolean,
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  supabase?: any
): AuthContext {
  return {
    user: null,
    userId: null,
    isAuthenticated: false,
    isWeb,
    isMobile,
    supabase: supabase || null,
  };
}

/**
 * Type guard to check if user is authenticated
 */
export function isAuthenticated(
  context: AuthContext
): context is AuthContext & {
  userId: string;
  isAuthenticated: true;
} {
  return context.isAuthenticated && !!context.userId;
}

/**
 * Helper to get user ID from context (throws if not authenticated)
 */
export function requireUserId(context: AuthContext): string {
  if (!isAuthenticated(context)) {
    throw new Error("User not authenticated");
  }
  return context.userId;
}

/**
 * Helper to get Supabase client from context
 */
export function getSupabaseClient(context: AuthContext) {
  if (!context.supabase) {
    throw new Error("Supabase client not available");
  }
  return context.supabase;
}
