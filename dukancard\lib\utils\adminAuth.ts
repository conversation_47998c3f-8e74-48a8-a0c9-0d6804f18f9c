import { createClient } from '@/utils/supabase/server';
import { createServiceRoleClient } from '@/utils/supabase/service-role';

/**
 * Verify if a user has admin role from their metadata
 */
export async function verifyAdminRole(userId: string): Promise<boolean> {
  const adminClient = createServiceRoleClient();
  
  try {
    const { data: user, error } = await adminClient.auth.admin.getUserById(userId);
    
    if (error || !user) {
      return false;
    }

    const userRole = user.user?.user_metadata?.role;
    return userRole === 'admin';
  } catch (error) {
    console.error('Error verifying admin role:', error);
    return false;
  }
}

/**
 * Get admin user from request and verify admin role
 */
export async function getAdminUser(request: Request) {
  try {
    const supabase = await createClient();
    const { data: { user }, error } = await supabase.auth.getUser();
    
    if (error || !user) {
      return null;
    }

    // Verify admin role
    const isAdmin = await verifyAdminRole(user.id);
    if (!isAdmin) {
      return null;
    }

    return user;
  } catch (error) {
    console.error('Error getting admin user:', error);
    return null;
  }
}

/**
 * Middleware to check admin authentication
 */
export async function requireAdmin(request: Request) {
  const adminUser = await getAdminUser(request);
  
  if (!adminUser) {
    return Response.json({ error: 'Admin access required' }, { status: 403 });
  }
  
  return adminUser;
}