'use client';

import { useState } from 'react';
import { useAdminDevices } from '@/lib/hooks/admin/useAdminDevices';
import { toast } from 'sonner';
import DeviceDetailModal from './DeviceDetailModal';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from '@/components/ui/table';
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { 
  Search,
  Filter,
  MoreHorizontal,
  Smartphone,
  Shield,
  ShieldOff,
  Calendar,
  Activity
} from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';

export default function DeviceManagementTab() {
  const [searchTerm, setSearchTerm] = useState('');
  const [platformFilter, setPlatformFilter] = useState<string>('all');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [selectedDevice, setSelectedDevice] = useState<any>(null);
  const [detailModalOpen, setDetailModalOpen] = useState(false);

  const {
    devices,
    loading,
    error,
    pagination,
    updateFilters,
    revokeDevice,
    quarantineDevice,
  } = useAdminDevices({
    search: searchTerm || undefined,
    platform: platformFilter !== 'all' ? platformFilter : undefined,
    status: statusFilter !== 'all' ? statusFilter : undefined,
  });

  const handleSearchChange = (value: string) => {
    setSearchTerm(value);
    updateFilters({ search: value || undefined, page: 1 });
  };

  const handlePlatformChange = (value: string) => {
    setPlatformFilter(value);
    updateFilters({ platform: value !== 'all' ? value : undefined, page: 1 });
  };

  const handleStatusChange = (value: string) => {
    setStatusFilter(value);
    updateFilters({ status: value !== 'all' ? value : undefined, page: 1 });
  };

  const handleRevokeDevice = async (deviceId: string) => {
    const result = await revokeDevice(deviceId, 'Admin revocation from dashboard');
    if (result.success) {
      toast.success('Device revoked successfully');
    } else {
      toast.error(result.error || 'Failed to revoke device');
    }
  };

  const handleQuarantineDevice = async (deviceId: string) => {
    const result = await quarantineDevice(deviceId, 'Admin quarantine from dashboard');
    if (result.success) {
      toast.success('Device quarantined successfully');
    } else {
      toast.error(result.error || 'Failed to quarantine device');
    }
  };

  const handleViewDevice = async (device: any) => {
    try {
      // Fetch detailed device information
      const response = await fetch(`/api/admin/devices/${device.device_id}`);
      if (response.ok) {
        const deviceDetails = await response.json();
        setSelectedDevice(deviceDetails);
        setDetailModalOpen(true);
      } else {
        toast.error('Failed to load device details');
      }
    } catch (error) {
      toast.error('Error loading device details');
    }
  };

  const getRiskBadgeColor = (score?: number) => {
    if (!score) return 'secondary';
    if (score >= 80) return 'destructive';
    if (score >= 50) return 'default';
    return 'secondary';
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Smartphone className="h-5 w-5" />
          Device Management
        </CardTitle>
        <CardDescription>
          Monitor and manage all registered devices across the platform
        </CardDescription>
      </CardHeader>
      <CardContent>
        {/* Filters */}
        <div className="flex flex-col sm:flex-row gap-4 mb-6">
          <div className="flex-1 relative">
            <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search devices or users..."
              value={searchTerm}
              onChange={(e) => handleSearchChange(e.target.value)}
              className="pl-8"
            />
          </div>
          <Select value={platformFilter} onValueChange={handlePlatformChange}>
            <SelectTrigger className="w-[140px]">
              <SelectValue placeholder="Platform" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Platforms</SelectItem>
              <SelectItem value="ios">iOS</SelectItem>
              <SelectItem value="android">Android</SelectItem>
            </SelectContent>
          </Select>
          <Select value={statusFilter} onValueChange={handleStatusChange}>
            <SelectTrigger className="w-[120px]">
              <SelectValue placeholder="Status" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Status</SelectItem>
              <SelectItem value="active">Active</SelectItem>
              <SelectItem value="revoked">Revoked</SelectItem>
            </SelectContent>
          </Select>
        </div>

        {/* Device Table */}
        <div className="rounded-md border">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Device</TableHead>
                <TableHead>User</TableHead>
                <TableHead>Platform</TableHead>
                <TableHead>Version</TableHead>
                <TableHead>Registered</TableHead>
                <TableHead>Last Activity</TableHead>
                <TableHead>Risk Score</TableHead>
                <TableHead>Status</TableHead>
                <TableHead className="text-right">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {loading ? (
                <TableRow>
                  <TableCell colSpan={9} className="text-center py-8">
                    Loading devices...
                  </TableCell>
                </TableRow>
              ) : error ? (
                <TableRow>
                  <TableCell colSpan={9} className="text-center py-8 text-red-600">
                    Error: {error}
                  </TableCell>
                </TableRow>
              ) : devices.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={9} className="text-center py-8">
                    No devices found matching your criteria
                  </TableCell>
                </TableRow>
              ) : (
                devices.map((device) => (
                  <TableRow key={device.device_id}>
                    <TableCell className="font-medium">
                      {device.device_name}
                    </TableCell>
                    <TableCell>{device.user_email}</TableCell>
                    <TableCell>
                      <Badge variant="outline" className="capitalize">
                        {device.platform}
                      </Badge>
                    </TableCell>
                    <TableCell className="font-mono text-sm">
                      {device.app_version}
                    </TableCell>
                    <TableCell className="text-sm">
                      {formatDate(device.registered_at)}
                    </TableCell>
                    <TableCell className="text-sm">
                      {formatDate(device.last_activity)}
                    </TableCell>
                    <TableCell>
                      {device.risk_score && (
                        <Badge variant={getRiskBadgeColor(device.risk_score)}>
                          {device.risk_score}
                        </Badge>
                      )}
                    </TableCell>
                    <TableCell>
                      <Badge variant={device.is_revoked ? 'destructive' : 'default'}>
                        {device.is_revoked ? (
                          <>
                            <ShieldOff className="h-3 w-3 mr-1" />
                            Revoked
                          </>
                        ) : (
                          <>
                            <Shield className="h-3 w-3 mr-1" />
                            Active
                          </>
                        )}
                      </Badge>
                    </TableCell>
                    <TableCell className="text-right">
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" className="h-8 w-8 p-0">
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          {!device.is_revoked && (
                            <>
                              <DropdownMenuItem
                                onClick={() => handleRevokeDevice(device.device_id)}
                                className="text-destructive"
                              >
                                <ShieldOff className="h-4 w-4 mr-2" />
                                Revoke Device
                              </DropdownMenuItem>
                              <DropdownMenuItem
                                onClick={() => handleQuarantineDevice(device.device_id)}
                              >
                                <Shield className="h-4 w-4 mr-2" />
                                Quarantine
                              </DropdownMenuItem>
                            </>
                          )}
                          <DropdownMenuItem onClick={() => handleViewDevice(device)}>
                            <Activity className="h-4 w-4 mr-2" />
                            View Details
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        </div>
      </CardContent>

      {/* Device Detail Modal */}
      <DeviceDetailModal
        device={selectedDevice}
        isOpen={detailModalOpen}
        onClose={() => {
          setDetailModalOpen(false);
          setSelectedDevice(null);
        }}
        onRevoke={handleRevokeDevice}
        onQuarantine={handleQuarantineDevice}
      />
    </Card>
  );
}