export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export type Database = {
  // Allows to automatically instanciate createClient with right options
  // instead of createClient<Database, { PostgrestVersion: 'XX' }>(URL, KEY)
  __InternalSupabase: {
    PostgrestVersion: "12.2.3 (519615d)"
  }
  public: {
    Tables: {
      blogs: {
        Row: {
          author_email: string | null
          author_name: string
          categories: string[] | null
          content: string
          created_at: string
          excerpt: string | null
          featured_image_url: string | null
          id: string
          meta_description: string | null
          meta_title: string | null
          published_at: string | null
          reading_time_minutes: number | null
          slug: string | null
          status: string
          tags: string[] | null
          title: string
          updated_at: string
        }
        Insert: {
          author_email?: string | null
          author_name?: string
          categories?: string[] | null
          content: string
          created_at?: string
          excerpt?: string | null
          featured_image_url?: string | null
          id?: string
          meta_description?: string | null
          meta_title?: string | null
          published_at?: string | null
          reading_time_minutes?: number | null
          slug?: string | null
          status?: string
          tags?: string[] | null
          title: string
          updated_at?: string
        }
        Update: {
          author_email?: string | null
          author_name?: string
          categories?: string[] | null
          content?: string
          created_at?: string
          excerpt?: string | null
          featured_image_url?: string | null
          id?: string
          meta_description?: string | null
          meta_title?: string | null
          published_at?: string | null
          reading_time_minutes?: number | null
          slug?: string | null
          status?: string
          tags?: string[] | null
          title?: string
          updated_at?: string
        }
        Relationships: []
      }
      business_posts: {
        Row: {
          business_id: string
          city_slug: string | null
          content: string
          created_at: string
          id: string
          image_url: string | null
          locality_slug: string | null
          mentioned_business_ids: string[] | null
          pincode: string | null
          product_ids: string[] | null
          state_slug: string | null
          updated_at: string
        }
        Insert: {
          business_id: string
          city_slug?: string | null
          content: string
          created_at?: string
          id?: string
          image_url?: string | null
          locality_slug?: string | null
          mentioned_business_ids?: string[] | null
          pincode?: string | null
          product_ids?: string[] | null
          state_slug?: string | null
          updated_at?: string
        }
        Update: {
          business_id?: string
          city_slug?: string | null
          content?: string
          created_at?: string
          id?: string
          image_url?: string | null
          locality_slug?: string | null
          mentioned_business_ids?: string[] | null
          pincode?: string | null
          product_ids?: string[] | null
          state_slug?: string | null
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "business_posts_business_id_fkey"
            columns: ["business_id"]
            isOneToOne: false
            referencedRelation: "business_profiles"
            referencedColumns: ["id"]
          },
        ]
      }
      business_profiles: {
        Row: {
          about_bio: string | null
          address_line: string | null
          average_rating: number | null
          business_category: string | null
          business_hours: Json | null
          business_name: string
          business_slug: string | null
          city: string | null
          city_slug: string | null
          contact_email: string | null
          created_at: string
          delivery_info: string | null
          established_year: number | null
          facebook_url: string | null
          gallery: Json | null
          id: string
          instagram_url: string | null
          latitude: number | null
          locality: string | null
          locality_slug: string | null
          logo_url: string | null
          longitude: number | null
          member_name: string | null
          phone: string | null
          pincode: string | null
          state: string | null
          state_slug: string | null
          status: string
          title: string | null
          total_likes: number
          total_subscriptions: number
          updated_at: string
          whatsapp_number: string | null
        }
        Insert: {
          about_bio?: string | null
          address_line?: string | null
          average_rating?: number | null
          business_category?: string | null
          business_hours?: Json | null
          business_name: string
          business_slug?: string | null
          city?: string | null
          city_slug?: string | null
          contact_email?: string | null
          created_at?: string
          delivery_info?: string | null
          established_year?: number | null
          facebook_url?: string | null
          gallery?: Json | null
          id: string
          instagram_url?: string | null
          latitude?: number | null
          locality?: string | null
          locality_slug?: string | null
          logo_url?: string | null
          longitude?: number | null
          member_name?: string | null
          phone?: string | null
          pincode?: string | null
          state?: string | null
          state_slug?: string | null
          status?: string
          title?: string | null
          total_likes?: number
          total_subscriptions?: number
          updated_at?: string
          whatsapp_number?: string | null
        }
        Update: {
          about_bio?: string | null
          address_line?: string | null
          average_rating?: number | null
          business_category?: string | null
          business_hours?: Json | null
          business_name?: string
          business_slug?: string | null
          city?: string | null
          city_slug?: string | null
          contact_email?: string | null
          created_at?: string
          delivery_info?: string | null
          established_year?: number | null
          facebook_url?: string | null
          gallery?: Json | null
          id?: string
          instagram_url?: string | null
          latitude?: number | null
          locality?: string | null
          locality_slug?: string | null
          logo_url?: string | null
          longitude?: number | null
          member_name?: string | null
          phone?: string | null
          pincode?: string | null
          state?: string | null
          state_slug?: string | null
          status?: string
          title?: string | null
          total_likes?: number
          total_subscriptions?: number
          updated_at?: string
          whatsapp_number?: string | null
        }
        Relationships: []
      }
      comment_likes: {
        Row: {
          comment_id: string
          created_at: string
          id: string
          updated_at: string
          user_id: string
        }
        Insert: {
          comment_id: string
          created_at?: string
          id?: string
          updated_at?: string
          user_id: string
        }
        Update: {
          comment_id?: string
          created_at?: string
          id?: string
          updated_at?: string
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "comment_likes_comment_id_fkey"
            columns: ["comment_id"]
            isOneToOne: false
            referencedRelation: "post_comments"
            referencedColumns: ["id"]
          },
        ]
      }
      custom_ad_targets: {
        Row: {
          ad_id: string
          created_at: string
          id: string
          is_global: boolean
          pincode: string
        }
        Insert: {
          ad_id: string
          created_at?: string
          id?: string
          is_global?: boolean
          pincode: string
        }
        Update: {
          ad_id?: string
          created_at?: string
          id?: string
          is_global?: boolean
          pincode?: string
        }
        Relationships: [
          {
            foreignKeyName: "custom_ad_targets_ad_id_fkey"
            columns: ["ad_id"]
            isOneToOne: false
            referencedRelation: "ad_targets_view"
            referencedColumns: ["ad_id"]
          },
          {
            foreignKeyName: "custom_ad_targets_ad_id_fkey"
            columns: ["ad_id"]
            isOneToOne: false
            referencedRelation: "custom_ads"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "custom_ad_targets_ad_id_fkey"
            columns: ["ad_id"]
            isOneToOne: false
            referencedRelation: "expired_ads_view"
            referencedColumns: ["ad_id"]
          },
        ]
      }
      custom_ads: {
        Row: {
          ad_image_url: string
          ad_link_url: string | null
          created_at: string
          expiry_date: string | null
          id: string
          is_active: boolean
        }
        Insert: {
          ad_image_url: string
          ad_link_url?: string | null
          created_at?: string
          expiry_date?: string | null
          id?: string
          is_active?: boolean
        }
        Update: {
          ad_image_url?: string
          ad_link_url?: string | null
          created_at?: string
          expiry_date?: string | null
          id?: string
          is_active?: boolean
        }
        Relationships: []
      }
      customer_posts: {
        Row: {
          city_slug: string | null
          content: string
          created_at: string
          customer_id: string
          id: string
          image_url: string | null
          locality_slug: string | null
          mentioned_business_ids: string[] | null
          pincode: string | null
          state_slug: string | null
          updated_at: string
        }
        Insert: {
          city_slug?: string | null
          content: string
          created_at?: string
          customer_id: string
          id?: string
          image_url?: string | null
          locality_slug?: string | null
          mentioned_business_ids?: string[] | null
          pincode?: string | null
          state_slug?: string | null
          updated_at?: string
        }
        Update: {
          city_slug?: string | null
          content?: string
          created_at?: string
          customer_id?: string
          id?: string
          image_url?: string | null
          locality_slug?: string | null
          mentioned_business_ids?: string[] | null
          pincode?: string | null
          state_slug?: string | null
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "customer_posts_customer_id_fkey"
            columns: ["customer_id"]
            isOneToOne: false
            referencedRelation: "customer_profiles"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "customer_posts_customer_id_fkey"
            columns: ["customer_id"]
            isOneToOne: false
            referencedRelation: "customer_profiles_public"
            referencedColumns: ["id"]
          },
        ]
      }
      customer_profiles: {
        Row: {
          address: string | null
          avatar_url: string | null
          city: string | null
          city_slug: string | null
          created_at: string
          email: string | null
          id: string
          latitude: number | null
          locality: string | null
          locality_slug: string | null
          longitude: number | null
          name: string | null
          phone: string | null
          pincode: string | null
          state: string | null
          state_slug: string | null
          updated_at: string
        }
        Insert: {
          address?: string | null
          avatar_url?: string | null
          city?: string | null
          city_slug?: string | null
          created_at?: string
          email?: string | null
          id: string
          latitude?: number | null
          locality?: string | null
          locality_slug?: string | null
          longitude?: number | null
          name?: string | null
          phone?: string | null
          pincode?: string | null
          state?: string | null
          state_slug?: string | null
          updated_at?: string
        }
        Update: {
          address?: string | null
          avatar_url?: string | null
          city?: string | null
          city_slug?: string | null
          created_at?: string
          email?: string | null
          id?: string
          latitude?: number | null
          locality?: string | null
          locality_slug?: string | null
          longitude?: number | null
          name?: string | null
          phone?: string | null
          pincode?: string | null
          state?: string | null
          state_slug?: string | null
          updated_at?: string
        }
        Relationships: []
      }
      device_audit_logs: {
        Row: {
          action_type: string
          admin_user_id: string
          created_at: string
          ip_address: string | null
          log_id: string
          metadata: Json | null
          reason: string | null
          target_device_id: string | null
          target_user_id: string | null
          timestamp: string
        }
        Insert: {
          action_type: string
          admin_user_id: string
          created_at?: string
          ip_address?: string | null
          log_id?: string
          metadata?: Json | null
          reason?: string | null
          target_device_id?: string | null
          target_user_id?: string | null
          timestamp?: string
        }
        Update: {
          action_type?: string
          admin_user_id?: string
          created_at?: string
          ip_address?: string | null
          log_id?: string
          metadata?: Json | null
          reason?: string | null
          target_device_id?: string | null
          target_user_id?: string | null
          timestamp?: string
        }
        Relationships: [
          {
            foreignKeyName: "fk_target_device"
            columns: ["target_device_id"]
            isOneToOne: false
            referencedRelation: "devices"
            referencedColumns: ["device_id"]
          },
        ]
      }
      devices: {
        Row: {
          app_signature_hash: string | null
          app_version: string | null
          created_at: string
          device_id: string
          device_name: string | null
          device_secret_hash: string
          hmac_key_hash: string | null
          is_quarantined: boolean | null
          last_seen_at: string | null
          platform: string | null
          quarantine_reason: string | null
          quarantined_at: string | null
          revocation_reason: string | null
          revoked: boolean
          revoked_at: string | null
          risk_score: number | null
          user_id: string
        }
        Insert: {
          app_signature_hash?: string | null
          app_version?: string | null
          created_at?: string
          device_id?: string
          device_name?: string | null
          device_secret_hash: string
          hmac_key_hash?: string | null
          is_quarantined?: boolean | null
          last_seen_at?: string | null
          platform?: string | null
          quarantine_reason?: string | null
          quarantined_at?: string | null
          revocation_reason?: string | null
          revoked?: boolean
          revoked_at?: string | null
          risk_score?: number | null
          user_id: string
        }
        Update: {
          app_signature_hash?: string | null
          app_version?: string | null
          created_at?: string
          device_id?: string
          device_name?: string | null
          device_secret_hash?: string
          hmac_key_hash?: string | null
          is_quarantined?: boolean | null
          last_seen_at?: string | null
          platform?: string | null
          quarantine_reason?: string | null
          quarantined_at?: string | null
          revocation_reason?: string | null
          revoked?: boolean
          revoked_at?: string | null
          risk_score?: number | null
          user_id?: string
        }
        Relationships: []
      }
      jwt_blacklist: {
        Row: {
          admin_user_id: string | null
          blacklisted_at: string
          created_at: string
          device_id: string | null
          expires_at: string
          id: string
          jti: string
          reason: string | null
          user_id: string
        }
        Insert: {
          admin_user_id?: string | null
          blacklisted_at?: string
          created_at?: string
          device_id?: string | null
          expires_at: string
          id?: string
          jti: string
          reason?: string | null
          user_id: string
        }
        Update: {
          admin_user_id?: string | null
          blacklisted_at?: string
          created_at?: string
          device_id?: string | null
          expires_at?: string
          id?: string
          jti?: string
          reason?: string | null
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "fk_jwt_device"
            columns: ["device_id"]
            isOneToOne: false
            referencedRelation: "devices"
            referencedColumns: ["device_id"]
          },
        ]
      }
      likes: {
        Row: {
          business_profile_id: string
          created_at: string
          id: string
          user_id: string
        }
        Insert: {
          business_profile_id: string
          created_at?: string
          id?: string
          user_id: string
        }
        Update: {
          business_profile_id?: string
          created_at?: string
          id?: string
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "likes_business_profile_id_fkey"
            columns: ["business_profile_id"]
            isOneToOne: false
            referencedRelation: "business_profiles"
            referencedColumns: ["id"]
          },
        ]
      }
      pincodes: {
        Row: {
          CircleName: string
          city_slug: string | null
          created_at: string
          Delivery: string
          District: string
          DivisionName: string
          id: number
          Latitude: string
          locality_slug: string | null
          Longitude: string
          OfficeName: string
          OfficeType: string
          Pincode: string
          RegionName: string
          state_slug: string | null
          StateName: string
        }
        Insert: {
          CircleName: string
          city_slug?: string | null
          created_at?: string
          Delivery: string
          District: string
          DivisionName: string
          id?: number
          Latitude: string
          locality_slug?: string | null
          Longitude: string
          OfficeName: string
          OfficeType: string
          Pincode: string
          RegionName: string
          state_slug?: string | null
          StateName: string
        }
        Update: {
          CircleName?: string
          city_slug?: string | null
          created_at?: string
          Delivery?: string
          District?: string
          DivisionName?: string
          id?: number
          Latitude?: string
          locality_slug?: string | null
          Longitude?: string
          OfficeName?: string
          OfficeType?: string
          Pincode?: string
          RegionName?: string
          state_slug?: string | null
          StateName?: string
        }
        Relationships: []
      }
      post_comments: {
        Row: {
          content: string
          created_at: string
          edited_at: string | null
          id: string
          is_edited: boolean
          is_pinned: boolean
          parent_comment_id: string | null
          post_id: string
          post_source: string
          updated_at: string
          user_id: string
        }
        Insert: {
          content: string
          created_at?: string
          edited_at?: string | null
          id?: string
          is_edited?: boolean
          is_pinned?: boolean
          parent_comment_id?: string | null
          post_id: string
          post_source: string
          updated_at?: string
          user_id: string
        }
        Update: {
          content?: string
          created_at?: string
          edited_at?: string | null
          id?: string
          is_edited?: boolean
          is_pinned?: boolean
          parent_comment_id?: string | null
          post_id?: string
          post_source?: string
          updated_at?: string
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "post_comments_parent_comment_id_fkey"
            columns: ["parent_comment_id"]
            isOneToOne: false
            referencedRelation: "post_comments"
            referencedColumns: ["id"]
          },
        ]
      }
      post_likes: {
        Row: {
          created_at: string
          id: string
          post_id: string
          post_source: string
          updated_at: string
          user_id: string
        }
        Insert: {
          created_at?: string
          id?: string
          post_id: string
          post_source: string
          updated_at?: string
          user_id: string
        }
        Update: {
          created_at?: string
          id?: string
          post_id?: string
          post_source?: string
          updated_at?: string
          user_id?: string
        }
        Relationships: []
      }
      product_variants: {
        Row: {
          base_price: number | null
          created_at: string
          discounted_price: number | null
          featured_image_index: number | null
          id: string
          images: string[] | null
          is_available: boolean
          product_id: string
          updated_at: string
          variant_name: string
          variant_values: Json
        }
        Insert: {
          base_price?: number | null
          created_at?: string
          discounted_price?: number | null
          featured_image_index?: number | null
          id?: string
          images?: string[] | null
          is_available?: boolean
          product_id: string
          updated_at?: string
          variant_name: string
          variant_values?: Json
        }
        Update: {
          base_price?: number | null
          created_at?: string
          discounted_price?: number | null
          featured_image_index?: number | null
          id?: string
          images?: string[] | null
          is_available?: boolean
          product_id?: string
          updated_at?: string
          variant_name?: string
          variant_values?: Json
        }
        Relationships: [
          {
            foreignKeyName: "product_variants_product_id_fkey"
            columns: ["product_id"]
            isOneToOne: false
            referencedRelation: "products_services"
            referencedColumns: ["id"]
          },
        ]
      }
      products_services: {
        Row: {
          base_price: number | null
          business_id: string
          created_at: string
          description: string | null
          dimensions_cm: Json | null
          discounted_price: number | null
          featured_image_index: number | null
          id: string
          image_url: string | null
          images: string[] | null
          is_available: boolean
          is_fragile: boolean | null
          is_high_value: boolean | null
          is_oversized: boolean | null
          name: string
          product_type: string | null
          slug: string | null
          updated_at: string
          weight_kg: number | null
        }
        Insert: {
          base_price?: number | null
          business_id: string
          created_at?: string
          description?: string | null
          dimensions_cm?: Json | null
          discounted_price?: number | null
          featured_image_index?: number | null
          id?: string
          image_url?: string | null
          images?: string[] | null
          is_available?: boolean
          is_fragile?: boolean | null
          is_high_value?: boolean | null
          is_oversized?: boolean | null
          name: string
          product_type?: string | null
          slug?: string | null
          updated_at?: string
          weight_kg?: number | null
        }
        Update: {
          base_price?: number | null
          business_id?: string
          created_at?: string
          description?: string | null
          dimensions_cm?: Json | null
          discounted_price?: number | null
          featured_image_index?: number | null
          id?: string
          image_url?: string | null
          images?: string[] | null
          is_available?: boolean
          is_fragile?: boolean | null
          is_high_value?: boolean | null
          is_oversized?: boolean | null
          name?: string
          product_type?: string | null
          slug?: string | null
          updated_at?: string
          weight_kg?: number | null
        }
        Relationships: [
          {
            foreignKeyName: "products_services_business_id_fkey"
            columns: ["business_id"]
            isOneToOne: false
            referencedRelation: "business_profiles"
            referencedColumns: ["id"]
          },
        ]
      }
      ratings_reviews: {
        Row: {
          business_profile_id: string
          created_at: string
          id: string
          rating: number
          review_text: string | null
          updated_at: string
          user_id: string
        }
        Insert: {
          business_profile_id: string
          created_at?: string
          id?: string
          rating: number
          review_text?: string | null
          updated_at?: string
          user_id: string
        }
        Update: {
          business_profile_id?: string
          created_at?: string
          id?: string
          rating?: number
          review_text?: string | null
          updated_at?: string
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "ratings_reviews_business_profile_id_fkey"
            columns: ["business_profile_id"]
            isOneToOne: false
            referencedRelation: "business_profiles"
            referencedColumns: ["id"]
          },
        ]
      }
      refresh_token_blacklist: {
        Row: {
          admin_user_id: string | null
          blacklisted_at: string
          created_at: string
          device_id: string
          expires_at: string
          id: string
          reason: string | null
          token_hash: string
          user_id: string
        }
        Insert: {
          admin_user_id?: string | null
          blacklisted_at?: string
          created_at?: string
          device_id: string
          expires_at: string
          id?: string
          reason?: string | null
          token_hash: string
          user_id: string
        }
        Update: {
          admin_user_id?: string | null
          blacklisted_at?: string
          created_at?: string
          device_id?: string
          expires_at?: string
          id?: string
          reason?: string | null
          token_hash?: string
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "fk_refresh_token_device"
            columns: ["device_id"]
            isOneToOne: false
            referencedRelation: "devices"
            referencedColumns: ["device_id"]
          },
        ]
      }
      refresh_tokens: {
        Row: {
          device_id: string
          expires_at: string | null
          id: string
          issued_at: string
          refresh_token_hash: string
          revoked: boolean
          rotated_from: string | null
        }
        Insert: {
          device_id: string
          expires_at?: string | null
          id?: string
          issued_at?: string
          refresh_token_hash: string
          revoked?: boolean
          rotated_from?: string | null
        }
        Update: {
          device_id?: string
          expires_at?: string | null
          id?: string
          issued_at?: string
          refresh_token_hash?: string
          revoked?: boolean
          rotated_from?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "refresh_tokens_device_id_fkey"
            columns: ["device_id"]
            isOneToOne: false
            referencedRelation: "devices"
            referencedColumns: ["device_id"]
          },
        ]
      }
      security_events: {
        Row: {
          created_at: string | null
          details: Json | null
          device_id: string | null
          event_type: string
          id: number
          ip_address: unknown
          request_method: string
          request_path: string
          severity: string
          user_agent: string
          user_id: string | null
        }
        Insert: {
          created_at?: string | null
          details?: Json | null
          device_id?: string | null
          event_type: string
          id?: number
          ip_address: unknown
          request_method: string
          request_path: string
          severity: string
          user_agent: string
          user_id?: string | null
        }
        Update: {
          created_at?: string | null
          details?: Json | null
          device_id?: string | null
          event_type?: string
          id?: number
          ip_address?: unknown
          request_method?: string
          request_path?: string
          severity?: string
          user_agent?: string
          user_id?: string | null
        }
        Relationships: []
      }
      storage_cleanup_config: {
        Row: {
          batch_size: number | null
          created_at: string | null
          id: number
          is_running: boolean | null
          last_processed_offset: number | null
          last_run_at: string | null
          max_batches_per_run: number | null
          total_files_deleted: number | null
          total_users_processed: number | null
          updated_at: string | null
        }
        Insert: {
          batch_size?: number | null
          created_at?: string | null
          id?: number
          is_running?: boolean | null
          last_processed_offset?: number | null
          last_run_at?: string | null
          max_batches_per_run?: number | null
          total_files_deleted?: number | null
          total_users_processed?: number | null
          updated_at?: string | null
        }
        Update: {
          batch_size?: number | null
          created_at?: string | null
          id?: number
          is_running?: boolean | null
          last_processed_offset?: number | null
          last_run_at?: string | null
          max_batches_per_run?: number | null
          total_files_deleted?: number | null
          total_users_processed?: number | null
          updated_at?: string | null
        }
        Relationships: []
      }
      storage_cleanup_progress: {
        Row: {
          bucket_name: string
          error_message: string | null
          files_deleted: number | null
          id: number
          processed_at: string | null
          run_id: string | null
          status: string
          user_id: string
        }
        Insert: {
          bucket_name: string
          error_message?: string | null
          files_deleted?: number | null
          id?: number
          processed_at?: string | null
          run_id?: string | null
          status: string
          user_id: string
        }
        Update: {
          bucket_name?: string
          error_message?: string | null
          files_deleted?: number | null
          id?: number
          processed_at?: string | null
          run_id?: string | null
          status?: string
          user_id?: string
        }
        Relationships: []
      }
      subscriptions: {
        Row: {
          business_profile_id: string
          created_at: string
          id: string
          user_id: string
        }
        Insert: {
          business_profile_id: string
          created_at?: string
          id?: string
          user_id: string
        }
        Update: {
          business_profile_id?: string
          created_at?: string
          id?: string
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "subscriptions_business_profile_id_fkey"
            columns: ["business_profile_id"]
            isOneToOne: false
            referencedRelation: "business_profiles"
            referencedColumns: ["id"]
          },
        ]
      }
      suspicious_activity_alerts: {
        Row: {
          alert_id: string
          alert_type: string
          created_at: string
          details: Json
          device_id: string
          review_reason: string | null
          reviewed_at: string | null
          reviewed_by: string | null
          severity: string
          status: string
          user_id: string
        }
        Insert: {
          alert_id?: string
          alert_type: string
          created_at?: string
          details?: Json
          device_id: string
          review_reason?: string | null
          reviewed_at?: string | null
          reviewed_by?: string | null
          severity: string
          status?: string
          user_id: string
        }
        Update: {
          alert_id?: string
          alert_type?: string
          created_at?: string
          details?: Json
          device_id?: string
          review_reason?: string | null
          reviewed_at?: string | null
          reviewed_by?: string | null
          severity?: string
          status?: string
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "fk_device"
            columns: ["device_id"]
            isOneToOne: false
            referencedRelation: "devices"
            referencedColumns: ["device_id"]
          },
        ]
      }
    }
    Views: {
      ad_targets_view: {
        Row: {
          ad_created_at: string | null
          ad_id: string | null
          ad_image_url: string | null
          ad_link_url: string | null
          expiry_date: string | null
          expiry_status: string | null
          is_active: boolean | null
          is_global: boolean | null
          pincode: string | null
          target_created_at: string | null
          target_id: string | null
        }
        Relationships: []
      }
      customer_profiles_public: {
        Row: {
          avatar_url: string | null
          city: string | null
          city_slug: string | null
          created_at: string | null
          id: string | null
          locality: string | null
          locality_slug: string | null
          name: string | null
          state: string | null
          state_slug: string | null
          updated_at: string | null
        }
        Insert: {
          avatar_url?: string | null
          city?: string | null
          city_slug?: string | null
          created_at?: string | null
          id?: string | null
          locality?: string | null
          locality_slug?: string | null
          name?: string | null
          state?: string | null
          state_slug?: string | null
          updated_at?: string | null
        }
        Update: {
          avatar_url?: string | null
          city?: string | null
          city_slug?: string | null
          created_at?: string | null
          id?: string | null
          locality?: string | null
          locality_slug?: string | null
          name?: string | null
          state?: string | null
          state_slug?: string | null
          updated_at?: string | null
        }
        Relationships: []
      }
      expired_ads_view: {
        Row: {
          ad_id: string | null
          ad_image_url: string | null
          ad_link_url: string | null
          created_at: string | null
          expired_for: unknown | null
          expiry_date: string | null
          is_active: boolean | null
          status: string | null
        }
        Insert: {
          ad_id?: string | null
          ad_image_url?: string | null
          ad_link_url?: string | null
          created_at?: string | null
          expired_for?: never
          expiry_date?: string | null
          is_active?: boolean | null
          status?: never
        }
        Update: {
          ad_id?: string | null
          ad_image_url?: string | null
          ad_link_url?: string | null
          created_at?: string | null
          expired_for?: never
          expiry_date?: string | null
          is_active?: boolean | null
          status?: never
        }
        Relationships: []
      }
      unified_posts: {
        Row: {
          author_address_line: string | null
          author_avatar_url: string | null
          author_average_rating: number | null
          author_category: string | null
          author_city: string | null
          author_id: string | null
          author_locality: string | null
          author_member_name: string | null
          author_name: string | null
          author_slug: string | null
          author_state: string | null
          author_status: string | null
          author_total_likes: number | null
          author_total_subscriptions: number | null
          city_slug: string | null
          content: string | null
          created_at: string | null
          id: string | null
          image_url: string | null
          locality_slug: string | null
          mentioned_business_ids: string[] | null
          pincode: string | null
          post_source: string | null
          product_ids: string[] | null
          state_slug: string | null
          total_comments: number | null
          total_likes: number | null
          updated_at: string | null
        }
        Relationships: []
      }
    }
    Functions: {
      add_custom_ad: {
        Args: {
          p_ad_image_url: string
          p_ad_link_url: string
          p_is_active: boolean
          p_is_global: boolean
          p_expiry_date?: string
          p_pincodes?: string[]
        }
        Returns: string
      }
      cleanup_expired_ads: {
        Args: Record<PropertyKey, never>
        Returns: number
      }
      cleanup_expired_blacklist_tokens: {
        Args: Record<PropertyKey, never>
        Returns: undefined
      }
      cleanup_orphaned_storage: {
        Args: Record<PropertyKey, never>
        Returns: {
          bucket_name: string
          user_id: string
          folder_path: string
          files_deleted: number
          status: string
        }[]
      }
      cleanup_orphaned_storage_batch: {
        Args: { batch_size?: number; max_batches?: number }
        Returns: {
          run_id: string
          batch_number: number
          users_processed: number
          files_deleted: number
          status: string
          message: string
        }[]
      }
      cleanup_orphaned_storage_scalable: {
        Args: Record<PropertyKey, never>
        Returns: string
      }
      cleanup_orphaned_storage_summary: {
        Args: Record<PropertyKey, never>
        Returns: string
      }
      delete_custom_ad: {
        Args: { p_ad_id: string }
        Returns: boolean
      }
      find_closest_locality: {
        Args: { user_lat: number; user_lng: number; max_distance_km?: number }
        Returns: {
          pincode: string
          office_name: string
          division_name: string
          state_name: string
          distance_km: number
        }[]
      }
      generate_slug: {
        Args: { input_text: string }
        Returns: string
      }
      generate_variant_combinations: {
        Args: { variant_types_values: Json }
        Returns: {
          combination: Json
          combination_name: string
        }[]
      }
      get_ad_for_pincode: {
        Args: { target_pincode: string }
        Returns: {
          id: string
          ad_image_url: string
          ad_link_url: string
          is_global: boolean
          expiry_date: string
        }[]
      }
      get_all_ads_for_pincode: {
        Args: { target_pincode: string }
        Returns: {
          id: string
          ad_image_url: string
          ad_link_url: string
          is_global: boolean
          expiry_date: string
          is_active: boolean
          created_at: string
          status: string
        }[]
      }
      get_available_product_variants: {
        Args: { product_uuid: string }
        Returns: {
          id: string
          variant_name: string
          variant_values: Json
          base_price: number
          discounted_price: number
          images: string[]
          featured_image_index: number
        }[]
      }
      get_business_variant_stats: {
        Args: { business_uuid: string }
        Returns: {
          total_products: number
          products_with_variants: number
          total_variants: number
          available_variants: number
        }[]
      }
      get_columns: {
        Args: { table_name: string }
        Returns: {
          column_name: string
          data_type: string
        }[]
      }
      get_custom_ads_stats: {
        Args: Record<PropertyKey, never>
        Returns: {
          total_ads: number
          active_ads: number
          expired_ads: number
          global_ads: number
          pincode_targeted_ads: number
          unique_pincodes_targeted: number
          expiring_soon_ads: number
        }[]
      }
      get_distinct_cities: {
        Args: { search_query: string; result_limit: number }
        Returns: {
          city: string
        }[]
      }
      get_most_targeted_pincodes: {
        Args: { limit_count?: number }
        Returns: {
          pincode: string
          ad_count: number
        }[]
      }
      get_product_with_variants: {
        Args: { product_uuid: string }
        Returns: {
          product_id: string
          product_name: string
          product_description: string
          product_base_price: number
          product_discounted_price: number
          product_is_available: boolean
          product_images: string[]
          product_featured_image_index: number
          variant_count: number
          variants: Json
        }[]
      }
      get_tables: {
        Args: Record<PropertyKey, never>
        Returns: {
          table_name: string
        }[]
      }
      increment: {
        Args: { row_id: string; table_name: string; column_name: string }
        Returns: number
      }
      is_variant_combination_unique: {
        Args: {
          product_uuid: string
          variant_vals: Json
          exclude_variant_id?: string
        }
        Returns: boolean
      }
      jsonb_object_keys_count: {
        Args: { obj: Json }
        Returns: number
      }
      reset_all_scalable_offsets: {
        Args: Record<PropertyKey, never>
        Returns: string
      }
      test_existing_triggers: {
        Args: Record<PropertyKey, never>
        Returns: {
          trigger_name: string
          table_name: string
          function_name: string
          status: string
        }[]
      }
      update_custom_ad: {
        Args: {
          p_ad_id: string
          p_ad_image_url?: string
          p_ad_link_url?: string
          p_is_active?: boolean
          p_expiry_date?: string
          p_is_global?: boolean
          p_pincodes?: string[]
        }
        Returns: boolean
      }
    }
    Enums: {
      payment_status_enum: "pending" | "paid" | "failed" | "refunded"
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}

type DatabaseWithoutInternals = Omit<Database, "__InternalSupabase">

type DefaultSchema = DatabaseWithoutInternals[Extract<keyof Database, "public">]

export type Tables<
  DefaultSchemaTableNameOrOptions extends
    | keyof (DefaultSchema["Tables"] & DefaultSchema["Views"])
    | { schema: keyof DatabaseWithoutInternals },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof (DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
        DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Views"])
    : never = never,
> = DefaultSchemaTableNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? (DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
      DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Views"])[TableName] extends {
      Row: infer R
    }
    ? R
    : never
  : DefaultSchemaTableNameOrOptions extends keyof (DefaultSchema["Tables"] &
        DefaultSchema["Views"])
    ? (DefaultSchema["Tables"] &
        DefaultSchema["Views"])[DefaultSchemaTableNameOrOptions] extends {
        Row: infer R
      }
      ? R
      : never
    : never

export type TablesInsert<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof DatabaseWithoutInternals },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Insert: infer I
    }
    ? I
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Insert: infer I
      }
      ? I
      : never
    : never

export type TablesUpdate<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof DatabaseWithoutInternals },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Update: infer U
    }
    ? U
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Update: infer U
      }
      ? U
      : never
    : never

export type Enums<
  DefaultSchemaEnumNameOrOptions extends
    | keyof DefaultSchema["Enums"]
    | { schema: keyof DatabaseWithoutInternals },
  EnumName extends DefaultSchemaEnumNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof DatabaseWithoutInternals[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"]
    : never = never,
> = DefaultSchemaEnumNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? DatabaseWithoutInternals[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"][EnumName]
  : DefaultSchemaEnumNameOrOptions extends keyof DefaultSchema["Enums"]
    ? DefaultSchema["Enums"][DefaultSchemaEnumNameOrOptions]
    : never

export type CompositeTypes<
  PublicCompositeTypeNameOrOptions extends
    | keyof DefaultSchema["CompositeTypes"]
    | { schema: keyof DatabaseWithoutInternals },
  CompositeTypeName extends PublicCompositeTypeNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof DatabaseWithoutInternals[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"]
    : never = never,
> = PublicCompositeTypeNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? DatabaseWithoutInternals[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"][CompositeTypeName]
  : PublicCompositeTypeNameOrOptions extends keyof DefaultSchema["CompositeTypes"]
    ? DefaultSchema["CompositeTypes"][PublicCompositeTypeNameOrOptions]
    : never

export const Constants = {
  public: {
    Enums: {
      payment_status_enum: ["pending", "paid", "failed", "refunded"],
    },
  },
} as const
