import { NextRequest, NextResponse } from "next/server";
import {
  getAuth<PERSON>ontext,
  AuthContext,
  isAuthenticated,
} from "@/lib/auth/getAuthContext";
import { verifyHMACMiddleware } from "@/lib/middleware/hmac";
import {
  bruteForceProtectionMiddleware,
  getClientIP,
  BruteForceContext,
} from "@/lib/middleware/bruteForceProtection";
import { csrfMiddleware } from "@/lib/security/csrf";

export interface RouteHandler {
  (
    _req: NextRequest,
    _context: AuthContext,
    ..._args: any[]
  ): Promise<NextResponse>;
}

export interface ProtectedRouteOptions {
  requireHMAC?: boolean; // For mobile clients
  requireCSRF?: boolean; // For web mutation endpoints
  operation?: BruteForceContext["operation"]; // For rate limiting
}

export interface PublicRouteOptions {
  allowOptionalAuth?: boolean; // Allow optional JWT for personalization
  operation?: BruteForceContext["operation"]; // For rate limiting
}

/**
 * Wrapper for protected routes that require authentication
 * Handles both web (session) and mobile (JWT + optional HMAC) clients
 */
export function withProtectedRoute(
  handler: RouteHandler,
  options: ProtectedRouteOptions = {}
) {
  return async (req: NextRequest, ...args: any[]) => {
    try {
      // Apply rate limiting and brute force protection
      const ipAddress = getClientIP(req);
      const bruteForceCheck = await bruteForceProtectionMiddleware(req, {
        operation: options.operation || "business_api",
        ipAddress,
      });

      if (bruteForceCheck) {
        return bruteForceCheck;
      }

      // Get authentication context
      const context = await getAuthContext(req);

      // Check if user is authenticated
      if (!isAuthenticated(context)) {
        // Handle JWT errors for web clients
        if (context.isWeb && context.shouldLogout) {
          return new NextResponse(
            JSON.stringify({
              error: "Authentication required",
              message: "Session expired",
            }),
            {
              status: 401,
              headers: {
                "Content-Type": "application/json",
                "x-jwt-invalid": "true",
                "x-should-logout": "true",
              },
            }
          );
        }

        return new NextResponse(
          JSON.stringify({
            error: "Authentication required",
            message: "Please log in to access this resource",
          }),
          {
            status: 401,
            headers: { "Content-Type": "application/json" },
          }
        );
      }

      // For mobile clients, verify HMAC if required
      if (context.isMobile && options.requireHMAC) {
        const hmacResult = await verifyHMACMiddleware(req, true);
        if (!hmacResult.success) {
          return new NextResponse(
            JSON.stringify({
              error: "Invalid signature",
              message: hmacResult.error,
            }),
            {
              status: hmacResult.status || 403,
              headers: { "Content-Type": "application/json" },
            }
          );
        }
      }

      // For web mutation endpoints, verify CSRF if required
      if (
        context.isWeb &&
        options.requireCSRF &&
        isMutationMethod(req.method)
      ) {
        const csrfResult = await csrfMiddleware(
          req,
          context.userId || undefined
        );
        if (!csrfResult.success) {
          return new NextResponse(
            JSON.stringify({
              error: "Invalid CSRF token",
              message: csrfResult.error || "Request validation failed",
            }),
            {
              status: 403,
              headers: { "Content-Type": "application/json" },
            }
          );
        }
      }

      // Call the actual route handler with context
      return await handler(req, context, ...args);
    } catch (error) {
      console.error("Protected route error:", error);
      return new NextResponse(
        JSON.stringify({
          error: "Internal server error",
          message: "An unexpected error occurred",
        }),
        {
          status: 500,
          headers: { "Content-Type": "application/json" },
        }
      );
    }
  };
}

/**
 * Wrapper for public routes that don't require authentication
 * Supports optional authentication for personalization
 */
export function withPublicRoute(
  handler: RouteHandler,
  options: PublicRouteOptions = {}
) {
  return async (req: NextRequest, ...args: any[]) => {
    try {
      // Apply rate limiting and brute force protection
      const ipAddress = getClientIP(req);
      const bruteForceCheck = await bruteForceProtectionMiddleware(req, {
        operation: options.operation || "business_api",
        ipAddress,
      });

      if (bruteForceCheck) {
        return bruteForceCheck;
      }

      // Get authentication context (optional for public routes)
      let context: AuthContext;

      if (options.allowOptionalAuth) {
        context = await getAuthContext(req);

        // For public routes with optional auth, don't block on auth failures
        // Just proceed without authentication
        if (!context.isAuthenticated && context.shouldLogout) {
          // Still signal logout for web clients with invalid JWT
          const response = await handler(req, context, ...args);
          response.headers.set("x-jwt-invalid", "true");
          response.headers.set("x-should-logout", "true");
          return response;
        }
      } else {
        // Create unauthenticated context for purely public routes
        context = {
          user: null,
          userId: null,
          isAuthenticated: false,
          isWeb: true, // Default assumption for public routes
          isMobile: false,
          supabase: null,
        };
      }

      // Call the actual route handler with context
      return await handler(req, context, ...args);
    } catch (error) {
      console.error("Public route error:", error);
      return new NextResponse(
        JSON.stringify({
          error: "Internal server error",
          message: "An unexpected error occurred",
        }),
        {
          status: 500,
          headers: { "Content-Type": "application/json" },
        }
      );
    }
  };
}

/**
 * Check if HTTP method is a mutation (requires CSRF for web)
 */
function isMutationMethod(method: string): boolean {
  return ["POST", "PUT", "PATCH", "DELETE"].includes(method.toUpperCase());
}

/**
 * Helper to create consistent error responses
 */
export function createErrorResponse(
  message: string,
  status: number = 400,
  code?: string,
  headers: Record<string, string> = {}
): NextResponse {
  return new NextResponse(
    JSON.stringify({
      error: true,
      code: code || "REQUEST_ERROR",
      message,
    }),
    {
      status,
      headers: {
        "Content-Type": "application/json",
        ...headers,
      },
    }
  );
}

/**
 * Helper to create success responses
 */
export function createSuccessResponse(
  data: any,
  status: number = 200,
  headers: Record<string, string> = {}
): NextResponse {
  return new NextResponse(JSON.stringify(data), {
    status,
    headers: {
      "Content-Type": "application/json",
      ...headers,
    },
  });
}
