import { 
  generateAccessToken, 
  generateRefreshToken, 
  verifyAccessToken, 
  extractBearerToken,
  ACCESS_TOKEN_DURATION 
} from '@/lib/auth/jwt';
import jwt from 'jsonwebtoken';

// Mock jwt for controlled testing
jest.mock('jsonwebtoken');

process.env.JWT_SECRET = 'test-secret';

describe('JWT utilities', () => {
  const mockJwt = jwt as jest.Mocked<typeof jwt>;
  
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('generateAccessToken', () => {
    it('should generate a JWT token with correct payload and options', () => {
      const mockToken = 'mock.jwt.token';
      mockJwt.sign.mockImplementation(() => mockToken);

      const userId = 'test-user-id';
      const roles = ['admin'];
      
      const result = generateAccessToken(userId, roles);

      expect(result).toBe(mockToken);
      expect(mockJwt.sign).toHaveBeenCalledWith(
        {
          user_id: userId,
          sub: userId,
          roles: roles,
        },
        expect.any(String), // JWT secret
        {
          expiresIn: ACCESS_TOKEN_DURATION,
          issuer: 'dukancard-api',
        }
      );
    });

    it('should generate token with empty roles array by default', () => {
      const mockToken = 'mock.jwt.token';
      mockJwt.sign.mockImplementation(() => mockToken);

      const userId = 'test-user-id';
      
      const result = generateAccessToken(userId);

      expect(result).toBe(mockToken);
      expect(mockJwt.sign).toHaveBeenCalledWith(
        {
          user_id: userId,
          sub: userId,
          roles: [],
        },
        expect.any(String), // JWT secret
        {
          expiresIn: ACCESS_TOKEN_DURATION,
          issuer: 'dukancard-api',
        }
      );
    });
  });

  describe('generateRefreshToken', () => {
    it('should generate a 64-character hex string', () => {
      // Mock crypto.randomBytes
      const mockRandomBytes = jest.fn().mockReturnValue({
        toString: jest.fn().mockReturnValue('a'.repeat(64))
      });
      
      jest.doMock('crypto', () => ({
        randomBytes: mockRandomBytes,
      }));

      const result = generateRefreshToken();
      
      expect(result).toHaveLength(64);
      expect(typeof result).toBe('string');
    });

    it('should generate different tokens on subsequent calls', () => {
      const token1 = generateRefreshToken();
      const token2 = generateRefreshToken();
      
      expect(token1).not.toBe(token2);
    });
  });

  describe('verifyAccessToken', () => {
    it('should return decoded payload for valid token', () => {
      const mockPayload = {
        user_id: 'test-user-id',
        roles: ['admin'],
        iat: 1234567890,
        exp: 1234567890 + 900, // 15 minutes later
      };
      
      mockJwt.verify.mockImplementation(() => mockPayload);

      const result = verifyAccessToken('valid.jwt.token');

      expect(result).toEqual(mockPayload);
      expect(mockJwt.verify).toHaveBeenCalledWith(
        'valid.jwt.token',
        expect.any(String) // JWT secret
      );
    });

    it('should return null for invalid token', () => {
      mockJwt.verify.mockImplementation(() => {
        throw new Error('Invalid token');
      });

      const result = verifyAccessToken('invalid.jwt.token');

      expect(result).toBeNull();
    });

    it('should return null for expired token', () => {
      mockJwt.verify.mockImplementation(() => {
        const error = new Error('Token expired');
        error.name = 'TokenExpiredError';
        throw error;
      });

      const result = verifyAccessToken('expired.jwt.token');

      expect(result).toBeNull();
    });
  });

  describe('extractBearerToken', () => {
    it('should extract token from valid Bearer header', () => {
      const token = 'valid.jwt.token';
      const authHeader = `Bearer ${token}`;
      
      const result = extractBearerToken(authHeader);
      
      expect(result).toBe(token);
    });

    it('should return null for missing header', () => {
      const result = extractBearerToken(undefined);
      
      expect(result).toBeNull();
    });

    it('should return null for header without Bearer prefix', () => {
      const result = extractBearerToken('valid.jwt.token');
      
      expect(result).toBeNull();
    });

    it('should return null for empty header', () => {
      const result = extractBearerToken('');
      
      expect(result).toBeNull();
    });

    it('should return null for Bearer header without token', () => {
      const result = extractBearerToken('Bearer ');
      
      expect(result).toBe('');
    });

    it('should handle Bearer header with extra spaces', () => {
      const token = 'valid.jwt.token';
      const authHeader = `Bearer   ${token}`;
      
      const result = extractBearerToken(authHeader);
      
      expect(result).toBe(`  ${token}`);
    });
  });
});