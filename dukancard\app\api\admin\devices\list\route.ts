import { NextRequest } from 'next/server';
import { createServiceRoleClient } from '@/utils/supabase/service-role';
import { requireAdmin } from '@/lib/utils/adminAuth';

export async function GET(request: NextRequest) {
  // Verify admin access
  const adminUser = await requireAdmin(request);
  if (adminUser instanceof Response) {
    return adminUser; // Return error response if not admin
  }

  const { searchParams } = new URL(request.url);
  const page = parseInt(searchParams.get('page') || '1');
  const limit = parseInt(searchParams.get('limit') || '20');
  const platform = searchParams.get('platform');
  const status = searchParams.get('status');
  const search = searchParams.get('search');

  const offset = (page - 1) * limit;

  try {
    const supabase = createServiceRoleClient();
    
    // Build the query
    let query = supabase
      .from('devices')
      .select(`
        device_id,
        user_id,
        device_name,
        platform,
        app_version,
        created_at,
        last_seen_at,
        revoked,
        risk_score
      `)
      .order('created_at', { ascending: false });

    // Apply filters
    if (platform && platform !== 'all') {
      query = query.eq('platform', platform);
    }

    if (status === 'active') {
      query = query.eq('revoked', false);
    } else if (status === 'revoked') {
      query = query.eq('revoked', true);
    }

    if (search) {
      query = query.ilike('device_name', `%${search}%`);
    }

    // Get total count for pagination
    const { count } = await query.select('*', { count: 'exact', head: true });

    // Get paginated results
    const { data: devices, error } = await query
      .range(offset, offset + limit - 1);

    if (error) {
      console.error('Error fetching devices:', error);
      return Response.json({ error: 'Failed to fetch devices' }, { status: 500 });
    }

    // Get user emails for devices (separate query to avoid join issues)
    const userIds = devices?.map(device => device.user_id) || [];
    const { data: users } = await supabase.auth.admin.listUsers();
    const userEmailMap = new Map(
      users.users?.map(user => [user.id, user.email]) || []
    );

    // Format the response
    const formattedDevices = devices?.map(device => ({
      device_id: device.device_id,
      user_id: device.user_id,
      user_email: userEmailMap.get(device.user_id) || 'Unknown',
      device_name: device.device_name,
      platform: device.platform,
      app_version: device.app_version,
      registered_at: device.created_at,
      last_activity: device.last_seen_at,
      is_revoked: device.revoked,
      risk_score: device.risk_score
    })) || [];

    return Response.json({
      devices: formattedDevices,
      pagination: {
        page,
        limit,
        total: count || 0,
        totalPages: Math.ceil((count || 0) / limit)
      },
      filters: {
        platform,
        status,
        search
      }
    });

  } catch (error) {
    console.error('Error in device list API:', error);
    return Response.json({ error: 'Internal server error' }, { status: 500 });
  }
}