import { POST } from '@/app/api/admin/devices/revoke/route';
import { NextRequest } from 'next/server';
import { requireAdmin } from '@/lib/utils/adminAuth';
import { createServiceRoleClient } from '@/utils/supabase/service-role';
import { tokenService } from '@/lib/services/tokenService';

// Mock dependencies
jest.mock('@/lib/utils/adminAuth');
jest.mock('@/utils/supabase/service-role');
jest.mock('@/lib/services/tokenService');

const mockRequireAdmin = requireAdmin as jest.MockedFunction<typeof requireAdmin>;
const mockCreateServiceRoleClient = createServiceRoleClient as jest.MockedFunction<typeof createServiceRoleClient>;
const mockTokenService = tokenService as jest.Mocked<typeof tokenService>;

describe('/api/admin/devices/revoke', () => {
  let mockSupabase: any;

  beforeEach(() => {
    jest.clearAllMocks();
    
    mockSupabase = {
      from: jest.fn().mockReturnThis(),
      update: jest.fn().mockReturnThis(),
      select: jest.fn().mockReturnThis(),
      insert: jest.fn().mockReturnThis(),
      eq: jest.fn().mockReturnThis(),
      single: jest.fn()
    };
    
    mockCreateServiceRoleClient.mockReturnValue(mockSupabase);
    mockRequireAdmin.mockResolvedValue({ id: 'admin-123', email: '<EMAIL>' } as any);
  });

  it('should revoke single device successfully', async () => {
    const deviceInfo = {
      device_id: 'device-1',
      user_id: 'user-1',
      device_name: 'Test Device',
      platform: 'ios',
      users: { email: '<EMAIL>' }
    };

    mockSupabase.update.mockResolvedValueOnce({ error: null });
    mockSupabase.single.mockResolvedValueOnce({ data: deviceInfo, error: null });
    mockSupabase.insert.mockResolvedValueOnce({ error: null });
    mockTokenService.blacklistDeviceTokens.mockResolvedValueOnce(undefined);

    const request = new NextRequest('http://localhost/api/admin/devices/revoke', {
      method: 'POST',
      body: JSON.stringify({
        device_ids: ['device-1'],
        reason: 'Security concern'
      })
    });

    const response = await POST(request);
    const data = await response.json();

    expect(response.status).toBe(200);
    expect(data.success).toBe(true);
    expect(data.revoked).toHaveLength(1);
    expect(data.revoked[0]).toMatchObject({
      device_id: 'device-1',
      status: 'revoked'
    });
    expect(data.summary.successfully_revoked).toBe(1);
  });

  it('should revoke multiple devices', async () => {
    const deviceInfo = {
      device_id: 'device-1',
      user_id: 'user-1',
      device_name: 'Test Device',
      platform: 'ios',
      users: { email: '<EMAIL>' }
    };

    mockSupabase.update.mockResolvedValue({ error: null });
    mockSupabase.single.mockResolvedValue({ data: deviceInfo, error: null });
    mockSupabase.insert.mockResolvedValue({ error: null });
    mockTokenService.blacklistDeviceTokens.mockResolvedValue(undefined);

    const request = new NextRequest('http://localhost/api/admin/devices/revoke', {
      method: 'POST',
      body: JSON.stringify({
        device_ids: ['device-1', 'device-2'],
        reason: 'Bulk revocation'
      })
    });

    const response = await POST(request);
    const data = await response.json();

    expect(response.status).toBe(200);
    expect(data.revoked).toHaveLength(2);
    expect(data.summary.successfully_revoked).toBe(2);
  });

  it('should handle partial failures', async () => {
    mockSupabase.update
      .mockResolvedValueOnce({ error: null }) // First device succeeds
      .mockResolvedValueOnce({ error: { message: 'Database error' } }); // Second device fails

    const deviceInfo = {
      device_id: 'device-1',
      user_id: 'user-1',
      device_name: 'Test Device',
      platform: 'ios',
      users: { email: '<EMAIL>' }
    };

    mockSupabase.single.mockResolvedValueOnce({ data: deviceInfo, error: null });
    mockSupabase.insert.mockResolvedValueOnce({ error: null });
    mockTokenService.blacklistDeviceTokens.mockResolvedValueOnce(undefined);

    const request = new NextRequest('http://localhost/api/admin/devices/revoke', {
      method: 'POST',
      body: JSON.stringify({
        device_ids: ['device-1', 'device-2'],
        reason: 'Test revocation'
      })
    });

    const response = await POST(request);
    const data = await response.json();

    expect(response.status).toBe(200);
    expect(data.revoked).toHaveLength(1);
    expect(data.errors).toHaveLength(1);
    expect(data.summary.successfully_revoked).toBe(1);
    expect(data.summary.failed).toBe(1);
  });

  it('should return 400 for invalid request body', async () => {
    const request = new NextRequest('http://localhost/api/admin/devices/revoke', {
      method: 'POST',
      body: JSON.stringify({})
    });

    const response = await POST(request);
    
    expect(response.status).toBe(400);
    const data = await response.json();
    expect(data.error).toBe('device_ids array is required');
  });

  it('should return 403 if user is not admin', async () => {
    mockRequireAdmin.mockResolvedValueOnce(Response.json({ error: 'Admin access required' }, { status: 403 }));

    const request = new NextRequest('http://localhost/api/admin/devices/revoke', {
      method: 'POST',
      body: JSON.stringify({ device_ids: ['device-1'] })
    });
    
    const response = await POST(request);
    expect(response.status).toBe(403);
  });

  it('should continue revocation even if token blacklisting fails', async () => {
    const deviceInfo = {
      device_id: 'device-1',
      user_id: 'user-1',
      device_name: 'Test Device',
      platform: 'ios',
      users: { email: '<EMAIL>' }
    };

    mockSupabase.update.mockResolvedValueOnce({ error: null });
    mockSupabase.single.mockResolvedValueOnce({ data: deviceInfo, error: null });
    mockSupabase.insert.mockResolvedValueOnce({ error: null });
    mockTokenService.blacklistDeviceTokens.mockRejectedValueOnce(new Error('Token service error'));

    const request = new NextRequest('http://localhost/api/admin/devices/revoke', {
      method: 'POST',
      body: JSON.stringify({
        device_ids: ['device-1'],
        reason: 'Test revocation'
      })
    });

    const response = await POST(request);
    const data = await response.json();

    expect(response.status).toBe(200);
    expect(data.success).toBe(true);
    expect(data.revoked).toHaveLength(1);
  });
});