# Technical Assumptions

## Repository Structure
- **Type**: Polyrepo - maintaining separate dukancard/ (Next.js web) and dukancard-app/ (React Native mobile) repositories
- **Shared Components**: API type definitions, utilities, and constants shared between repositories
- **Backend Services**: Centralized business logic in backend/supabase/services/ directory structure

## Service Architecture  
- **API Layer**: Next.js API routes serving as unified backend for both web and mobile applications
- **Database**: Supabase PostgreSQL with Row Level Security (RLS) maintained as backup protection layer
- **Authentication**: Supabase Auth enhanced with HMAC device authentication for mobile applications
- **Storage**: Supabase Storage accessed through API routes with proper access controls and validation

## Testing Requirements
- **Unit Testing**: Jest with jsdom for web components, React Native preset for mobile components
- **Integration Testing**: API route testing with mock database scenarios and authentication flows  
- **End-to-End Testing**: Detox for mobile E2E testing, Playwright for web E2E testing
- **Performance Testing**: Load testing for API endpoints, response time monitoring, and cache effectiveness validation

## Additional Technical Assumptions
- Current device registration system is stable and secure for HMAC implementation
- Existing Zustand stores can be enhanced for API caching without major architectural changes
- Supabase RLS policies are correctly configured and will remain as backup security layer
- Development team has sufficient API development experience for complex authentication flows
- Mobile app users will accept minor performance impact from API layer introduction
