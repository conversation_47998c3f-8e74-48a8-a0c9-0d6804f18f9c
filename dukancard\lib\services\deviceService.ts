/**
 * Device Management Service for Next.js
 * Handles device registration, lookup, and validation operations
 * Used by API routes and other backend services
 */

import { Database } from '@/types/supabase';
import { createServiceRoleClient } from '@/utils/supabase/service-role';
import { hashSecret, compareSecret } from '@/lib/security/hashing';
import crypto from 'crypto';

export type DeviceRow = Database['public']['Tables']['devices']['Row'];
export type DeviceInsert = Database['public']['Tables']['devices']['Insert'];

export interface DeviceRegistrationData {
  userId: string;
  deviceName: string;
  platform: 'ios' | 'android' | 'web';
  appVersion: string;
  appSignatureHash?: string;
}

export interface DeviceCredentials {
  deviceId: string;
  deviceSecret: string;
  hmacKey: string;
}

export interface DeviceInfo {
  deviceId: string;
  userId: string;
  deviceName: string;
  platform: string;
  appVersion: string;
  isRevoked: boolean;
  registeredAt: string;
  lastActivity: string | null;
}

/**
 * Register a new device and generate credentials
 */
export async function registerDevice(data: DeviceRegistrationData): Promise<DeviceCredentials> {
  const supabase = createServiceRoleClient();
  
  // Generate device secret and HMAC key
  const deviceSecret = crypto.randomBytes(32).toString("hex");
  const hmacKey = crypto.randomBytes(32).toString("hex");
  
  // Hash the device secret for storage
  const deviceSecretHash = await hashSecret(deviceSecret);
  
  // Insert device record
  const { data: deviceData, error } = await supabase
    .from("devices")
    .insert({
      user_id: data.userId,
      device_name: data.deviceName,
      platform: data.platform,
      app_version: data.appVersion,
      device_secret_hash: deviceSecretHash,
      hmac_key_hash: hmacKey,
      app_signature_hash: data.appSignatureHash || null,
    })
    .select("device_id")
    .single();

  if (error) {
    console.error("Database error during device registration:", error);
    throw new Error("Failed to register device");
  }

  return {
    deviceId: deviceData.device_id,
    deviceSecret,
    hmacKey,
  };
}

/**
 * Look up device by device ID
 */
export async function getDeviceById(deviceId: string): Promise<DeviceRow | null> {
  const supabase = createServiceRoleClient();
  
  const { data, error } = await supabase
    .from("devices")
    .select("*")
    .eq("device_id", deviceId)
    .single();

  if (error) {
    if (error.code === 'PGRST116') {
      // No rows returned
      return null;
    }
    console.error("Error fetching device:", error);
    throw new Error("Failed to fetch device");
  }

  return data;
}

/**
 * Get all devices for a user
 */
export async function getUserDevices(userId: string): Promise<DeviceInfo[]> {
  const supabase = createServiceRoleClient();
  
  const { data, error } = await supabase
    .from("devices")
    .select("device_id, user_id, device_name, platform, app_version, revoked, created_at, last_seen_at")
    .eq("user_id", userId)
    .order("created_at", { ascending: false });

  if (error) {
    console.error("Error fetching user devices:", error);
    throw new Error("Failed to fetch user devices");
  }

  return (data || []).map(device => ({
    deviceId: device.device_id,
    userId: device.user_id,
    deviceName: device.device_name || 'Unknown Device',
    platform: device.platform || 'unknown',
    appVersion: device.app_version || 'unknown',
    isRevoked: device.revoked,
    registeredAt: device.created_at,
    lastActivity: device.last_seen_at,
  }));
}

/**
 * Validate device credentials
 */
export async function validateDeviceCredentials(
  deviceId: string,
  deviceSecret: string
): Promise<boolean> {
  const device = await getDeviceById(deviceId);
  
  if (!device || device.revoked) {
    return false;
  }

  return await compareSecret(deviceSecret, device.device_secret_hash);
}

/**
 * Get device HMAC key for signature verification
 */
export async function getDeviceHMACKey(deviceId: string): Promise<string | null> {
  const device = await getDeviceById(deviceId);
  
  if (!device || device.revoked) {
    return null;
  }

  return device.hmac_key_hash;
}

/**
 * Update device last activity timestamp
 */
export async function updateDeviceActivity(deviceId: string): Promise<void> {
  const supabase = createServiceRoleClient();
  
  const { error } = await supabase
    .from("devices")
    .update({ last_seen_at: new Date().toISOString() })
    .eq("device_id", deviceId);

  if (error) {
    console.error("Error updating device activity:", error);
    // Don't throw error for activity updates - log and continue
  }
}

/**
 * Revoke a device
 */
export async function revokeDevice(
  deviceId: string,
  reason?: string
): Promise<void> {
  const supabase = createServiceRoleClient();
  
  const { error } = await supabase
    .from("devices")
    .update({
      revoked: true,
      revoked_at: new Date().toISOString(),
      revocation_reason: reason || 'Manually revoked',
    })
    .eq("device_id", deviceId);

  if (error) {
    console.error("Error revoking device:", error);
    throw new Error("Failed to revoke device");
  }
}

/**
 * Check if device exists and is active
 */
export async function isDeviceActive(deviceId: string): Promise<boolean> {
  const device = await getDeviceById(deviceId);
  return device !== null && !device.revoked;
}