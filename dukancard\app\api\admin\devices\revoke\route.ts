import { NextRequest } from 'next/server';
import { createServiceRoleClient } from '@/utils/supabase/service-role';
import { requireAdmin } from '@/lib/utils/adminAuth';
import { tokenService } from '@/lib/services/tokenService';

interface RevokeRequest {
  device_ids: string[];
  reason?: string;
}

export async function POST(request: NextRequest) {
  // Verify admin access
  const adminUser = await requireAdmin(request);
  if (adminUser instanceof Response) {
    return adminUser; // Return error response if not admin
  }

  try {
    const body: RevokeRequest = await request.json();
    const { device_ids, reason } = body;

    if (!device_ids || !Array.isArray(device_ids) || device_ids.length === 0) {
      return Response.json({ error: 'device_ids array is required' }, { status: 400 });
    }

    const supabase = createServiceRoleClient();
    const timestamp = new Date().toISOString();

    // Start a transaction-like operation
    const results = [];
    const errors = [];

    for (const deviceId of device_ids) {
      try {
        // 1. Revoke the device
        const { error: revokeError } = await supabase
          .from('devices')
          .update({
            revoked: true,
            revoked_at: timestamp,
            revocation_reason: reason || 'Admin revocation'
          })
          .eq('device_id', deviceId);

        if (revokeError) {
          errors.push({ device_id: deviceId, error: revokeError.message });
          continue;
        }

        // 2. Get device info for audit log
        const { data: deviceInfo } = await supabase
          .from('devices')
          .select(`
            device_id,
            user_id,
            device_name,
            platform,
            users!inner(email)
          `)
          .eq('device_id', deviceId)
          .single();

        // 3. Create audit log entry
        const { error: auditError } = await supabase
          .from('device_audit_logs')
          .insert({
            admin_user_id: adminUser.id,
            action_type: 'revoke',
            target_device_id: deviceId,
            target_user_id: deviceInfo?.user_id,
            reason: reason || 'Admin revocation',
            metadata: {
              device_name: deviceInfo?.device_name,
              platform: deviceInfo?.platform,
              user_email: deviceInfo?.users?.email,
              ip_address: request.headers.get('x-forwarded-for') || 'unknown'
            },
            timestamp,
            ip_address: request.headers.get('x-forwarded-for') || 'unknown'
          });

        if (auditError) {
          console.error('Audit log error:', auditError);
          // Continue even if audit log fails - revocation is more important
        }

        // 4. Invalidate refresh tokens and JWT sessions
        try {
          await tokenService.blacklistDeviceTokens(
            deviceId, 
            reason || 'Admin revocation', 
            adminUser.id
          );
        } catch (tokenError) {
          console.error(`Token invalidation error for device ${deviceId}:`, tokenError);
          // Continue - device is still revoked even if token cleanup fails
        }
        
        results.push({
          device_id: deviceId,
          status: 'revoked',
          revoked_at: timestamp
        });

      } catch (deviceError) {
        console.error(`Error revoking device ${deviceId}:`, deviceError);
        errors.push({ 
          device_id: deviceId, 
          error: deviceError instanceof Error ? deviceError.message : 'Unknown error'
        });
      }
    }

    // Return response with results and any errors
    const response = {
      success: results.length > 0,
      revoked: results,
      errors: errors.length > 0 ? errors : undefined,
      summary: {
        total_requested: device_ids.length,
        successfully_revoked: results.length,
        failed: errors.length
      }
    };

    const status = errors.length === device_ids.length ? 500 : 200;
    return Response.json(response, { status });

  } catch (error) {
    console.error('Error in device revoke API:', error);
    return Response.json({ error: 'Internal server error' }, { status: 500 });
  }
}