# Target Architecture Design

## 2.1 Unified API-First Architecture

```
Target Unified Architecture:
┌─────────────────────┐    ┌─────────────────────┐
│   Web Application   │    │  Mobile Application │
│     (dukancard/)    │    │   (dukancard-app/)  │
│                     │    │                     │
│ CSRF + JWT Auth ────┼────┼──── HMAC + JWT Auth │
│ Enhanced Zustand    │    │ Enhanced Zustand    │
│ Stores              │    │ Stores              │
└─────────┬───────────┘    └─────────┬───────────┘
          │                          │
          └──────────┬─────────────────┘
                     │
        ┌────────────▼────────────┐
        │   Next.js API Routes    │
        │  (Centralized Layer)    │
        │                         │
        │ • HMAC Middleware       │
        │ • CSRF Protection       │
        │ • Rate Limiting         │
        │ • Business Logic        │
        │ • Validation Layer      │
        │ • Admin Separation      │
        └────────────┬────────────┘
                     │
              ┌──────▼──────┐
              │   Supabase  │
              │ PostgreSQL  │
              │ RLS Backup  │
              └─────────────┘
```

## 2.2 API Route Structure

```
/api/
├── auth/
│   ├── login/           # Unified login endpoint
│   ├── register/        # Account creation
│   ├── refresh/         # Token refresh with HMAC
│   └── logout/          # Session termination
├── user/
│   ├── profile/         # User profile management
│   └── preferences/     # User settings
├── business/
│   ├── profile/         # Business profile CRUD
│   ├── onboarding/      # Multi-step business setup
│   ├── dashboard/       # Analytics and metrics
│   ├── products/        # Product catalog management
│   └── [businessId]/    # Public business data
├── customer/
│   ├── profile/         # Customer profile CRUD
│   ├── feed/           # Personalized content feed
│   └── interactions/    # Likes, follows, reviews
├── posts/
│   ├── business/        # Business content management
│   ├── customer/        # Customer content management
│   └── [postId]/        # Single post operations
├── social/
│   ├── likes/           # Like/unlike operations
│   ├── comments/        # Comment management
│   └── follows/         # Follow/unfollow operations
├── storage/
│   ├── upload/          # Secure file upload
│   └── delete/          # File cleanup
├── discovery/
│   ├── search/          # Business/product search
│   ├── location/        # Location-based discovery
│   └── categories/      # Category filtering
└── admin/
    ├── users/           # User management (service key)
    ├── content/         # Content moderation
    └── analytics/       # Platform metrics
```

## 2.3 Security Architecture

### 2.3.1 Multi-Layer Security Model

```
Security Layer Stack:
┌─────────────────────────────────────────┐
│         Request Authentication          │
│  Web: CSRF + JWT  |  Mobile: HMAC + JWT │
└─────────────────────────────────────────┘
┌─────────────────────────────────────────┐
│            Rate Limiting                │
│     Upstash Redis + Sliding Window      │
└─────────────────────────────────────────┘
┌─────────────────────────────────────────┐
│           Route Protection              │
│    Role-based Access + Admin Separation │
└─────────────────────────────────────────┘
┌─────────────────────────────────────────┐
│          Business Logic Layer          │
│     Validation + Sanitization + Auth   │
└─────────────────────────────────────────┘
┌─────────────────────────────────────────┐
│           Database Security             │
│        Supabase RLS (Backup Layer)      │
└─────────────────────────────────────────┘
```

### 2.3.2 HMAC Authentication Flow

```typescript
// Mobile HMAC Authentication Implementation
interface HMACAuthFlow {
  // 1. Device Registration (One-time)
  deviceRegistration: {
    endpoint: "/api/auth/devices/register";
    method: "POST";
    payload: {
      deviceInfo: DeviceInfo;
      appSignature: string;
      userAgent: string;
    };
    response: {
      deviceId: string;
      deviceSecret: string;
      hmacKey: string;
    };
  };

  // 2. Request Signing (Every API Call)
  requestSigning: {
    components: [
      "HTTP_METHOD",
      "REQUEST_PATH", 
      "TIMESTAMP",
      "REQUEST_BODY_JSON",
      "DEVICE_SECRET"
    ];
    algorithm: "HMAC-SHA256";
    headers: {
      "X-Device-Id": string;
      "X-Timestamp": string;
      "X-Signature": string;
      "Authorization": "Bearer JWT_TOKEN";
    };
  };

  // 3. Server Validation
  serverValidation: {
    timestampWindow: "5 minutes";
    replayProtection: "Redis-based nonce tracking";
    deviceVerification: "Database lookup + status check";
    signatureValidation: "HMAC-SHA256 verification";
  };
}
```

### 2.3.3 Device Management System

```typescript
// Device Management Schema
interface DeviceManagement {
  devices: {
    device_id: string;
    user_id: string;
    device_name: string;
    platform: "ios" | "android";
    app_version: string;
    device_secret_hash: string;
    hmac_key_hash: string;
    registered_at: timestamp;
    last_activity: timestamp;
    is_revoked: boolean;
    revoked_at?: timestamp;
    revocation_reason?: string;
  };

  refresh_tokens: {
    token_id: string;
    device_id: string;
    token_hash: string;
    expires_at: timestamp;
    created_at: timestamp;
  };

  // Admin Operations
  adminOperations: {
    revokeDevice: "/api/admin/devices/revoke";
    listDevices: "/api/admin/devices/list";
    suspiciousActivity: "/api/admin/security/alerts";
    deviceQuarantine: "/api/admin/devices/quarantine";
  };
}
```
