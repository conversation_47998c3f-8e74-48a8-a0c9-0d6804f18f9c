import { NextRequest } from 'next/server';
import { createServiceRoleClient } from '@/utils/supabase/service-role';
import { requireAdmin } from '@/lib/utils/adminAuth';
import { deviceAnalytics } from '@/lib/services/deviceAnalytics';

export async function GET(
  request: NextRequest,
  { params }: { params: { deviceId: string } }
) {
  // Verify admin access
  const adminUser = await requireAdmin(request);
  if (adminUser instanceof Response) {
    return adminUser; // Return error response if not admin
  }

  const { deviceId } = params;

  if (!deviceId) {
    return Response.json({ error: 'Device ID is required' }, { status: 400 });
  }

  try {
    const supabase = createServiceRoleClient();
    
    // Get basic device info
    const { data: device, error: deviceError } = await supabase
      .from('devices')
      .select('*')
      .eq('device_id', deviceId)
      .single();

    if (deviceError || !device) {
      return Response.json({ error: 'Device not found' }, { status: 404 });
    }

    // Get user email
    const { data: userData } = await supabase.auth.admin.getUserById(device.user_id);
    const userEmail = userData.user?.email || 'Unknown';

    // Get device activity timeline
    const timeline = await deviceAnalytics.getDeviceActivityTimeline(deviceId, 30);

    // Mock analytics data (would be real data in production)
    const analytics = {
      totalSessions: Math.floor(Math.random() * 200) + 50,
      avgSessionDuration: `${Math.floor(Math.random() * 45) + 15}m`,
      lastLocation: timeline.locationHistory[0]?.location || 'Unknown',
      flaggedActivities: timeline.events.filter(e => e.event_type === 'alert').length
    };

    // Format response
    const deviceDetails = {
      device_id: device.device_id,
      user_id: device.user_id,
      user_email: userEmail,
      device_name: device.device_name,
      platform: device.platform,
      app_version: device.app_version,
      registered_at: device.created_at,
      last_activity: device.last_seen_at,
      is_revoked: device.revoked,
      is_quarantined: device.is_quarantined,
      risk_score: device.risk_score,
      events: timeline.events,
      riskHistory: timeline.riskHistory,
      locationHistory: timeline.locationHistory,
      analytics
    };

    return Response.json(deviceDetails);

  } catch (error) {
    console.error('Error fetching device details:', error);
    return Response.json({ error: 'Internal server error' }, { status: 500 });
  }
}