import { GET } from '@/app/api/admin/devices/list/route';
import { NextRequest } from 'next/server';
import { requireAdmin } from '@/lib/utils/adminAuth';
import { createServiceRoleClient } from '@/utils/supabase/service-role';

// Mock dependencies
jest.mock('@/lib/utils/adminAuth');
jest.mock('@/utils/supabase/service-role');

const mockRequireAdmin = requireAdmin as jest.MockedFunction<typeof requireAdmin>;
const mockCreateServiceRoleClient = createServiceRoleClient as jest.MockedFunction<typeof createServiceRoleClient>;

describe('/api/admin/devices/list', () => {
  let mockSupabase: any;

  beforeEach(() => {
    jest.clearAllMocks();
    
    mockSupabase = {
      from: jest.fn().mockReturnThis(),
      select: jest.fn().mockReturnThis(),
      order: jest.fn().mockReturnThis(),
      eq: jest.fn().mockReturnThis(),
      ilike: jest.fn().mockReturnThis(),
      range: jest.fn().mockReturnThis(),
      auth: {
        admin: {
          listUsers: jest.fn()
        }
      }
    };
    
    mockCreateServiceRoleClient.mockReturnValue(mockSupabase);
    mockRequireAdmin.mockResolvedValue({ id: 'admin-123', email: '<EMAIL>' } as any);
  });

  it('should return devices list with pagination', async () => {
    const mockDevices = [
      {
        device_id: 'device-1',
        user_id: 'user-1',
        device_name: 'Test Device',
        platform: 'ios',
        app_version: '1.0.0',
        created_at: '2024-01-01T00:00:00Z',
        last_seen_at: '2024-01-02T00:00:00Z',
        revoked: false,
        risk_score: 20
      }
    ];

    mockSupabase.select.mockResolvedValueOnce({ count: 1 });
    mockSupabase.range.mockResolvedValueOnce({ data: mockDevices, error: null });
    mockSupabase.auth.admin.listUsers.mockResolvedValueOnce({
      users: [{ id: 'user-1', email: '<EMAIL>' }]
    });

    const request = new NextRequest('http://localhost/api/admin/devices/list?page=1&limit=20');
    const response = await GET(request);
    const data = await response.json();

    expect(response.status).toBe(200);
    expect(data.devices).toHaveLength(1);
    expect(data.devices[0]).toMatchObject({
      device_id: 'device-1',
      user_email: '<EMAIL>',
      device_name: 'Test Device',
      platform: 'ios',
      is_revoked: false
    });
    expect(data.pagination).toEqual({
      page: 1,
      limit: 20,
      total: 1,
      totalPages: 1
    });
  });

  it('should filter devices by platform', async () => {
    const request = new NextRequest('http://localhost/api/admin/devices/list?platform=ios');
    await GET(request);

    expect(mockSupabase.eq).toHaveBeenCalledWith('platform', 'ios');
  });

  it('should filter devices by status', async () => {
    const request = new NextRequest('http://localhost/api/admin/devices/list?status=revoked');
    await GET(request);

    expect(mockSupabase.eq).toHaveBeenCalledWith('revoked', true);
  });

  it('should search devices by name', async () => {
    const request = new NextRequest('http://localhost/api/admin/devices/list?search=iPhone');
    await GET(request);

    expect(mockSupabase.ilike).toHaveBeenCalledWith('device_name', '%iPhone%');
  });

  it('should return 403 if user is not admin', async () => {
    mockRequireAdmin.mockResolvedValueOnce(Response.json({ error: 'Admin access required' }, { status: 403 }));

    const request = new NextRequest('http://localhost/api/admin/devices/list');
    const response = await GET(request);

    expect(response.status).toBe(403);
  });

  it('should handle database errors', async () => {
    mockSupabase.range.mockResolvedValueOnce({ data: null, error: { message: 'Database error' } });

    const request = new NextRequest('http://localhost/api/admin/devices/list');
    const response = await GET(request);

    expect(response.status).toBe(500);
    const data = await response.json();
    expect(data.error).toBe('Failed to fetch devices');
  });

  it('should apply pagination correctly', async () => {
    const request = new NextRequest('http://localhost/api/admin/devices/list?page=2&limit=10');
    mockSupabase.select.mockResolvedValueOnce({ count: 25 });
    mockSupabase.range.mockResolvedValueOnce({ data: [], error: null });
    mockSupabase.auth.admin.listUsers.mockResolvedValueOnce({ users: [] });

    await GET(request);

    expect(mockSupabase.range).toHaveBeenCalledWith(10, 19); // page 2, limit 10
  });
});