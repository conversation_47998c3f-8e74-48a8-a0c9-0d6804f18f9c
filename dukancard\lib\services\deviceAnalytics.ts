import { createServiceRoleClient } from '@/utils/supabase/service-role';

interface DeviceMetrics {
  totalDevices: number;
  activeDevices: number;
  revokedDevices: number;
  quarantinedDevices: number;
  platformBreakdown: { [platform: string]: number };
  riskDistribution: { low: number; medium: number; high: number };
  registrationTrend: { date: string; count: number }[];
  activityTrend: { date: string; activeDevices: number }[];
}

interface DeviceActivityTimeline {
  device_id: string;
  events: DeviceEvent[];
  riskHistory: { date: string; score: number }[];
  locationHistory: { date: string; location: string; ip: string }[];
}

interface DeviceEvent {
  timestamp: string;
  event_type: 'registration' | 'login' | 'logout' | 'revocation' | 'quarantine' | 'risk_update' | 'alert';
  description: string;
  severity?: 'info' | 'warning' | 'critical';
  metadata?: any;
}

/**
 * Device analytics service for metrics and activity tracking
 */
export class DeviceAnalytics {
  private supabase = createServiceRoleClient();

  /**
   * Get comprehensive device metrics
   */
  async getDeviceMetrics(timeRange: 'day' | 'week' | 'month' | 'year' = 'month'): Promise<DeviceMetrics> {
    try {
      const endDate = new Date();
      const startDate = new Date();
      
      switch (timeRange) {
        case 'day':
          startDate.setDate(endDate.getDate() - 1);
          break;
        case 'week':
          startDate.setDate(endDate.getDate() - 7);
          break;
        case 'month':
          startDate.setMonth(endDate.getMonth() - 1);
          break;
        case 'year':
          startDate.setFullYear(endDate.getFullYear() - 1);
          break;
      }

      // Get basic device counts
      const [totalDevicesResult, activeDevicesResult, revokedDevicesResult, quarantinedResult] = await Promise.all([
        this.supabase.from('devices').select('*', { count: 'exact', head: true }),
        this.supabase.from('devices').select('*', { count: 'exact', head: true }).eq('revoked', false),
        this.supabase.from('devices').select('*', { count: 'exact', head: true }).eq('revoked', true),
        this.supabase.from('devices').select('*', { count: 'exact', head: true }).eq('is_quarantined', true)
      ]);

      const totalDevices = totalDevicesResult.count || 0;
      const activeDevices = activeDevicesResult.count || 0;
      const revokedDevices = revokedDevicesResult.count || 0;
      const quarantinedDevices = quarantinedResult.count || 0;

      // Platform breakdown
      const { data: platformData } = await this.supabase
        .from('devices')
        .select('platform')
        .not('platform', 'is', null);

      const platformBreakdown = (platformData || []).reduce((acc, device) => {
        acc[device.platform] = (acc[device.platform] || 0) + 1;
        return acc;
      }, {} as { [platform: string]: number });

      // Risk distribution
      const { data: riskData } = await this.supabase
        .from('devices')
        .select('risk_score')
        .not('risk_score', 'is', null);

      const riskDistribution = (riskData || []).reduce((acc, device) => {
        const score = device.risk_score || 0;
        if (score < 30) acc.low++;
        else if (score < 70) acc.medium++;
        else acc.high++;
        return acc;
      }, { low: 0, medium: 0, high: 0 });

      // Registration trend
      const registrationTrend = await this.getRegistrationTrend(startDate, endDate);

      // Activity trend (simplified - based on last_seen_at)
      const activityTrend = await this.getActivityTrend(startDate, endDate);

      return {
        totalDevices,
        activeDevices,
        revokedDevices,
        quarantinedDevices,
        platformBreakdown,
        riskDistribution,
        registrationTrend,
        activityTrend
      };

    } catch (error) {
      console.error('Error getting device metrics:', error);
      return {
        totalDevices: 0,
        activeDevices: 0,
        revokedDevices: 0,
        quarantinedDevices: 0,
        platformBreakdown: {},
        riskDistribution: { low: 0, medium: 0, high: 0 },
        registrationTrend: [],
        activityTrend: []
      };
    }
  }

  /**
   * Get device activity timeline
   */
  async getDeviceActivityTimeline(deviceId: string, days: number = 30): Promise<DeviceActivityTimeline> {
    try {
      const events: DeviceEvent[] = [];
      const riskHistory: { date: string; score: number }[] = [];
      const locationHistory: { date: string; location: string; ip: string }[] = [];

      // Get device info
      const { data: device } = await this.supabase
        .from('devices')
        .select('*')
        .eq('device_id', deviceId)
        .single();

      if (device) {
        // Add registration event
        events.push({
          timestamp: device.created_at,
          event_type: 'registration',
          description: `Device registered: ${device.device_name || 'Unknown Device'}`,
          severity: 'info',
          metadata: {
            platform: device.platform,
            app_version: device.app_version
          }
        });

        // Add revocation event if applicable
        if (device.revoked && device.revoked_at) {
          events.push({
            timestamp: device.revoked_at,
            event_type: 'revocation',
            description: `Device revoked: ${device.revocation_reason || 'No reason provided'}`,
            severity: 'critical',
            metadata: {
              reason: device.revocation_reason
            }
          });
        }

        // Add quarantine event if applicable
        if (device.is_quarantined && device.quarantined_at) {
          events.push({
            timestamp: device.quarantined_at,
            event_type: 'quarantine',
            description: `Device quarantined: ${device.quarantine_reason || 'No reason provided'}`,
            severity: 'warning',
            metadata: {
              reason: device.quarantine_reason
            }
          });
        }

        // Add current risk score
        if (device.risk_score) {
          riskHistory.push({
            date: new Date().toISOString().split('T')[0],
            score: device.risk_score
          });
        }
      }

      // Get audit log events
      const { data: auditLogs } = await this.supabase
        .from('device_audit_logs')
        .select('*')
        .eq('target_device_id', deviceId)
        .order('timestamp', { ascending: false })
        .limit(50);

      if (auditLogs) {
        for (const log of auditLogs) {
          events.push({
            timestamp: log.timestamp,
            event_type: log.action_type === 'review' ? 'alert' : log.action_type,
            description: log.reason || `Admin ${log.action_type}`,
            severity: log.action_type === 'revoke' ? 'critical' : 'warning',
            metadata: {
              admin_action: true,
              admin_user: log.admin_user_id,
              ...log.metadata
            }
          });
        }
      }

      // Get security alerts
      const { data: alerts } = await this.supabase
        .from('suspicious_activity_alerts')
        .select('*')
        .eq('device_id', deviceId)
        .order('created_at', { ascending: false })
        .limit(20);

      if (alerts) {
        for (const alert of alerts) {
          events.push({
            timestamp: alert.created_at,
            event_type: 'alert',
            description: alert.details?.description || `${alert.alert_type} detected`,
            severity: alert.severity === 'high' ? 'critical' : alert.severity === 'medium' ? 'warning' : 'info',
            metadata: {
              alert_type: alert.alert_type,
              alert_id: alert.alert_id,
              status: alert.status,
              ...alert.details
            }
          });
        }
      }

      // Sort events by timestamp (newest first)
      events.sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime());

      // Generate mock location history (would be real data in production)
      for (let i = 0; i < days; i++) {
        const date = new Date();
        date.setDate(date.getDate() - i);
        
        if (Math.random() > 0.3) { // 70% chance of activity each day
          locationHistory.push({
            date: date.toISOString().split('T')[0],
            location: this.generateMockLocation(),
            ip: this.generateMockIP()
          });
        }
      }

      return {
        device_id: deviceId,
        events,
        riskHistory,
        locationHistory
      };

    } catch (error) {
      console.error('Error getting device activity timeline:', error);
      return {
        device_id: deviceId,
        events: [],
        riskHistory: [],
        locationHistory: []
      };
    }
  }

  /**
   * Get registration trend data
   */
  private async getRegistrationTrend(startDate: Date, endDate: Date): Promise<{ date: string; count: number }[]> {
    try {
      const { data: devices } = await this.supabase
        .from('devices')
        .select('created_at')
        .gte('created_at', startDate.toISOString())
        .lte('created_at', endDate.toISOString());

      if (!devices) return [];

      // Group by date
      const dateGroups = devices.reduce((acc, device) => {
        const date = device.created_at.split('T')[0];
        acc[date] = (acc[date] || 0) + 1;
        return acc;
      }, {} as { [date: string]: number });

      // Fill in missing dates with 0
      const trend: { date: string; count: number }[] = [];
      const currentDate = new Date(startDate);
      
      while (currentDate <= endDate) {
        const dateStr = currentDate.toISOString().split('T')[0];
        trend.push({
          date: dateStr,
          count: dateGroups[dateStr] || 0
        });
        currentDate.setDate(currentDate.getDate() + 1);
      }

      return trend;

    } catch (error) {
      console.error('Error getting registration trend:', error);
      return [];
    }
  }

  /**
   * Get activity trend data
   */
  private async getActivityTrend(startDate: Date, endDate: Date): Promise<{ date: string; activeDevices: number }[]> {
    try {
      // This is simplified - in reality you'd track daily activity
      const { data: devices } = await this.supabase
        .from('devices')
        .select('last_seen_at')
        .eq('revoked', false)
        .not('last_seen_at', 'is', null);

      if (!devices) return [];

      // Group devices that were active on each day
      const trend: { date: string; activeDevices: number }[] = [];
      const currentDate = new Date(startDate);
      
      while (currentDate <= endDate) {
        const dateStr = currentDate.toISOString().split('T')[0];
        const nextDay = new Date(currentDate);
        nextDay.setDate(nextDay.getDate() + 1);
        
        // Count devices that were seen on this day
        const activeOnDay = devices.filter(device => {
          const lastSeen = new Date(device.last_seen_at);
          return lastSeen >= currentDate && lastSeen < nextDay;
        }).length;

        trend.push({
          date: dateStr,
          activeDevices: activeOnDay
        });

        currentDate.setDate(currentDate.getDate() + 1);
      }

      return trend;

    } catch (error) {
      console.error('Error getting activity trend:', error);
      return [];
    }
  }

  /**
   * Get security dashboard metrics
   */
  async getSecurityMetrics(): Promise<{
    alertsToday: number;
    highRiskDevices: number;
    averageRiskScore: number;
    topAlertTypes: { type: string; count: number }[];
    recentAlerts: any[];
  }> {
    try {
      const today = new Date();
      today.setHours(0, 0, 0, 0);

      const [alertsTodayResult, highRiskResult, averageRiskResult, alertTypesResult, recentAlertsResult] = await Promise.all([
        // Alerts today
        this.supabase
          .from('suspicious_activity_alerts')
          .select('*', { count: 'exact', head: true })
          .gte('created_at', today.toISOString()),

        // High risk devices (score >= 70)
        this.supabase
          .from('devices')
          .select('*', { count: 'exact', head: true })
          .gte('risk_score', 70),

        // Average risk score
        this.supabase
          .from('devices')
          .select('risk_score')
          .not('risk_score', 'is', null),

        // Alert types breakdown
        this.supabase
          .from('suspicious_activity_alerts')
          .select('alert_type')
          .gte('created_at', new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString()), // Last 7 days

        // Recent alerts
        this.supabase
          .from('suspicious_activity_alerts')
          .select('*, devices(device_name)')
          .order('created_at', { ascending: false })
          .limit(10)
      ]);

      const alertsToday = alertsTodayResult.count || 0;
      const highRiskDevices = highRiskResult.count || 0;
      
      const riskScores = averageRiskResult.data || [];
      const averageRiskScore = riskScores.length > 0 
        ? Math.round(riskScores.reduce((sum, device) => sum + (device.risk_score || 0), 0) / riskScores.length)
        : 0;

      const alertTypes = (alertTypesResult.data || []).reduce((acc, alert) => {
        acc[alert.alert_type] = (acc[alert.alert_type] || 0) + 1;
        return acc;
      }, {} as { [type: string]: number });

      const topAlertTypes = Object.entries(alertTypes)
        .map(([type, count]) => ({ type, count }))
        .sort((a, b) => b.count - a.count)
        .slice(0, 5);

      const recentAlerts = recentAlertsResult.data || [];

      return {
        alertsToday,
        highRiskDevices,
        averageRiskScore,
        topAlertTypes,
        recentAlerts
      };

    } catch (error) {
      console.error('Error getting security metrics:', error);
      return {
        alertsToday: 0,
        highRiskDevices: 0,
        averageRiskScore: 0,
        topAlertTypes: [],
        recentAlerts: []
      };
    }
  }

  // Helper methods
  private generateMockIP(): string {
    return `${Math.floor(Math.random() * 256)}.${Math.floor(Math.random() * 256)}.${Math.floor(Math.random() * 256)}.${Math.floor(Math.random() * 256)}`;
  }

  private generateMockLocation(): string {
    const locations = ['New York, US', 'Toronto, CA', 'London, UK', 'Berlin, DE', 'Tokyo, JP'];
    return locations[Math.floor(Math.random() * locations.length)];
  }
}

export const deviceAnalytics = new DeviceAnalytics();