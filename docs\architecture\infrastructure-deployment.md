# Infrastructure & Deployment

## 7.1 Deployment Architecture

```
Production Deployment Stack:
┌─────────────────────────────────────────┐
│                Load Balancer             │
│           (Platform-managed)            │
└─────────────────┬───────────────────────┘
                  │
┌─────────────────▼───────────────────────┐
│            Next.js Application          │
│        (Vercel/Platform Deployment)     │
│                                         │
│ • API Routes (Centralized Layer)        │
│ • Static Site Generation               │
│ • Edge Functions                       │
└─────────────────┬───────────────────────┘
                  │
┌─────────────────▼───────────────────────┐
│              Supabase                   │
│                                         │
│ • PostgreSQL Database                  │
│ • Storage (Media Files)                │
│ • Real-time Subscriptions             │
│ • Row Level Security                   │
└─────────────────┬───────────────────────┘
                  │
┌─────────────────▼───────────────────────┐
│            Upstash Redis                │
│                                         │
│ • Rate Limiting                        │
│ • Session Management                   │
│ • Cache Layer                          │
└─────────────────────────────────────────┘
```

## 7.2 Environment Configuration

```typescript
// Environment-specific configuration
export interface EnvironmentConfig {
  development: {
    api: {
      baseUrl: "http://localhost:3000";
      rateLimiting: false;
      hmacEnforcement: false;
      verboseLogging: true;
    };
    database: {
      supabaseUrl: "dev-project-url";
      realtimeEnabled: true;
      rlsEnabled: true;
    };
  };
  
  staging: {
    api: {
      baseUrl: "https://staging.dukancard.com";
      rateLimiting: true;
      hmacEnforcement: true;
      verboseLogging: true;
    };
    database: {
      supabaseUrl: "staging-project-url";
      realtimeEnabled: true;
      rlsEnabled: true;
    };
  };
  
  production: {
    api: {
      baseUrl: "https://dukancard.com";
      rateLimiting: true;
      hmacEnforcement: true;
      verboseLogging: false;
    };
    database: {
      supabaseUrl: "prod-project-url";
      realtimeEnabled: true;
      rlsEnabled: true;
    };
  };
}
```
