import { renderHook, act, waitFor } from '@testing-library/react';
import { useAdminDevices } from '@/lib/hooks/admin/useAdminDevices';

// Mock fetch
global.fetch = jest.fn();

describe('useAdminDevices', () => {
  const mockDevices = [
    {
      device_id: 'device-1',
      user_id: 'user-1',
      user_email: '<EMAIL>',
      device_name: 'Test iPhone',
      platform: 'ios',
      app_version: '1.0.0',
      registered_at: '2024-01-01T00:00:00Z',
      last_activity: '2024-01-02T00:00:00Z',
      is_revoked: false,
      risk_score: 25
    }
  ];

  const mockResponse = {
    devices: mockDevices,
    pagination: { page: 1, limit: 20, total: 1, totalPages: 1 },
    filters: { page: 1, limit: 20 }
  };

  beforeEach(() => {
    jest.clearAllMocks();
    (global.fetch as jest.Mock).mockResolvedValue({
      ok: true,
      json: () => Promise.resolve(mockResponse)
    });
  });

  it('should fetch devices on mount', async () => {
    const { result } = renderHook(() => useAdminDevices());

    expect(result.current.loading).toBe(true);

    await waitFor(() => {
      expect(result.current.loading).toBe(false);
    });

    expect(result.current.devices).toEqual(mockDevices);
    expect(result.current.pagination).toEqual(mockResponse.pagination);
    expect(global.fetch).toHaveBeenCalledWith('/api/admin/devices/list?page=1&limit=20');
  });

  it('should handle fetch error', async () => {
    (global.fetch as jest.Mock).mockRejectedValueOnce(new Error('Network error'));

    const { result } = renderHook(() => useAdminDevices());

    await waitFor(() => {
      expect(result.current.loading).toBe(false);
    });

    expect(result.current.error).toBe('Network error');
    expect(result.current.devices).toEqual([]);
  });

  it('should handle HTTP error response', async () => {
    (global.fetch as jest.Mock).mockResolvedValueOnce({
      ok: false,
      status: 500
    });

    const { result } = renderHook(() => useAdminDevices());

    await waitFor(() => {
      expect(result.current.loading).toBe(false);
    });

    expect(result.current.error).toBe('HTTP error! status: 500');
  });

  it('should update filters and refetch', async () => {
    const { result } = renderHook(() => useAdminDevices());

    await waitFor(() => {
      expect(result.current.loading).toBe(false);
    });

    act(() => {
      result.current.updateFilters({ search: 'iPhone', platform: 'ios' });
    });

    await waitFor(() => {
      expect(global.fetch).toHaveBeenCalledWith(
        '/api/admin/devices/list?page=1&limit=20&search=iPhone&platform=ios'
      );
    });
  });

  it('should revoke device successfully', async () => {
    (global.fetch as jest.Mock)
      .mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve(mockResponse)
      })
      .mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve({ success: true })
      });

    const { result } = renderHook(() => useAdminDevices());

    await waitFor(() => {
      expect(result.current.loading).toBe(false);
    });

    let revokeResult;
    await act(async () => {
      revokeResult = await result.current.revokeDevice('device-1', 'Test reason');
    });

    expect(revokeResult).toEqual({ success: true });
    expect(global.fetch).toHaveBeenCalledWith('/api/admin/devices/revoke', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        device_ids: ['device-1'],
        reason: 'Test reason'
      })
    });

    // Should update local state
    expect(result.current.devices[0].is_revoked).toBe(true);
  });

  it('should handle revoke device failure', async () => {
    (global.fetch as jest.Mock)
      .mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve(mockResponse)
      })
      .mockResolvedValueOnce({
        ok: false,
        status: 500
      });

    const { result } = renderHook(() => useAdminDevices());

    await waitFor(() => {
      expect(result.current.loading).toBe(false);
    });

    let revokeResult;
    await act(async () => {
      revokeResult = await result.current.revokeDevice('device-1');
    });

    expect(revokeResult).toEqual({
      success: false,
      error: 'HTTP error! status: 500'
    });
  });

  it('should quarantine device successfully', async () => {
    (global.fetch as jest.Mock)
      .mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve(mockResponse)
      })
      .mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve({ success: true })
      })
      .mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve(mockResponse) // Refetch after quarantine
      });

    const { result } = renderHook(() => useAdminDevices());

    await waitFor(() => {
      expect(result.current.loading).toBe(false);
    });

    let quarantineResult;
    await act(async () => {
      quarantineResult = await result.current.quarantineDevice('device-1', 'Test reason');
    });

    expect(quarantineResult).toEqual({ success: true });
    expect(global.fetch).toHaveBeenCalledWith('/api/admin/devices/quarantine', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        device_id: 'device-1',
        reason: 'Test reason'
      })
    });
  });

  it('should handle quarantine device failure', async () => {
    (global.fetch as jest.Mock)
      .mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve(mockResponse)
      })
      .mockResolvedValueOnce({
        ok: false,
        json: () => Promise.resolve({ error: 'Quarantine failed' })
      });

    const { result } = renderHook(() => useAdminDevices());

    await waitFor(() => {
      expect(result.current.loading).toBe(false);
    });

    let quarantineResult;
    await act(async () => {
      quarantineResult = await result.current.quarantineDevice('device-1');
    });

    expect(quarantineResult).toEqual({
      success: false,
      error: 'Quarantine failed'
    });
  });

  it('should apply initial filters', async () => {
    const initialFilters = {
      search: 'test',
      platform: 'ios',
      status: 'active'
    };

    renderHook(() => useAdminDevices(initialFilters));

    await waitFor(() => {
      expect(global.fetch).toHaveBeenCalledWith(
        '/api/admin/devices/list?page=1&limit=20&search=test&platform=ios&status=active'
      );
    });
  });

  it('should refetch data when calling refetch', async () => {
    const { result } = renderHook(() => useAdminDevices());

    await waitFor(() => {
      expect(result.current.loading).toBe(false);
    });

    // Clear previous calls
    (global.fetch as jest.Mock).mockClear();

    await act(async () => {
      result.current.refetch();
    });

    await waitFor(() => {
      expect(global.fetch).toHaveBeenCalledWith('/api/admin/devices/list?page=1&limit=20');
    });
  });

  it('should build search params correctly', async () => {
    const { result } = renderHook(() => useAdminDevices({
      search: 'test device',
      platform: 'android',
      status: 'revoked',
      page: 2,
      limit: 50
    }));

    await waitFor(() => {
      expect(global.fetch).toHaveBeenCalledWith(
        '/api/admin/devices/list?page=2&limit=50&search=test%20device&platform=android&status=revoked'
      );
    });
  });

  it('should omit undefined and empty filter values', async () => {
    const { result } = renderHook(() => useAdminDevices({
      search: '',
      platform: undefined,
      status: 'all',
      page: 1
    }));

    await waitFor(() => {
      // Should only include defined, non-empty values
      expect(global.fetch).toHaveBeenCalledWith('/api/admin/devices/list?page=1&status=all');
    });
  });
});