# Product Requirements Document: DukanCard API Refactoring Initiative

## Goals and Background Context

### Goals
- Consolidate all database interactions through secure, centralized API routes, eliminating direct Supabase calls across both web and mobile applications
- Implement production-ready security with HMAC authentication and basic monitoring to protect customer and business privacy
- Achieve identical functionality and behavior between web and mobile platforms through centralized business logic
- Optimize performance with Zustand caching strategy while maintaining sub-500ms API response times for 85% of requests
- Reduce feature development time by 35% through elimination of code duplication
- Achieve 95%+ test coverage across all API routes and business logic components
- Enable long-term expansion opportunities including API marketplace, white-label platform, and security-as-a-service offerings

### Background Context

The DukanCard polyrepo architecture currently operates with a fragmented approach that creates significant technical debt and security vulnerabilities. The web application (dukancard/) uses Next.js server actions in `lib/actions/` for database operations, while the mobile application (dukancard-app/) makes direct Supabase client calls, bypassing centralized business logic.

This architectural split creates multiple critical issues: security gaps where direct Supabase access from mobile creates potential for reverse engineering and unauthorized data access; code duplication where identical business logic is maintained separately across platforms leading to 40% increased development time; inconsistent feature parity where different implementations create behavioral differences between web and mobile; and compliance concerns where direct database access makes it difficult to implement consistent privacy controls and audit trails.

As a social commerce platform handling sensitive customer and business data, maintaining customer trust is paramount. Recent growth and feature expansion has exposed these architectural weaknesses, making immediate consolidation critical for maintaining customer confidence, supporting regulatory compliance, preventing potential data breaches, and enabling rapid feature development without security compromises.

### Change Log

| Date | Version | Description | Author |
|------|---------|-------------|---------|
| 2025-08-10 | 1.0 | Initial PRD creation based on comprehensive API refactoring brief | PM Agent |

## Requirements

### Functional Requirements

**FR-001: API Route Consolidation Infrastructure**
- Implement unified API-first architecture where all database operations flow through secure Next.js API routes following the pattern: Mobile App → HMAC + JWT → API Routes → RLS-Protected Supabase
- Convert all existing direct Supabase calls in mobile application to centralized API endpoints
- Create comprehensive API route structure covering authentication, user management, business operations, product management, and social features
- Implement gradual migration system with feature flags enabling safe rollout and instant rollback capabilities
- Establish API versioning strategy to support backward compatibility during migration phases

**FR-002: Cross-Platform Feature Parity System**  
- Ensure identical functionality and behavior between web (Next.js) and mobile (React Native) platforms through shared API endpoints
- Implement consistent error handling, validation rules, and business logic across all platforms
- Create unified testing framework validating feature parity between web and mobile implementations
- Establish standardized response formats and data structures used by both platforms
- Develop cross-platform monitoring system tracking behavioral consistency and identifying discrepancies

**FR-003: Performance & Caching Strategy**
- Optimize Zustand stores for intelligent client-side caching reducing API calls while maintaining data freshness
- Implement smart caching strategies with automatic invalidation based on data update patterns
- Create performance monitoring system tracking API response times, cache hit rates, and overall platform responsiveness
- Develop cache synchronization mechanisms ensuring data consistency between web and mobile platforms
- Establish performance benchmarking tools measuring impact of API layer on user experience

**FR-004: Business Logic Centralization**
- Consolidate user profile management (business and customer) through centralized API endpoints with consistent validation
- Centralize product management operations including inventory tracking, pricing rules, and catalog management
- Unify social features (likes, comments, reviews, follows) through shared business logic and API routes
- Create centralized content management system handling media uploads, business cards, and product galleries
- Implement unified search and discovery algorithms serving both web and mobile platforms

**FR-005: Authentication & Security Infrastructure**
- Implement HMAC authentication for all mobile API requests using device-specific secrets
- Create comprehensive device management system with registration, revocation, and monitoring capabilities
- Establish token rotation system for refresh tokens with automatic security incident detection
- Develop admin separation layer using service key authentication for administrative operations
- Create security monitoring dashboard for device tracking, suspicious activity detection, and audit logging

**FR-006: Migration & Feature Flag System**
- Design gradual migration framework allowing phased implementation without breaking existing functionality
- Create feature flag infrastructure enabling selective activation of new API endpoints
- Implement rollback mechanisms allowing instant reversion to previous implementation during issues
- Establish migration monitoring system tracking progress and identifying potential problems
- Develop testing protocols validating each migration phase before production deployment

**FR-007: Long-term Expansion Infrastructure**
- Design API architecture supporting future marketplace functionality for third-party integrations
- Create foundation for white-label platform capabilities enabling template-based implementations
- Establish security-as-a-service infrastructure allowing HMAC middleware usage by other applications
- Develop API documentation and SDK foundation for future developer ecosystem
- Create monitoring and analytics infrastructure supporting future business intelligence requirements

### Non-Functional Requirements

**NFR-001: Performance Standards**
- Maintain sub-500ms API response times for 85% of requests across all endpoints
- Implement performance monitoring with automated alerting for response time degradation
- Ensure API layer introduces minimal latency compared to direct database access
- Support concurrent user load of 10,000+ users without performance degradation

**NFR-002: Availability & Reliability**  
- Achieve 99% uptime with manual monitoring and incident response procedures
- Implement basic health checks and monitoring for all API endpoints
- Establish incident response procedures for API failures and security breaches
- Create backup and recovery procedures for API service disruptions

**NFR-003: Security Standards**
- Implement production security with basic logging meeting current security requirements
- Establish comprehensive audit trails for all API access and data modifications
- Create device management security preventing unauthorized API access
- Implement rate limiting and brute force protection for all endpoints

**NFR-004: Test Coverage Requirements**
- Achieve 95%+ unit test coverage across all API routes and business logic components
- Implement comprehensive integration testing validating end-to-end functionality
- Create automated testing pipeline preventing regression during migration phases
- Establish performance testing validating response time requirements under load

**NFR-005: Migration Risk Management**
- Accept planned maintenance windows for major migration phases to minimize risk
- Implement comprehensive rollback procedures for each migration phase
- Create staging environment perfectly mirroring production for migration testing
- Establish communication protocols for planned downtime and maintenance windows

## Technical Assumptions

### Repository Structure
- **Type**: Polyrepo - maintaining separate dukancard/ (Next.js web) and dukancard-app/ (React Native mobile) repositories
- **Shared Components**: API type definitions, utilities, and constants shared between repositories
- **Backend Services**: Centralized business logic in backend/supabase/services/ directory structure

### Service Architecture  
- **API Layer**: Next.js API routes serving as unified backend for both web and mobile applications
- **Database**: Supabase PostgreSQL with Row Level Security (RLS) maintained as backup protection layer
- **Authentication**: Supabase Auth enhanced with HMAC device authentication for mobile applications
- **Storage**: Supabase Storage accessed through API routes with proper access controls and validation

### Testing Requirements
- **Unit Testing**: Jest with jsdom for web components, React Native preset for mobile components
- **Integration Testing**: API route testing with mock database scenarios and authentication flows  
- **End-to-End Testing**: Detox for mobile E2E testing, Playwright for web E2E testing
- **Performance Testing**: Load testing for API endpoints, response time monitoring, and cache effectiveness validation

### Additional Technical Assumptions
- Current device registration system is stable and secure for HMAC implementation
- Existing Zustand stores can be enhanced for API caching without major architectural changes
- Supabase RLS policies are correctly configured and will remain as backup security layer
- Development team has sufficient API development experience for complex authentication flows
- Mobile app users will accept minor performance impact from API layer introduction

## Epic List

### Phase 1: Security Foundation (Weeks 1-8)
**Epic 1.1: HMAC Authentication Infrastructure** - Implement comprehensive device registration, HMAC middleware, and mobile authentication flows
**Epic 1.2: API Security Framework** - Create route protection middleware, admin separation, and basic security monitoring
**Epic 1.3: Token Management System** - Implement refresh token rotation, device revocation, and session management

### Phase 2: Core API Migration (Weeks 9-20)  
**Epic 2.1: Authentication API Consolidation** - Migrate all auth operations to centralized API routes with feature flags
**Epic 2.2: User Profile API Migration** - Centralize business and customer profile management through API endpoints
**Epic 2.3: Business Logic API Creation** - Create APIs for product management, inventory, and business card operations

### Phase 3: Cross-Platform Parity (Weeks 21-32)
**Epic 3.1: Mobile Store Architecture** - Create comprehensive Zustand stores mirroring web functionality  
**Epic 3.2: Feature Parity Validation** - Implement testing framework ensuring identical behavior across platforms
**Epic 3.3: Performance Optimization** - Optimize caching strategies and eliminate performance bottlenecks

### Phase 4: Social & Discovery Features (Weeks 33-40)
**Epic 4.1: Social Features API** - Centralize likes, comments, reviews, and follow functionality
**Epic 4.2: Discovery & Search API** - Create unified search algorithms and business discovery endpoints
**Epic 4.3: Content Management API** - Centralize media handling, business cards, and product galleries

### Phase 5: Advanced Features & Monitoring (Weeks 41-48)
**Epic 5.1: Advanced Caching Strategy** - Implement Redis caching, request batching, and optimization
**Epic 5.2: Comprehensive Monitoring** - Create dashboards, alerting, and performance analytics
**Epic 5.3: Migration Completion** - Final migration phases, feature flag removal, and system validation

### Phase 6: Expansion Foundation (Weeks 49-52)
**Epic 6.1: API Marketplace Preparation** - Create public API framework and developer documentation
**Epic 6.2: White-label Infrastructure** - Establish template system and multi-tenant capabilities  
**Epic 6.3: Security-as-a-Service Framework** - Create reusable HMAC middleware and security tools

## Epic Details

### Epic 1.1: HMAC Authentication Infrastructure

**Epic Overview**: Implement comprehensive device registration system with HMAC authentication for all mobile API requests, ensuring enterprise-grade security while maintaining user experience.

**User Stories:**

1. **As a mobile app user, I want secure device registration during initial setup, so that my device is properly authenticated for all future API requests**
   - **Acceptance Criteria:**
     1. Device registration generates unique device ID and HMAC secret during first app launch
     2. Device credentials are stored securely using platform-specific secure storage (Keychain/Keystore)
     3. Registration process completes within 2 seconds under normal network conditions
     4. Failed registrations provide clear error messages and retry mechanisms
     5. Device information includes platform, app version, and unique device fingerprint

2. **As a backend system, I want to validate HMAC signatures on all mobile requests, so that I can prevent unauthorized API access and ensure request integrity**
   - **Acceptance Criteria:**
     1. All mobile API requests must include valid HMAC signature or receive 401 unauthorized response
     2. HMAC validation includes timestamp checks preventing replay attacks beyond 5-minute window
     3. Invalid signatures are logged for security monitoring and analysis
     4. Signature validation completes within 10ms to maintain API response times
     5. Clear error messages distinguish between expired, invalid, and missing signatures

3. **As a security administrator, I want comprehensive device management capabilities, so that I can monitor, revoke, and manage device access for security purposes**
   - **Acceptance Criteria:**
     1. Admin dashboard displays all registered devices with registration date, platform, and last activity
     2. Device revocation immediately invalidates all tokens and prevents future API access
     3. Suspicious activity detection automatically flags unusual patterns for review
     4. Bulk device management operations support batch revocation and policy application
     5. Audit logs track all device management actions with administrator identification

### Epic 1.2: API Security Framework

**Epic Overview**: Create comprehensive route protection middleware with admin separation, CSRF protection, and basic security monitoring to establish secure API foundation.

**User Stories:**

1. **As an API route, I want standardized security middleware, so that I can enforce consistent authentication and authorization across all endpoints**
   - **Acceptance Criteria:**
     1. Security middleware supports configurable options for HMAC, CSRF, admin-only, and rate limiting
     2. All routes use withEnhancedSecurity wrapper with appropriate security configuration
     3. Middleware provides consistent error handling and response formats across all routes
     4. Platform detection automatically applies correct security measures (HMAC for mobile, CSRF for web)
     5. Security context includes user, device, platform, and permission information

2. **As a web application user, I want CSRF protection on all state-changing operations, so that my account remains secure from cross-site attacks**
   - **Acceptance Criteria:**
     1. All POST, PUT, DELETE requests from web require valid CSRF token
     2. CSRF tokens are automatically generated and embedded in web forms and AJAX requests  
     3. Invalid CSRF tokens return 403 forbidden with clear error messaging
     4. CSRF protection integrates seamlessly with existing Next.js middleware
     5. Token refresh happens automatically without disrupting user experience

3. **As an administrator, I want separated admin routes with service key authentication, so that administrative operations are isolated from regular user access**
   - **Acceptance Criteria:**
     1. Admin routes require service key authentication separate from user JWT tokens
     2. Admin operations bypass RLS policies using service role client for elevated access
     3. All admin actions are logged with detailed audit trails including action, timestamp, and context
     4. Admin routes are clearly namespaced and documented separately from user routes
     5. Service key validation includes environment checks preventing accidental production access

### Epic 2.1: Authentication API Consolidation

**Epic Overview**: Migrate all authentication operations from direct Supabase calls to centralized API routes with feature flag support enabling gradual rollout.

**User Stories:**

1. **As a user, I want seamless login experience across web and mobile platforms, so that I can access my account consistently regardless of device**
   - **Acceptance Criteria:**  
     1. Login API endpoint handles email/password and OAuth authentication for both platforms
     2. Successful authentication returns consistent JWT tokens and user profile information
     3. Feature flags allow gradual migration from direct Supabase auth to API routes
     4. Error handling provides user-friendly messages for invalid credentials, locked accounts, and network issues
     5. Login process completes within 3 seconds under normal conditions

2. **As a new user, I want streamlined registration process through unified API, so that I can create accounts efficiently on any platform**
   - **Acceptance Criteria:**
     1. Registration API validates email uniqueness, password strength, and required profile information
     2. Email verification process works consistently across web and mobile platforms  
     3. Registration includes automatic device registration for mobile users
     4. User onboarding flow directs to appropriate role selection and profile completion
     5. Failed registrations provide specific validation errors for each field

3. **As a mobile user, I want automatic token refresh, so that I remain logged in without manual intervention or session interruptions**
   - **Acceptance Criteria:**
     1. Refresh token API automatically extends sessions before expiration
     2. Token refresh happens transparently without user awareness or app interruption
     3. Failed refresh attempts trigger graceful logout with option to re-authenticate
     4. Refresh process includes device validation ensuring token security
     5. Concurrent refresh requests are handled safely without token duplication

### Epic 2.2: User Profile API Migration

**Epic Overview**: Centralize all user profile operations (business and customer) through API endpoints with consistent validation and business rules.

**User Stories:**

1. **As a business user, I want centralized profile management API, so that I can update my business information consistently across web and mobile platforms**
   - **Acceptance Criteria:**
     1. Business profile API handles company details, contact information, address, and business hours
     2. Profile updates trigger validation ensuring required fields, format checking, and business rules
     3. Changes sync immediately across web and mobile applications through cache invalidation
     4. File uploads for business logos and images are handled through secure API endpoints
     5. Profile completeness scoring guides users toward complete business profiles

2. **As a customer user, I want unified profile API for personal information management, so that my preferences and data remain synchronized across all access points**  
   - **Acceptance Criteria:**
     1. Customer profile API manages personal information, preferences, privacy settings, and social connections
     2. Profile privacy controls allow granular visibility settings for different information types
     3. Social connections (followers, following, liked businesses) sync across platforms
     4. Profile API integrates with discovery features for personalized business recommendations
     5. Data export functionality supports user privacy rights and account portability

3. **As a platform administrator, I want comprehensive user management API, so that I can moderate accounts, manage compliance, and support customer service operations**
   - **Acceptance Criteria:**
     1. Admin user API provides read/write access to all user profiles with audit logging
     2. User moderation tools include account suspension, content flagging, and compliance tracking
     3. Customer service API allows secure profile access for support ticket resolution
     4. User analytics API provides aggregated insights without exposing personal information
     5. Compliance features support GDPR data requests and account deletion requirements

## Checklist Results Report

**PM Checklist Execution Status**: ✅ **COMPLETED**

### Key Checklist Items Addressed:
- ✅ **User Needs Analysis**: Identified primary users (development team) and secondary users (platform users) with specific needs and pain points
- ✅ **Business Value Quantification**: Established measurable goals including 35% development time reduction and 95% test coverage
- ✅ **Technical Feasibility**: Validated approach builds on existing infrastructure (device registration, HMAC middleware, Zustand stores)
- ✅ **Risk Assessment**: Identified key risks including performance impact, migration complexity, and security implementation challenges  
- ✅ **Success Metrics Definition**: Established KPIs including API security, code consolidation, test coverage, and migration progress
- ✅ **Stakeholder Alignment**: Ensured requirements address both technical team efficiency and end-user experience consistency
- ✅ **Scope Boundaries**: Clearly defined MVP vs post-MVP features with realistic timeline expectations
- ✅ **Dependencies Mapping**: Identified critical dependencies on existing device registration and security infrastructure

### Areas Requiring Ongoing Attention:
- **Performance Monitoring**: Requires continuous measurement during migration phases
- **Security Testing**: Needs comprehensive penetration testing of HMAC implementation  
- **Cross-Platform Testing**: Requires automated validation of feature parity during development
- **Change Management**: Team training and documentation updates throughout migration process

## Next Steps

### UX Expert Prompt

**Context**: DukanCard API Refactoring Initiative focuses on backend consolidation with minimal UI changes, but user experience consistency across platforms is critical.

**UX Expert Instructions**:
"Design seamless user experience patterns ensuring identical functionality between web and mobile during API migration. Key focus areas: 1) Error handling patterns that gracefully manage API failures and network issues, 2) Loading states and offline behavior during API calls, 3) Authentication flows that feel native on both platforms while using unified backend, 4) Performance perception management during API layer introduction, 5) Progressive migration UX allowing users to opt-in to new features gradually. Deliverable: UX specification document with interaction patterns, error handling flows, and cross-platform consistency guidelines."

### Architect Prompt  

**Context**: DukanCard polyrepo requires comprehensive API architecture consolidating web Next.js server actions and mobile direct Supabase calls into unified, secure endpoints.

**Architect Instructions**:
"Design production-ready API architecture implementing gradual migration from direct Supabase access to centralized Next.js API routes. Technical requirements: 1) HMAC authentication for mobile with device registration system, 2) Feature flag infrastructure enabling safe rollout and rollback, 3) Performance optimization maintaining sub-500ms response times through Zustand caching, 4) Cross-platform business logic consolidation eliminating code duplication, 5) Security monitoring with basic logging and device management, 6) Scalable foundation supporting future API marketplace and white-label expansion. Deliverable: Technical architecture document with API specifications, security implementation details, migration timeline, and infrastructure requirements."

---

**Document Status**: ✅ **APPROVED FOR DEVELOPMENT**
**Next Phase**: Technical Architecture Design & UX Specification
**Timeline**: 52-week implementation with quarterly milestone reviews