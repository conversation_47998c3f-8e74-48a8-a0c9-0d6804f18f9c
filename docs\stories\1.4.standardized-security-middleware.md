# Story 1.4: Standardized Security Middleware

## Status

Draft

## Story

**As an** API route,
**I want** standardized security middleware,
**so that** I can enforce consistent authentication and authorization across all endpoints

## Acceptance Criteria

1. Security middleware supports configurable options for HMAC, CSRF, admin-only, and rate limiting
2. All routes use withEnhancedSecurity wrapper with appropriate security configuration
3. Middleware provides consistent error handling and response formats across all routes
4. Platform detection automatically applies correct security measures (HMAC for mobile, CSRF for web)
5. Security context includes user, device, platform, and permission information

## Tasks / Subtasks

- [ ] Create enhanced security middleware foundation (AC: 1, 3)

  - [ ] Create `lib/middleware/enhancedSecurity.ts` with SecurityConfig interface
  - [ ] Implement `withEnhancedSecurity` wrapper function with configurable options
  - [ ] Add support for HMAC, CSRF, JWT, and admin-only authentication modes
  - [ ] Create consistent error response formatting across all security checks
  - [ ] Implement security context object with user, device, and platform information

- [ ] Implement platform detection and authentication logic (AC: 4, 5)

  - [ ] Create platform detection function using User-Agent and headers
  - [ ] Implement mobile authentication flow with HMAC validation
  - [ ] Implement web authentication flow with CSRF protection
  - [ ] Add JWT token validation for both platforms
  - [ ] Create unified AuthContext interface with all security metadata

- [ ] Add authorization and permission checking system (AC: 2, 5)

  - [ ] Implement role-based access control (business vs customer)
  - [ ] Add permission checking for specific operations
  - [ ] Create resource ownership validation
  - [ ] Implement admin separation with service key authentication
  - [ ] Add plan-based feature access control

- [ ] Create rate limiting and validation layer (AC: 1, 3)

  - [ ] Integrate with existing Upstash Redis rate limiting
  - [ ] Add configurable rate limit tiers (low, medium, high)
  - [ ] Implement input validation and sanitization options
  - [ ] Add request timeout and size limiting
  - [ ] Create middleware chaining for multiple security checks

- [ ] Build middleware integration utilities (AC: 2)

  - [ ] Create helper functions for common security configurations
  - [ ] Add middleware composition utilities for route-specific needs
  - [ ] Implement security configuration templates for different route types
  - [ ] Create debugging and logging utilities for security middleware
  - [ ] Add performance monitoring for middleware execution times

- [ ] Add comprehensive testing coverage (AC: All)
  - [ ] Test security configuration options and middleware wrapper
  - [ ] Test platform detection and appropriate security measure application
  - [ ] Test authentication flows for both web and mobile platforms
  - [ ] Test authorization checks and permission validation
  - [ ] Test rate limiting and input validation functionality

## Dev Notes

### Previous Story Insights

**From Story 1.1**: Device registration infrastructure provides the device_id and device secrets needed for HMAC validation in the security middleware.

**From Story 1.2**: HMAC validation middleware foundation established - this story extends that into a comprehensive, configurable security system.

**From Story 1.3**: Admin separation requirements established with service key authentication patterns that this middleware must support.

### Architecture Context

**Enhanced Route Protection**: The middleware system implements a multi-layer security model with configurable authentication, authorization, rate limiting, and validation layers.

[Source: architecture/target-architecture-design.md#231-multi-layer-security-model]

**Unified Authentication Context**: Security middleware creates consistent AuthContext objects containing user information, platform context, permissions, and security metadata.

[Source: architecture/api-layer-implementation.md#32-unified-authentication-context]

### API Specifications

**Security Configuration Interface**:

```typescript
interface SecurityConfig {
  authentication: {
    requireHMAC: boolean; // Mobile HMAC required
    requireCSRF: boolean; // Web CSRF required
    requireJWT: boolean; // JWT token required
    adminOnly: boolean; // Service key required
  };
  authorization: {
    allowedRoles: UserRole[];
    requiredPermissions: string[];
    resourceOwnership: boolean; // Check resource ownership
  };
  rateLimiting: {
    tier: "low" | "medium" | "high";
    customLimit?: RateLimitConfig;
  };
  validation: {
    strictMode: boolean; // Strict input validation
    sanitization: boolean; // Auto-sanitize inputs
  };
}
```

[Source: architecture/api-layer-implementation.md#31-enhanced-route-protection-middleware]

**WithEnhancedSecurity Wrapper**:

```typescript
export function withEnhancedSecurity(
  handler: APIHandler,
  config: SecurityConfig
) {
  return async (req: NextRequest, context: RequestContext) => {
    // 1. Rate Limiting Check
    // 2. Platform Detection & Authentication
    // 3. Authorization Checks
    // 4. Input Validation & Sanitization
    // 5. Execute Business Logic
  };
}
```

[Source: architecture/api-layer-implementation.md#31-enhanced-route-protection-middleware]

### Data Models

**AuthContext Interface**:

```typescript
interface AuthContext {
  user: {
    id: string;
    email: string;
    role: "business" | "customer";
    verified: boolean;
  };
  platform: {
    type: "web" | "mobile";
    device_id?: string;
    user_agent: string;
    ip_address: string;
  };
  permissions: string[];
  planId: string;
  isAdmin: boolean;
  supabase: SupabaseClient;
  adminSupabase?: SupabaseClient;
  security: {
    hmacVerified: boolean;
    csrfVerified: boolean;
    rateLimitInfo: RateLimitInfo;
    sessionId: string;
  };
}
```

[Source: architecture/api-layer-implementation.md#32-unified-authentication-context]

**Platform Detection**: Uses User-Agent and custom headers to detect web vs mobile platforms and apply appropriate security measures.

### Security Implementation

**Multi-Layer Security Stack**:

1. Request Authentication (CSRF for web, HMAC for mobile)
2. Rate Limiting (Upstash Redis with sliding window)
3. Route Protection (Role-based access + admin separation)
4. Business Logic Layer (Validation + sanitization)
5. Database Security (Supabase RLS as backup)

[Source: architecture/target-architecture-design.md#231-multi-layer-security-model]

**HMAC Authentication Flow**: Middleware validates HMAC signatures using device secrets from Story 1.1, including timestamp checks and replay protection.

[Source: architecture/target-architecture-design.md#232-hmac-authentication-flow]

### File Locations

Based on project structure and architecture specifications:

- **Middleware Core**: `dukancard/lib/middleware/enhancedSecurity.ts` (main middleware implementation)
- **Auth Context**: `dukancard/lib/auth/context.ts` (authentication context utilities)
- **Platform Detection**: `dukancard/lib/utils/platform.ts` (platform detection logic)
- **Security Configs**: `dukancard/lib/middleware/securityConfigs.ts` (predefined configurations)
- **Validation Utils**: `dukancard/lib/validation/security.ts` (input validation utilities)

### Component Specifications

**Middleware Configuration Templates**: Pre-built security configurations for common route types (public, authenticated, admin-only, high-security).

**Error Response Standardization**: Consistent error response formats with appropriate HTTP status codes and security-safe error messages.

**Performance Monitoring**: Integration with existing monitoring to track middleware execution times and security check performance.

### Technical Constraints

**Performance**: Middleware must complete security checks within 10ms to maintain API response time targets.

**Compatibility**: Must work with existing Next.js App Router and current authentication infrastructure.

**Security**: All authentication failures and suspicious activity must be logged for security monitoring.

**Extensibility**: Middleware design must support future addition of new authentication methods and security checks.

### Testing

**Testing Requirements**:

- **Unit Tests**: 95% code coverage for middleware functions and security validation logic [Source: architecture/testing-strategy.md#81-comprehensive-testing-framework]
- **Security Testing**: Authentication workflow testing, authorization checks, and admin separation validation [Source: architecture/testing-strategy.md#82-security-testing-requirements]
- **Integration Testing**: End-to-end security middleware integration with sample API routes
- **Performance Testing**: Middleware execution time validation under load conditions

**Test File Locations**:

- Middleware tests: `dukancard/__tests__/lib/middleware/enhancedSecurity.test.ts`
- Auth context tests: `dukancard/__tests__/lib/auth/context.test.ts`
- Platform detection tests: `dukancard/__tests__/lib/utils/platform.test.ts`
- Security config tests: `dukancard/__tests__/lib/middleware/securityConfigs.test.ts`
- Integration tests: `dukancard/__tests__/integration/middleware-security.test.ts`

**Testing Frameworks**: Jest for unit/integration tests with custom mocking for Supabase clients and Redis rate limiting [Source: architecture/testing-strategy.md#81-comprehensive-testing-framework]

### Project Structure Notes

This story builds upon the HMAC validation foundation from previous stories and creates the comprehensive middleware system that will be used by all future API routes. The middleware integrates with existing Upstash Redis rate limiting and Supabase authentication infrastructure.

## Change Log

| Date       | Version | Description            | Author             |
| ---------- | ------- | ---------------------- | ------------------ |
| 2025-08-10 | 1.0     | Initial story creation | Bob (Scrum Master) |

## Dev Agent Record

### Agent Model Used

_To be populated by development agent_

### Debug Log References

_To be populated by development agent_

### Completion Notes List

_To be populated by development agent_

### File List

_To be populated by development agent_

## QA Results

_Results from QA Agent review will be populated here after story completion_
