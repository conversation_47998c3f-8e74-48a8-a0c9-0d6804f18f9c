# DukanCard API Refactoring Architecture

## Executive Summary

This document outlines the comprehensive technical architecture for consolidating DukanCard's polyrepo system into a unified, secure API-first platform. The initiative transforms the current fragmented approach—where web uses Next.js server actions and mobile makes direct Supabase calls—into a centralized, production-ready architecture serving both platforms through secure API endpoints.

**Key Objectives:**
- **Security First**: HMAC authentication for mobile, eliminating direct database access
- **Code Consolidation**: Single source of truth for business logic across platforms
- **Performance Optimization**: Sub-500ms response times with intelligent caching
- **Cross-Platform Parity**: Identical functionality between web and mobile applications
- **Future-Ready Foundation**: Scalable architecture supporting marketplace and white-label expansion

## Current State Analysis

### 1.1 Existing Architecture Overview

```
Current Fragmented Architecture:
┌─────────────────────┐    ┌─────────────────────┐
│   Web Application   │    │  Mobile Application │
│     (dukancard/)    │    │   (dukancard-app/)  │
│                     │    │                     │
│ Next.js Server      │    │ React Native +      │
│ Actions in lib/     │    │ Direct Supabase     │
│ actions/            │    │ Client Calls        │
└─────────┬───────────┘    └─────────┬───────────┘
          │                          │
          └──────────┬─────────────────┘
                     │
              ┌──────▼──────┐
              │   Supabase  │
              │ PostgreSQL  │
              │    + RLS    │
              └─────────────┘
```

### 1.2 Current Technology Stack

**Web Application (dukancard/):**
- **Framework**: Next.js 15.2.4 with App Router
- **UI**: shadcn/ui components with Radix UI primitives
- **State Management**: Zustand 5.0.7 with Immer middleware
- **Authentication**: Supabase Auth with middleware-based session management
- **Database**: Direct server-side Supabase calls via server actions
- **Security**: CSRF protection, rate limiting with Upstash Redis
- **Testing**: Jest with 95% coverage target

**Mobile Application (dukancard-app/):**
- **Framework**: React Native 0.79.5 with Expo 53.0.19
- **Navigation**: Expo Router 5.1.3 with React Navigation
- **State Management**: Zustand 5.0.7 (minimal implementation)
- **Authentication**: Direct Supabase client authentication
- **Database**: Direct client-side Supabase calls
- **Security**: Basic JWT token validation
- **Testing**: Jest with Detox for E2E testing

**Shared Backend Infrastructure:**
- **Database**: Supabase PostgreSQL with Row Level Security (RLS)
- **Storage**: Supabase Storage with organized bucket structure
- **Real-time**: Supabase Realtime for live updates
- **Authentication**: Supabase Auth with email/password and Google OAuth

### 1.3 Critical Pain Points

**Security Vulnerabilities:**
- Direct Supabase access from mobile clients enables potential reverse engineering
- Inconsistent authentication patterns between platforms
- Limited audit trails and access control granularity

**Development Inefficiencies:**
- ~40% increased development time due to duplicate business logic
- Platform-specific implementation differences causing feature parity issues
- Complex testing scenarios across different data access patterns

**Scalability Limitations:**
- No centralized rate limiting or request monitoring
- Difficult to implement consistent caching strategies
- Limited ability to add business logic without platform-specific changes

### 1.4 Database Schema (Current)

```sql
-- Key Tables from constants.ts analysis
TABLES = {
  business_profiles,      -- Business account information
  customer_profiles,      -- Customer account information  
  business_posts,         -- Business content creation
  customer_posts,         -- Customer content creation
  products_services,      -- Product catalog
  product_variants,       -- Product variations
  likes,                  -- Social interactions
  post_likes,            -- Post engagement
  post_comments,         -- Post discussions
  comment_likes,         -- Comment engagement
  ratings_reviews,       -- Business reviews
  subscriptions,         -- Business plan management
  pincodes              -- Location data
}

BUCKETS = {
  business,             -- Business media storage
  customers            -- Customer media storage
}
```

## Target Architecture Design

### 2.1 Unified API-First Architecture

```
Target Unified Architecture:
┌─────────────────────┐    ┌─────────────────────┐
│   Web Application   │    │  Mobile Application │
│     (dukancard/)    │    │   (dukancard-app/)  │
│                     │    │                     │
│ CSRF + JWT Auth ────┼────┼──── HMAC + JWT Auth │
│ Enhanced Zustand    │    │ Enhanced Zustand    │
│ Stores              │    │ Stores              │
└─────────┬───────────┘    └─────────┬───────────┘
          │                          │
          └──────────┬─────────────────┘
                     │
        ┌────────────▼────────────┐
        │   Next.js API Routes    │
        │  (Centralized Layer)    │
        │                         │
        │ • HMAC Middleware       │
        │ • CSRF Protection       │
        │ • Rate Limiting         │
        │ • Business Logic        │
        │ • Validation Layer      │
        │ • Admin Separation      │
        └────────────┬────────────┘
                     │
              ┌──────▼──────┐
              │   Supabase  │
              │ PostgreSQL  │
              │ RLS Backup  │
              └─────────────┘
```

### 2.2 API Route Structure

```
/api/
├── auth/
│   ├── login/           # Unified login endpoint
│   ├── register/        # Account creation
│   ├── refresh/         # Token refresh with HMAC
│   └── logout/          # Session termination
├── user/
│   ├── profile/         # User profile management
│   └── preferences/     # User settings
├── business/
│   ├── profile/         # Business profile CRUD
│   ├── onboarding/      # Multi-step business setup
│   ├── dashboard/       # Analytics and metrics
│   ├── products/        # Product catalog management
│   └── [businessId]/    # Public business data
├── customer/
│   ├── profile/         # Customer profile CRUD
│   ├── feed/           # Personalized content feed
│   └── interactions/    # Likes, follows, reviews
├── posts/
│   ├── business/        # Business content management
│   ├── customer/        # Customer content management
│   └── [postId]/        # Single post operations
├── social/
│   ├── likes/           # Like/unlike operations
│   ├── comments/        # Comment management
│   └── follows/         # Follow/unfollow operations
├── storage/
│   ├── upload/          # Secure file upload
│   └── delete/          # File cleanup
├── discovery/
│   ├── search/          # Business/product search
│   ├── location/        # Location-based discovery
│   └── categories/      # Category filtering
└── admin/
    ├── users/           # User management (service key)
    ├── content/         # Content moderation
    └── analytics/       # Platform metrics
```

### 2.3 Security Architecture

#### 2.3.1 Multi-Layer Security Model

```
Security Layer Stack:
┌─────────────────────────────────────────┐
│         Request Authentication          │
│  Web: CSRF + JWT  |  Mobile: HMAC + JWT │
└─────────────────────────────────────────┘
┌─────────────────────────────────────────┐
│            Rate Limiting                │
│     Upstash Redis + Sliding Window      │
└─────────────────────────────────────────┘
┌─────────────────────────────────────────┐
│           Route Protection              │
│    Role-based Access + Admin Separation │
└─────────────────────────────────────────┘
┌─────────────────────────────────────────┐
│          Business Logic Layer          │
│     Validation + Sanitization + Auth   │
└─────────────────────────────────────────┘
┌─────────────────────────────────────────┐
│           Database Security             │
│        Supabase RLS (Backup Layer)      │
└─────────────────────────────────────────┘
```

#### 2.3.2 HMAC Authentication Flow

```typescript
// Mobile HMAC Authentication Implementation
interface HMACAuthFlow {
  // 1. Device Registration (One-time)
  deviceRegistration: {
    endpoint: "/api/auth/devices/register";
    method: "POST";
    payload: {
      deviceInfo: DeviceInfo;
      appSignature: string;
      userAgent: string;
    };
    response: {
      deviceId: string;
      deviceSecret: string;
      hmacKey: string;
    };
  };

  // 2. Request Signing (Every API Call)
  requestSigning: {
    components: [
      "HTTP_METHOD",
      "REQUEST_PATH", 
      "TIMESTAMP",
      "REQUEST_BODY_JSON",
      "DEVICE_SECRET"
    ];
    algorithm: "HMAC-SHA256";
    headers: {
      "X-Device-Id": string;
      "X-Timestamp": string;
      "X-Signature": string;
      "Authorization": "Bearer JWT_TOKEN";
    };
  };

  // 3. Server Validation
  serverValidation: {
    timestampWindow: "5 minutes";
    replayProtection: "Redis-based nonce tracking";
    deviceVerification: "Database lookup + status check";
    signatureValidation: "HMAC-SHA256 verification";
  };
}
```

#### 2.3.3 Device Management System

```typescript
// Device Management Schema
interface DeviceManagement {
  devices: {
    device_id: string;
    user_id: string;
    device_name: string;
    platform: "ios" | "android";
    app_version: string;
    device_secret_hash: string;
    hmac_key_hash: string;
    registered_at: timestamp;
    last_activity: timestamp;
    is_revoked: boolean;
    revoked_at?: timestamp;
    revocation_reason?: string;
  };

  refresh_tokens: {
    token_id: string;
    device_id: string;
    token_hash: string;
    expires_at: timestamp;
    created_at: timestamp;
  };

  // Admin Operations
  adminOperations: {
    revokeDevice: "/api/admin/devices/revoke";
    listDevices: "/api/admin/devices/list";
    suspiciousActivity: "/api/admin/security/alerts";
    deviceQuarantine: "/api/admin/devices/quarantine";
  };
}
```

## API Layer Implementation

### 3.1 Enhanced Route Protection Middleware

```typescript
// lib/middleware/enhancedSecurity.ts
export interface SecurityConfig {
  authentication: {
    requireHMAC: boolean;      // Mobile HMAC required
    requireCSRF: boolean;      // Web CSRF required
    requireJWT: boolean;       // JWT token required
    adminOnly: boolean;        // Service key required
  };
  authorization: {
    allowedRoles: UserRole[];
    requiredPermissions: string[];
    resourceOwnership: boolean; // Check resource ownership
  };
  rateLimiting: {
    tier: "low" | "medium" | "high";
    customLimit?: RateLimitConfig;
  };
  validation: {
    strictMode: boolean;       // Strict input validation
    sanitization: boolean;     // Auto-sanitize inputs
  };
}

export function withEnhancedSecurity(
  handler: APIHandler,
  config: SecurityConfig
) {
  return async (req: NextRequest, context: RequestContext) => {
    // 1. Rate Limiting Check
    await enforceRateLimit(req, config.rateLimiting);
    
    // 2. Platform Detection & Authentication
    const authContext = await authenticateRequest(req, config.authentication);
    
    // 3. Authorization Checks
    await authorizeRequest(authContext, config.authorization);
    
    // 4. Input Validation & Sanitization
    const validatedInput = await validateRequest(req, config.validation);
    
    // 5. Execute Business Logic
    return handler(req, authContext, validatedInput);
  };
}
```

### 3.2 Unified Authentication Context

```typescript
// lib/auth/context.ts
export interface AuthContext {
  // User Information
  user: {
    id: string;
    email: string;
    role: "business" | "customer";
    verified: boolean;
  };
  
  // Platform Context
  platform: {
    type: "web" | "mobile";
    device_id?: string;
    user_agent: string;
    ip_address: string;
  };
  
  // Authorization
  permissions: string[];
  planId: string;
  isAdmin: boolean;
  
  // Database Access
  supabase: SupabaseClient;
  adminSupabase?: SupabaseClient; // Service role for admin operations
  
  // Security Metadata
  security: {
    hmacVerified: boolean;
    csrfVerified: boolean;
    rateLimitInfo: RateLimitInfo;
    sessionId: string;
  };
}

export async function getAuthContext(req: NextRequest): Promise<AuthContext> {
  const platform = detectPlatform(req);
  
  if (platform.type === "mobile") {
    return await getMobileAuthContext(req);
  } else {
    return await getWebAuthContext(req);
  }
}
```

### 3.3 Business Logic Centralization

```typescript
// lib/services/businessLogic.ts
export class BusinessLogicService {
  constructor(private context: AuthContext) {}

  // User Profile Management
  async updateUserProfile(data: UserProfileUpdate): Promise<UserProfile> {
    // 1. Validate input data
    const validatedData = UserProfileSchema.parse(data);
    
    // 2. Check permissions
    await this.checkProfileUpdatePermissions(validatedData);
    
    // 3. Apply business rules
    const processedData = await this.applyProfileBusinessRules(validatedData);
    
    // 4. Update database
    const result = await this.updateProfileInDatabase(processedData);
    
    // 5. Invalidate relevant caches
    await this.invalidateProfileCaches(this.context.user.id);
    
    // 6. Trigger side effects
    await this.triggerProfileUpdateSideEffects(result);
    
    return result;
  }

  // Product Management
  async createProduct(productData: ProductCreate): Promise<Product> {
    // Business rule validation
    await this.validateProductLimits();
    await this.validatePlanFeatures("product_creation");
    
    // Data processing
    const processedProduct = await this.processProductData(productData);
    
    // Database operations
    const product = await this.createProductInDatabase(processedProduct);
    
    // Cache management
    await this.invalidateBusinessCaches();
    
    return product;
  }

  // Social Interactions
  async toggleLike(targetId: string, targetType: "post" | "comment"): Promise<LikeResult> {
    // Check existing like
    const existingLike = await this.findExistingLike(targetId, targetType);
    
    if (existingLike) {
      await this.removeLike(existingLike.id);
      await this.decrementLikeCount(targetId, targetType);
      return { action: "removed", count: await this.getLikeCount(targetId, targetType) };
    } else {
      await this.createLike(targetId, targetType);
      await this.incrementLikeCount(targetId, targetType);
      return { action: "added", count: await this.getLikeCount(targetId, targetType) };
    }
  }
}
```

## State Management & Caching Strategy

### 4.1 Enhanced Zustand Store Architecture

```typescript
// Shared store patterns between web and mobile
interface StoreConfig {
  // State structure
  state: StateShape;
  
  // Actions
  actions: StateActions;
  
  // Middleware configuration
  middleware: {
    persist: PersistConfig;
    immer: boolean;
    devtools: boolean;
    apiCache: ApiCacheConfig;
  };
  
  // Cache invalidation
  invalidation: {
    keys: string[];
    ttl: number;
    conditions: InvalidationCondition[];
  };
}

// Example: Enhanced Business Store
export const useBusinessStore = create<BusinessStore>()(
  devtools(
    persist(
      immer((set, get) => ({
        // State
        profile: null,
        products: [],
        analytics: null,
        loading: false,
        error: null,
        
        // API Actions with Caching
        fetchProfile: async () => {
          const cached = get().getCachedData("profile");
          if (cached && !isExpired(cached)) return cached;
          
          set((state) => { state.loading = true; });
          
          try {
            const profile = await apiClient.get("/api/business/profile");
            set((state) => {
              state.profile = profile;
              state.loading = false;
              state.setCachedData("profile", profile);
            });
          } catch (error) {
            set((state) => {
              state.error = error;
              state.loading = false;
            });
          }
        },
        
        // Cache Management
        getCachedData: (key: string) => get().cache[key],
        setCachedData: (key: string, data: any) => {
          set((state) => {
            state.cache[key] = {
              data,
              timestamp: Date.now(),
              ttl: CACHE_TTL[key]
            };
          });
        },
        
        invalidateCache: (keys: string[]) => {
          set((state) => {
            keys.forEach(key => delete state.cache[key]);
          });
        }
      })),
      {
        name: "business-store",
        partialize: (state) => ({ profile: state.profile }),
      }
    ),
    { name: "BusinessStore" }
  )
);
```

### 4.2 Smart Caching Strategy

```typescript
// lib/caching/strategy.ts
export interface CacheStrategy {
  // Cache Types
  types: {
    memory: "In-memory for frequent access";
    localStorage: "Persistent client storage";
    sessionStorage: "Session-based caching";
    apiResponse: "API response caching";
  };
  
  // Cache Policies
  policies: {
    userProfile: { ttl: "15 minutes", invalidateOn: ["profile_update"] };
    businessData: { ttl: "10 minutes", invalidateOn: ["business_update"] };
    products: { ttl: "30 minutes", invalidateOn: ["product_crud"] };
    feed: { ttl: "5 minutes", invalidateOn: ["post_crud", "social_interaction"] };
    analytics: { ttl: "1 hour", invalidateOn: ["analytics_refresh"] };
  };
  
  // Invalidation Events
  invalidationEvents: {
    profile_update: ["userProfile", "businessData"];
    product_crud: ["products", "businessData", "feed"];
    post_crud: ["feed", "userProfile"];
    social_interaction: ["feed", "analytics"];
  };
}

export class CacheManager {
  private stores: Map<string, any> = new Map();
  
  async invalidate(event: string) {
    const affectedCaches = CACHE_STRATEGY.invalidationEvents[event] || [];
    
    // Invalidate Zustand stores
    affectedCaches.forEach(cacheKey => {
      const store = this.stores.get(cacheKey);
      if (store) {
        store.getState().invalidateCache();
      }
    });
    
    // Invalidate API cache
    await this.invalidateApiCache(affectedCaches);
  }
}
```

## Migration Strategy & Implementation

### 5.1 Phase-Based Migration Approach

```
Migration Timeline (52 Weeks):

Phase 1: Security Foundation (Weeks 1-8)
├── HMAC Authentication Infrastructure
├── Device Management System  
├── Enhanced Route Protection
└── Basic Security Monitoring

Phase 2: Core API Migration (Weeks 9-20)
├── Authentication API Consolidation
├── User Profile API Migration
├── Business Logic API Creation
└── Feature Flag Implementation

Phase 3: Cross-Platform Parity (Weeks 21-32)
├── Mobile Store Architecture
├── Feature Parity Validation
├── Performance Optimization
└── Cache Strategy Implementation

Phase 4: Social & Discovery (Weeks 33-40)
├── Social Features API
├── Discovery & Search API
├── Content Management API
└── Real-time Features

Phase 5: Advanced Features (Weeks 41-48)
├── Advanced Caching (Redis)
├── Comprehensive Monitoring
├── Performance Analytics
└── Migration Completion

Phase 6: Future Foundation (Weeks 49-52)
├── API Marketplace Prep
├── White-label Infrastructure
├── Security-as-a-Service
└── Documentation & SDK
```

### 5.2 Feature Flag Implementation

```typescript
// lib/featureFlags/manager.ts
export interface FeatureFlag {
  key: string;
  enabled: boolean;
  rolloutPercentage: number;
  conditions: {
    userType?: "business" | "customer";
    planId?: string;
    platform?: "web" | "mobile";
    environment?: "development" | "staging" | "production";
  };
}

export class FeatureFlagManager {
  private flags: Map<string, FeatureFlag> = new Map();
  
  isEnabled(flagKey: string, context: UserContext): boolean {
    const flag = this.flags.get(flagKey);
    if (!flag) return false;
    
    // Check conditions
    if (flag.conditions.userType && context.userType !== flag.conditions.userType) {
      return false;
    }
    
    // Check rollout percentage
    if (flag.rolloutPercentage < 100) {
      const userHash = this.hashUser(context.userId);
      return userHash % 100 < flag.rolloutPercentage;
    }
    
    return flag.enabled;
  }
  
  // Usage in API routes
  async migrateEndpoint(req: NextRequest, context: AuthContext) {
    if (this.isEnabled("api_migration_auth", context)) {
      return await newAuthEndpoint(req, context);
    } else {
      return await legacyAuthEndpoint(req, context);
    }
  }
}
```

### 5.3 Rollback Strategy

```typescript
// lib/migration/rollback.ts
export interface RollbackPlan {
  triggers: {
    errorRate: { threshold: "5%", timeWindow: "5 minutes" };
    responseTime: { threshold: "1000ms", percentile: "95th" };
    userComplaints: { threshold: "10 reports", timeWindow: "1 hour" };
    criticalBug: { severity: "high", impact: "user_data" };
  };
  
  procedures: {
    immediate: "Feature flag disable (< 30 seconds)";
    database: "Schema rollback scripts";
    cache: "Cache invalidation and rebuild";
    monitoring: "Alert escalation and team notification";
  };
  
  validation: {
    functionality: "Automated test suite execution";
    data_integrity: "Database consistency checks";
    performance: "Load testing validation";
    user_experience: "Manual UX verification";
  };
}
```

## Performance & Monitoring

### 6.1 Performance Targets

```typescript
// Performance SLA Definition
export interface PerformanceSLA {
  apiResponseTimes: {
    target: "< 500ms for 85% of requests";
    authentication: "< 200ms for login/refresh";
    dataRetrieval: "< 300ms for profile/feed";
    dataModification: "< 400ms for CRUD operations";
    fileUpload: "< 2000ms for media uploads";
  };
  
  availability: {
    uptime: "99% with manual monitoring";
    downtime: "Planned maintenance windows allowed";
    recovery: "< 5 minutes for critical issues";
  };
  
  scalability: {
    concurrentUsers: "10,000+ without degradation";
    requestVolume: "100,000+ requests/hour";
    storageGrowth: "Support for 10TB+ media content";
  };
}
```

### 6.2 Monitoring Infrastructure

```typescript
// lib/monitoring/metrics.ts
export interface MonitoringStack {
  // API Metrics
  apiMetrics: {
    responseTime: "Percentile-based tracking";
    errorRate: "4xx/5xx error monitoring";
    throughput: "Requests per second";
    availabilityHealth: "Endpoint availability checks";
  };
  
  // Security Metrics  
  securityMetrics: {
    hmacFailures: "Invalid signature attempts";
    deviceRevocations: "Security incident tracking";
    rateLimitHits: "Abuse pattern detection";
    suspiciousActivity: "Anomaly detection alerts";
  };
  
  // Business Metrics
  businessMetrics: {
    userEngagement: "Platform usage analytics";
    featureAdoption: "New feature usage tracking";
    conversionRates: "Business goal achievement";
    platformGrowth: "User acquisition and retention";
  };
  
  // Implementation
  tools: {
    logging: "Console + structured logging";
    metrics: "Custom dashboard + alerts";
    alerting: "Email/SMS notifications";
    performance: "Response time monitoring";
  };
}
```

## Infrastructure & Deployment

### 7.1 Deployment Architecture

```
Production Deployment Stack:
┌─────────────────────────────────────────┐
│                Load Balancer             │
│           (Platform-managed)            │
└─────────────────┬───────────────────────┘
                  │
┌─────────────────▼───────────────────────┐
│            Next.js Application          │
│        (Vercel/Platform Deployment)     │
│                                         │
│ • API Routes (Centralized Layer)        │
│ • Static Site Generation               │
│ • Edge Functions                       │
└─────────────────┬───────────────────────┘
                  │
┌─────────────────▼───────────────────────┐
│              Supabase                   │
│                                         │
│ • PostgreSQL Database                  │
│ • Storage (Media Files)                │
│ • Real-time Subscriptions             │
│ • Row Level Security                   │
└─────────────────┬───────────────────────┘
                  │
┌─────────────────▼───────────────────────┐
│            Upstash Redis                │
│                                         │
│ • Rate Limiting                        │
│ • Session Management                   │
│ • Cache Layer                          │
└─────────────────────────────────────────┘
```

### 7.2 Environment Configuration

```typescript
// Environment-specific configuration
export interface EnvironmentConfig {
  development: {
    api: {
      baseUrl: "http://localhost:3000";
      rateLimiting: false;
      hmacEnforcement: false;
      verboseLogging: true;
    };
    database: {
      supabaseUrl: "dev-project-url";
      realtimeEnabled: true;
      rlsEnabled: true;
    };
  };
  
  staging: {
    api: {
      baseUrl: "https://staging.dukancard.com";
      rateLimiting: true;
      hmacEnforcement: true;
      verboseLogging: true;
    };
    database: {
      supabaseUrl: "staging-project-url";
      realtimeEnabled: true;
      rlsEnabled: true;
    };
  };
  
  production: {
    api: {
      baseUrl: "https://dukancard.com";
      rateLimiting: true;
      hmacEnforcement: true;
      verboseLogging: false;
    };
    database: {
      supabaseUrl: "prod-project-url";
      realtimeEnabled: true;
      rlsEnabled: true;
    };
  };
}
```

## Testing Strategy

### 8.1 Comprehensive Testing Framework

```typescript
// Testing pyramid for API refactoring
export interface TestingStrategy {
  // Unit Tests (70% of test coverage)
  unitTests: {
    target: "95% code coverage";
    scope: [
      "API route handlers",
      "Business logic functions", 
      "Authentication middleware",
      "Security validation",
      "Data transformation",
      "Cache management"
    ];
    frameworks: ["Jest", "React Testing Library"];
  };
  
  // Integration Tests (20% of test coverage)
  integrationTests: {
    target: "End-to-end API flows";
    scope: [
      "Authentication workflows",
      "Cross-platform feature parity",
      "Database operations",
      "File upload/download",
      "Real-time synchronization"
    ];
    frameworks: ["Jest", "Supertest", "MSW"];
  };
  
  // E2E Tests (10% of test coverage)
  e2eTests: {
    target: "Critical user journeys";
    scope: [
      "User registration/login",
      "Business onboarding",
      "Product creation",
      "Social interactions",
      "Cross-device synchronization"
    ];
    frameworks: ["Playwright (web)", "Detox (mobile)"];
  };
}
```

### 8.2 Security Testing Requirements

```typescript
// Security-focused testing protocols
export interface SecurityTestingSuite {
  authenticationTesting: {
    hmacValidation: "Test signature generation and validation";
    tokenManagement: "JWT refresh and revocation testing";
    deviceSecurity: "Device registration and revocation";
    replayProtection: "Timestamp and nonce validation";
  };
  
  authorizationTesting: {
    roleBasedAccess: "Business vs customer permission checks";
    resourceOwnership: "User can only access own data";
    adminSeparation: "Service key authentication testing";
    planRestrictions: "Feature access based on subscription";
  };
  
  inputValidationTesting: {
    sqlInjection: "Database query parameter testing";
    xssProtection: "Script injection prevention";
    dataValidation: "Schema validation and sanitization";
    fileUploadSecurity: "Media file type and size validation";
  };
  
  rateLimitingTesting: {
    apiThrottling: "Rate limit enforcement testing";
    bruteForceProtection: "Login attempt limiting";
    deviceQuarantine: "Suspicious activity handling";
    adminBypass: "Service key rate limit exceptions";
  };
}
```

## Risk Assessment & Mitigation

### 9.1 Technical Risks

| Risk | Impact | Probability | Mitigation Strategy |
|------|--------|-------------|-------------------|
| **Performance Degradation** | High | Medium | • Comprehensive benchmarking • Zustand caching optimization • Gradual rollout with monitoring |
| **Security Implementation Flaws** | Critical | Low | • Security audit by external experts • Penetration testing • Code review by security team |
| **Migration Data Loss** | Critical | Low | • Comprehensive backup strategy • Parallel data validation • Rollback procedures |
| **Cross-Platform Inconsistency** | High | Medium | • Automated parity testing • Shared business logic validation • QA testing matrix |
| **User Experience Disruption** | Medium | Medium | • Feature flags for gradual rollout • User feedback monitoring • Support team training |

### 9.2 Mitigation Strategies

```typescript
// Risk mitigation implementation
export interface RiskMitigation {
  performanceMonitoring: {
    realTimeAlerts: "Response time threshold monitoring";
    loadTesting: "Stress testing before major releases";
    cacheOptimization: "Intelligent cache warming strategies";
    databaseOptimization: "Query performance monitoring";
  };
  
  securityHardening: {
    penetrationTesting: "Quarterly security assessments";
    vulnerabilityScanning: "Automated security scanning";
    incidentResponse: "Security breach response plan";
    auditLogging: "Comprehensive activity tracking";
  };
  
  dataProtection: {
    backupStrategy: "Automated daily backups";
    migrationValidation: "Data integrity verification";
    rollbackProcedures: "Rapid rollback mechanisms";
    dataRecovery: "Point-in-time recovery capabilities";
  };
}
```

## Success Metrics & KPIs

### 10.1 Technical Success Metrics

```typescript
export interface SuccessMetrics {
  // Security Metrics
  security: {
    hmacAdoption: "100% of mobile requests using HMAC within 6 weeks";
    zeroBreaches: "Zero security incidents related to API access";
    deviceManagement: "Comprehensive device tracking and revocation";
    auditCompliance: "100% API access logging and monitoring";
  };
  
  // Performance Metrics  
  performance: {
    responseTime: "< 500ms for 85% of API requests";
    availability: "99% uptime with manual monitoring";
    concurrency: "Support 10,000+ concurrent users";
    cacheEffectiveness: "70%+ cache hit rate for frequently accessed data";
  };
  
  // Development Efficiency
  development: {
    codeReduction: "80% elimination of duplicate business logic";
    testCoverage: "95% unit test coverage for API routes";
    developmentTime: "35% reduction in feature development time";
    bugReduction: "50% reduction in platform-specific bugs";
  };
  
  // User Experience
  userExperience: {
    featureParity: "100% identical functionality across platforms";
    userSatisfaction: "Maintain current user satisfaction scores";
    performancePerception: "No degradation in perceived app performance";
    errorReduction: "90% reduction in API-related user errors";
  };
}
```

### 10.2 Business Impact Metrics

```typescript
export interface BusinessImpactMetrics {
  // Operational Efficiency
  operations: {
    maintenanceReduction: "60% reduction in platform-specific maintenance";
    incidentResponse: "50% faster incident resolution time";
    teamProductivity: "25% increase in development team velocity";
    onboardingTime: "50% reduction in new developer onboarding";
  };
  
  // Scalability Preparation  
  scalability: {
    apiFoundation: "Ready for 10x user growth without architecture changes";
    partnerIntegration: "API foundation for future marketplace";
    whiteLabel: "Infrastructure ready for multi-tenant deployment";
    internationalExpansion: "Scalable architecture for global deployment";
  };
}
```

## Conclusion & Next Steps

### 11.1 Architecture Summary

The DukanCard API Refactoring Architecture provides a comprehensive blueprint for transforming a fragmented polyrepo system into a unified, secure, and scalable platform. Key architectural achievements include:

**Security Excellence**: HMAC authentication for mobile clients, comprehensive device management, and multi-layer security protection ensuring enterprise-grade data protection.

**Performance Optimization**: Intelligent caching strategies, optimized API response times, and scalable infrastructure supporting 10,000+ concurrent users.

**Development Efficiency**: Centralized business logic, 95% test coverage, and standardized development patterns reducing feature development time by 35%.

**Future-Ready Foundation**: Scalable architecture supporting API marketplace, white-label deployment, and international expansion.

### 11.2 Implementation Readiness

✅ **Technical Feasibility**: Architecture builds on proven technologies and existing infrastructure
✅ **Security Framework**: Comprehensive security model with HMAC authentication and device management  
✅ **Performance Strategy**: Detailed caching and optimization approaches for sub-500ms response times
✅ **Migration Plan**: Phase-based implementation with feature flags and rollback procedures
✅ **Testing Strategy**: 95% coverage target with comprehensive security testing protocols

### 11.3 Immediate Next Steps

1. **Phase 1 Kickoff**: Begin HMAC authentication infrastructure implementation
2. **Security Audit**: External security review of HMAC implementation design
3. **Performance Baseline**: Establish current performance metrics for comparison
4. **Team Training**: Development team education on new architectural patterns
5. **Infrastructure Setup**: Deploy staging environment with monitoring and logging

### 11.4 Long-term Vision

This architecture establishes DukanCard as a leader in secure, scalable social commerce platforms. The unified API-first approach enables:

- **Rapid Feature Development**: Single implementation serving all platforms
- **Enterprise Security**: Bank-level authentication and data protection  
- **Global Scalability**: Architecture supporting millions of users worldwide
- **Platform Ecosystem**: Foundation for marketplace and third-party integrations
- **White-label Opportunity**: Template for rapid social commerce deployments

The investment in this architectural transformation positions DukanCard for sustained growth, enhanced security, and market leadership in the social commerce space.

---

**Document Status**: ✅ **APPROVED FOR IMPLEMENTATION**  
**Architecture Review**: Ready for technical design review and implementation planning  
**Timeline**: 52-week implementation with quarterly milestone reviews  
**Next Phase**: Security infrastructure implementation and team kickoff