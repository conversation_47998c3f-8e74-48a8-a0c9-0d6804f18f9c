import { NextRequest } from 'next/server';
import { createServiceRoleClient } from '@/utils/supabase/service-role';
import { requireAdmin } from '@/lib/utils/adminAuth';

export async function GET(request: NextRequest) {
  // Verify admin access
  const adminUser = await requireAdmin(request);
  if (adminUser instanceof Response) {
    return adminUser; // Return error response if not admin
  }

  const { searchParams } = new URL(request.url);
  const actionType = searchParams.get('action_type');
  const search = searchParams.get('search');
  const startDate = searchParams.get('start_date');
  const endDate = searchParams.get('end_date');
  const page = parseInt(searchParams.get('page') || '1');
  const limit = parseInt(searchParams.get('limit') || '50');

  const offset = (page - 1) * limit;

  try {
    const supabase = createServiceRoleClient();
    
    // Build the query
    let query = supabase
      .from('device_audit_logs')
      .select(`
        log_id,
        admin_user_id,
        action_type,
        target_device_id,
        target_user_id,
        reason,
        metadata,
        timestamp,
        ip_address,
        admin_users:users!device_audit_logs_admin_user_id_fkey(email),
        target_users:users!device_audit_logs_target_user_id_fkey(email),
        devices(device_name, platform)
      `)
      .order('timestamp', { ascending: false });

    // Apply filters
    if (actionType && actionType !== 'all') {
      query = query.eq('action_type', actionType);
    }

    if (startDate) {
      query = query.gte('timestamp', startDate);
    }

    if (endDate) {
      query = query.lte('timestamp', endDate);
    }

    if (search) {
      // Search in admin email, target user email, device name, or reason
      query = query.or(`
        admin_users.email.ilike.%${search}%,
        target_users.email.ilike.%${search}%,
        devices.device_name.ilike.%${search}%,
        reason.ilike.%${search}%
      `);
    }

    // Get total count for pagination
    const { count } = await query.select('*', { count: 'exact', head: true });

    // Get paginated results
    const { data: logs, error } = await query
      .range(offset, offset + limit - 1);

    if (error) {
      console.error('Error fetching audit logs:', error);
      return Response.json({ error: 'Failed to fetch audit logs' }, { status: 500 });
    }

    // Format the response
    const formattedLogs = logs?.map(log => ({
      log_id: log.log_id,
      admin_user_id: log.admin_user_id,
      admin_email: log.admin_users?.email,
      action_type: log.action_type,
      target_device_id: log.target_device_id,
      target_user_id: log.target_user_id,
      target_user_email: log.target_users?.email,
      reason: log.reason,
      metadata: log.metadata,
      timestamp: log.timestamp,
      ip_address: log.ip_address,
      device_details: {
        device_name: log.devices?.device_name,
        platform: log.devices?.platform
      }
    })) || [];

    return Response.json({
      logs: formattedLogs,
      pagination: {
        page,
        limit,
        total: count || 0,
        totalPages: Math.ceil((count || 0) / limit)
      },
      filters: {
        action_type: actionType,
        search,
        start_date: startDate,
        end_date: endDate
      }
    });

  } catch (error) {
    console.error('Error in audit logs API:', error);
    return Response.json({ error: 'Internal server error' }, { status: 500 });
  }
}