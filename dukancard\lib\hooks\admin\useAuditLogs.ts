import { useState, useEffect, useCallback } from 'react';

interface AuditLogEntry {
  log_id: string;
  admin_user_id: string;
  admin_email?: string;
  action_type: 'revoke' | 'quarantine' | 'bulk_revoke' | 'review';
  target_device_id?: string;
  target_user_id?: string;
  target_user_email?: string;
  reason?: string;
  metadata: {
    alert_id?: string;
    alert_type?: string;
    action?: string;
    device_name?: string;
    platform?: string;
    user_email?: string;
    ip_address?: string;
  };
  timestamp: string;
  ip_address: string;
  device_details?: {
    device_name?: string;
    platform?: string;
  };
}

interface AuditFilters {
  action_type?: string;
  search?: string;
  start_date?: string;
  end_date?: string;
  page?: number;
  limit?: number;
}

interface AuditResponse {
  logs: AuditLogEntry[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
  filters: AuditFilters;
}

export function useAuditLogs(initialFilters: AuditFilters = {}) {
  const [logs, setLogs] = useState<AuditLogEntry[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 50,
    total: 0,
    totalPages: 0
  });
  const [filters, setFilters] = useState<AuditFilters>({
    page: 1,
    limit: 50,
    ...initialFilters
  });

  const fetchLogs = useCallback(async () => {
    setLoading(true);
    setError(null);

    try {
      const searchParams = new URLSearchParams();
      
      Object.entries(filters).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== '') {
          searchParams.append(key, value.toString());
        }
      });

      const response = await fetch(`/api/admin/audit/logs?${searchParams}`);
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data: AuditResponse = await response.json();
      
      setLogs(data.logs);
      setPagination(data.pagination);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch audit logs');
      console.error('Error fetching audit logs:', err);
    } finally {
      setLoading(false);
    }
  }, [filters]);

  useEffect(() => {
    fetchLogs();
  }, [fetchLogs]);

  const updateFilters = useCallback((newFilters: Partial<AuditFilters>) => {
    setFilters(prev => ({
      ...prev,
      ...newFilters,
      page: newFilters.page ?? 1 // Reset to page 1 when filters change (unless page is explicitly set)
    }));
  }, []);

  const exportLogs = useCallback(async () => {
    try {
      const searchParams = new URLSearchParams();
      
      // Export with current filters but without pagination
      const exportFilters = { ...filters };
      delete exportFilters.page;
      delete exportFilters.limit;
      
      Object.entries(exportFilters).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== '') {
          searchParams.append(key, value.toString());
        }
      });

      const response = await fetch(`/api/admin/audit/logs/export?${searchParams}`);
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      // Create download link
      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `audit-logs-${new Date().toISOString().split('T')[0]}.csv`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);

      return { success: true };
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to export audit logs';
      console.error('Error exporting audit logs:', err);
      return { success: false, error: errorMessage };
    }
  }, [filters]);

  return {
    logs,
    loading,
    error,
    pagination,
    filters,
    updateFilters,
    exportLogs,
    refetch: fetchLogs
  };
}