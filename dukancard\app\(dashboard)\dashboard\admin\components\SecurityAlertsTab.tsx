'use client';

import { useState } from 'react';
import { useSecurityAlerts } from '@/lib/hooks/admin/useSecurityAlerts';
import { toast } from 'sonner';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  AlertTriangle,
  Shield,
  MapPin,
  Clock,
  Eye,
  CheckCircle,
  XCircle
} from 'lucide-react';

export default function SecurityAlertsTab() {
  const {
    pendingAlerts,
    reviewedAlerts,
    loading,
    error,
    reviewAlert,
  } = useSecurityAlerts();

  const handleReviewAlert = async (alertId: string, action: 'reviewed' | 'resolved') => {
    const result = await reviewAlert(alertId, action, `Alert ${action} from dashboard`);
    if (result.success) {
      toast.success(`Alert ${action} successfully`);
    } else {
      toast.error(result.error || `Failed to ${action} alert`);
    }
  };

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'high': return 'destructive';
      case 'medium': return 'default';
      case 'low': return 'secondary';
      default: return 'secondary';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending': return 'destructive';
      case 'reviewed': return 'default';
      case 'resolved': return 'secondary';
      default: return 'secondary';
    }
  };

  const getAlertIcon = (alertType: string) => {
    switch (alertType) {
      case 'location_anomaly': return MapPin;
      case 'frequency_spike': return Clock;
      case 'multiple_devices': return Shield;
      default: return AlertTriangle;
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };


  return (
    <div className="space-y-6">
      {/* Pending Alerts */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <AlertTriangle className="h-5 w-5 text-destructive" />
            Pending Security Alerts
          </CardTitle>
          <CardDescription>
            {pendingAlerts.length} alerts requiring immediate attention
          </CardDescription>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="text-center py-8">Loading alerts...</div>
          ) : error ? (
            <div className="text-center py-8 text-red-600">
              Error: {error}
            </div>
          ) : pendingAlerts.length === 0 ? (
            <div className="text-center py-8 text-muted-foreground">
              No pending security alerts
            </div>
          ) : (
            <div className="space-y-4">
              {pendingAlerts.map((alert) => {
                const AlertIcon = getAlertIcon(alert.alert_type);
                return (
                  <div key={alert.alert_id} className="border rounded-lg p-4">
                    <div className="flex items-start justify-between">
                      <div className="flex items-start gap-3">
                        <AlertIcon className="h-5 w-5 mt-1 text-muted-foreground" />
                        <div className="flex-1">
                          <div className="flex items-center gap-2 mb-2">
                            <Badge variant={getSeverityColor(alert.severity)} className="capitalize">
                              {alert.severity} Priority
                            </Badge>
                            <Badge variant="outline" className="capitalize">
                              {alert.alert_type.replace('_', ' ')}
                            </Badge>
                          </div>
                          <div className="mb-2">
                            <p className="font-medium">{alert.user_email}</p>
                            <p className="text-sm text-muted-foreground">{alert.device_name}</p>
                          </div>
                          <p className="text-sm mb-2">{alert.details.description}</p>
                          {alert.details.location && (
                            <p className="text-sm text-muted-foreground">
                              <MapPin className="h-3 w-3 inline mr-1" />
                              Location: {alert.details.location}
                            </p>
                          )}
                          {alert.details.frequency && (
                            <p className="text-sm text-muted-foreground">
                              <Clock className="h-3 w-3 inline mr-1" />
                              Requests: {alert.details.frequency}/hour
                            </p>
                          )}
                          {alert.details.deviceCount && (
                            <p className="text-sm text-muted-foreground">
                              <Shield className="h-3 w-3 inline mr-1" />
                              Active devices: {alert.details.deviceCount}
                            </p>
                          )}
                          <p className="text-xs text-muted-foreground mt-2">
                            Created: {formatDate(alert.created_at)}
                          </p>
                        </div>
                      </div>
                      <div className="flex gap-2">
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => handleReviewAlert(alert.alert_id, 'reviewed')}
                        >
                          <Eye className="h-4 w-4 mr-1" />
                          Review
                        </Button>
                        <Button
                          size="sm"
                          onClick={() => handleReviewAlert(alert.alert_id, 'resolved')}
                        >
                          <CheckCircle className="h-4 w-4 mr-1" />
                          Resolve
                        </Button>
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Reviewed Alerts */}
      {reviewedAlerts.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <CheckCircle className="h-5 w-5 text-green-600" />
              Recent Activity
            </CardTitle>
            <CardDescription>
              Recently reviewed and resolved security alerts
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {reviewedAlerts.slice(0, 5).map((alert) => {
                const AlertIcon = getAlertIcon(alert.alert_type);
                return (
                  <div key={alert.alert_id} className="flex items-center justify-between py-2 border-b last:border-0">
                    <div className="flex items-center gap-3">
                      <AlertIcon className="h-4 w-4 text-muted-foreground" />
                      <div>
                        <p className="text-sm font-medium">{alert.user_email}</p>
                        <p className="text-xs text-muted-foreground">
                          {alert.alert_type.replace('_', ' ')} • {formatDate(alert.created_at)}
                        </p>
                      </div>
                    </div>
                    <Badge variant={getStatusColor(alert.status)} className="capitalize">
                      {alert.status}
                    </Badge>
                  </div>
                );
              })}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}