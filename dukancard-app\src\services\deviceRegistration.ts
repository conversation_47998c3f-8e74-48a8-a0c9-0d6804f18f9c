/**
 * Device Registration Service for React Native
 * Handles device registration, secure credential storage, and device fingerprinting
 * Based on dukancard-app/src/services/locationStorageService.ts patterns
 */

import * as SecureStore from 'expo-secure-store';
import * as Device from 'expo-device';
import * as Crypto from 'expo-crypto';
import { Platform } from 'react-native';

// Storage keys for secure storage
const DEVICE_ID_KEY = 'dukancard_device_id';
const DEVICE_SECRET_KEY = 'dukancard_device_secret';
const HMAC_KEY_KEY = 'dukancard_hmac_key';
const DEVICE_FINGERPRINT_KEY = 'dukancard_device_fingerprint';

// Types
export interface DeviceInfo {
  deviceName: string;
  platform: 'ios' | 'android' | 'web';
  appVersion: string;
  appSignatureHash?: string;
}

export interface DeviceCredentials {
  deviceId: string;
  deviceSecret: string;
  hmacKey: string;
}

export interface DeviceRegistrationResult {
  success: boolean;
  credentials?: DeviceCredentials;
  error?: string;
}

export interface SecureStorageResult {
  success: boolean;
  error?: string;
}

/**
 * Generate platform-specific device fingerprint
 * This creates a unique identifier for the device based on available hardware info
 */
export async function generateDeviceFingerprint(): Promise<{
  success: boolean;
  fingerprint?: string;
  error?: string;
}> {
  try {
    // Check if we already have a stored fingerprint
    const existingFingerprint = await SecureStore.getItemAsync(DEVICE_FINGERPRINT_KEY);
    if (existingFingerprint) {
      return { success: true, fingerprint: existingFingerprint };
    }

    // Generate new fingerprint based on device characteristics
    const deviceInfo = {
      brand: Device.brand || 'unknown',
      manufacturer: Device.manufacturer || 'unknown',
      modelName: Device.modelName || 'unknown',
      osName: Device.osName || 'unknown',
      osVersion: Device.osVersion || 'unknown',
      platform: Platform.OS,
      // Add a random component to ensure uniqueness even on identical devices
      randomSeed: await Crypto.getRandomBytesAsync(16).then(bytes => 
        Array.from(bytes).map(b => b.toString(16).padStart(2, '0')).join('')
      ),
    };

    // Create fingerprint from device info
    const fingerprintData = JSON.stringify(deviceInfo);
    const fingerprint = await Crypto.digestStringAsync(
      Crypto.CryptoDigestAlgorithm.SHA256,
      fingerprintData,
      { encoding: Crypto.CryptoEncoding.HEX }
    );

    // Store the fingerprint for future use
    await SecureStore.setItemAsync(DEVICE_FINGERPRINT_KEY, fingerprint);

    return { success: true, fingerprint };
  } catch (error) {
    console.error('Error generating device fingerprint:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to generate device fingerprint',
    };
  }
}

/**
 * Collect device information for registration
 */
export async function collectDeviceInfo(appVersion: string): Promise<{
  success: boolean;
  deviceInfo?: DeviceInfo;
  error?: string;
}> {
  try {
    const fingerprintResult = await generateDeviceFingerprint();
    if (!fingerprintResult.success) {
      return { success: false, error: fingerprintResult.error };
    }

    const deviceInfo: DeviceInfo = {
      deviceName: Device.deviceName || `${Device.brand} ${Device.modelName}` || 'Unknown Device',
      platform: Platform.OS === 'ios' ? 'ios' : Platform.OS === 'android' ? 'android' : 'web',
      appVersion,
      appSignatureHash: fingerprintResult.fingerprint,
    };

    return { success: true, deviceInfo };
  } catch (error) {
    console.error('Error collecting device info:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to collect device info',
    };
  }
}

/**
 * Store device credentials securely in Keychain/Keystore
 */
export async function storeDeviceCredentials(credentials: DeviceCredentials): Promise<SecureStorageResult> {
  try {
    await Promise.all([
      SecureStore.setItemAsync(DEVICE_ID_KEY, credentials.deviceId),
      SecureStore.setItemAsync(DEVICE_SECRET_KEY, credentials.deviceSecret),
      SecureStore.setItemAsync(HMAC_KEY_KEY, credentials.hmacKey),
    ]);

    return { success: true };
  } catch (error) {
    console.error('Error storing device credentials:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to store device credentials',
    };
  }
}

/**
 * Retrieve stored device credentials
 */
export async function getStoredDeviceCredentials(): Promise<{
  success: boolean;
  credentials?: DeviceCredentials;
  error?: string;
}> {
  try {
    const [deviceId, deviceSecret, hmacKey] = await Promise.all([
      SecureStore.getItemAsync(DEVICE_ID_KEY),
      SecureStore.getItemAsync(DEVICE_SECRET_KEY),
      SecureStore.getItemAsync(HMAC_KEY_KEY),
    ]);

    if (!deviceId || !deviceSecret || !hmacKey) {
      return { success: true, credentials: undefined };
    }

    return {
      success: true,
      credentials: { deviceId, deviceSecret, hmacKey },
    };
  } catch (error) {
    console.error('Error retrieving device credentials:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to retrieve device credentials',
    };
  }
}

/**
 * Clear stored device credentials
 */
export async function clearDeviceCredentials(): Promise<SecureStorageResult> {
  try {
    await Promise.all([
      SecureStore.deleteItemAsync(DEVICE_ID_KEY),
      SecureStore.deleteItemAsync(DEVICE_SECRET_KEY),  
      SecureStore.deleteItemAsync(HMAC_KEY_KEY),
      SecureStore.deleteItemAsync(DEVICE_FINGERPRINT_KEY),
    ]);

    return { success: true };
  } catch (error) {
    console.error('Error clearing device credentials:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to clear device credentials',
    };
  }
}

/**
 * Check if device is already registered
 */
export async function isDeviceRegistered(): Promise<{
  success: boolean;
  isRegistered?: boolean;
  error?: string;
}> {
  try {
    const credentialsResult = await getStoredDeviceCredentials();
    if (!credentialsResult.success) {
      return { success: false, error: credentialsResult.error };
    }

    return { success: true, isRegistered: !!credentialsResult.credentials };
  } catch (error) {
    console.error('Error checking device registration:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to check device registration',
    };
  }
}

/**
 * Register device with retry mechanism
 */
export async function registerDevice(
  apiUrl: string,
  accessToken: string,
  appVersion: string,
  maxRetries: number = 3
): Promise<DeviceRegistrationResult> {
  let lastError: string = 'Unknown error';

  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      // Collect device information
      const deviceInfoResult = await collectDeviceInfo(appVersion);
      if (!deviceInfoResult.success) {
        lastError = deviceInfoResult.error || 'Failed to collect device info';
        continue;
      }

      // Make registration request
      const response = await fetch(`${apiUrl}/api/devices/register`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${accessToken}`,
        },
        body: JSON.stringify(deviceInfoResult.deviceInfo),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        lastError = errorData.error || `Registration failed with status ${response.status}`;
        
        // Don't retry on client errors (4xx), only server errors (5xx)
        if (response.status >= 400 && response.status < 500) {
          break;
        }
        continue;
      }

      const credentials: DeviceCredentials = await response.json();

      // Store credentials securely
      const storeResult = await storeDeviceCredentials(credentials);
      if (!storeResult.success) {
        lastError = storeResult.error || 'Failed to store credentials';
        continue;
      }

      // Registration successful
      return { success: true, credentials };

    } catch (error) {
      lastError = error instanceof Error ? error.message : 'Network error';
      console.warn(`Device registration attempt ${attempt} failed:`, lastError);
      
      // Wait before retry (exponential backoff)
      if (attempt < maxRetries) {
        await new Promise(resolve => setTimeout(resolve, Math.pow(2, attempt) * 1000));
      }
    }
  }

  return { success: false, error: `Registration failed after ${maxRetries} attempts: ${lastError}` };
}

/**
 * Re-register device (useful when credentials are lost or expired)
 */
export async function reRegisterDevice(
  apiUrl: string,
  accessToken: string,
  appVersion: string
): Promise<DeviceRegistrationResult> {
  try {
    // Clear existing credentials
    await clearDeviceCredentials();
    
    // Register with fresh credentials
    return await registerDevice(apiUrl, accessToken, appVersion);
  } catch (error) {
    console.error('Error re-registering device:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to re-register device',
    };
  }
}

/**
 * Validate stored credentials by checking if they exist and are well-formed
 */
export async function validateStoredCredentials(): Promise<{
  success: boolean;
  isValid?: boolean;
  error?: string;
}> {
  try {
    const credentialsResult = await getStoredDeviceCredentials();
    if (!credentialsResult.success) {
      return { success: false, error: credentialsResult.error };
    }

    const credentials = credentialsResult.credentials;
    if (!credentials) {
      return { success: true, isValid: false };
    }

    // Validate credential format
    const isValid = 
      typeof credentials.deviceId === 'string' && credentials.deviceId.length > 0 &&
      typeof credentials.deviceSecret === 'string' && credentials.deviceSecret.length === 64 && // 32 bytes hex
      typeof credentials.hmacKey === 'string' && credentials.hmacKey.length === 64; // 32 bytes hex

    return { success: true, isValid };
  } catch (error) {
    console.error('Error validating stored credentials:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to validate credentials',
    };
  }
}