import { redirect } from 'next/navigation';
import { createClient } from '@/utils/supabase/server';
import AdminDashboardClient from './components/AdminDashboardClient';

export default async function AdminDashboard() {
  const supabase = await createClient();
  
  const { data: { user }, error: userError } = await supabase.auth.getUser();
  
  if (userError || !user) {
    redirect('/auth/login');
  }

  // Check if user has admin role
  const userMetadata = user.user_metadata || {};
  const userRole = userMetadata.role;
  
  if (userRole !== 'admin') {
    redirect('/dashboard');
  }

  return (
    <div className="container mx-auto py-6">
      <div className="mb-6">
        <h1 className="text-3xl font-bold">Admin Dashboard</h1>
        <p className="text-muted-foreground">
          Device management and security administration
        </p>
      </div>
      <AdminDashboardClient />
    </div>
  );
}