'use client';

import { useState } from 'react';
import { useAuditLogs } from '@/lib/hooks/admin/useAuditLogs';
import { toast } from 'sonner';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from '@/components/ui/table';
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { 
  FileText,
  Search,
  Download,
  Shield,
  ShieldOff,
  Eye,
  AlertTriangle
} from 'lucide-react';

export default function AuditLogsTab() {
  const [searchTerm, setSearchTerm] = useState('');
  const [actionFilter, setActionFilter] = useState<string>('all');

  const {
    logs,
    loading,
    error,
    pagination,
    updateFilters,
    exportLogs,
  } = useAuditLogs({
    search: searchTerm || undefined,
    action_type: actionFilter !== 'all' ? actionFilter : undefined,
  });

  const handleSearchChange = (value: string) => {
    setSearchTerm(value);
    updateFilters({ search: value || undefined, page: 1 });
  };

  const handleActionFilterChange = (value: string) => {
    setActionFilter(value);
    updateFilters({ action_type: value !== 'all' ? value : undefined, page: 1 });
  };

  const getActionIcon = (actionType: string) => {
    switch (actionType) {
      case 'revoke': return ShieldOff;
      case 'quarantine': return Shield;
      case 'bulk_revoke': return ShieldOff;
      case 'review': return Eye;
      default: return AlertTriangle;
    }
  };

  const getActionColor = (actionType: string) => {
    switch (actionType) {
      case 'revoke': return 'destructive';
      case 'quarantine': return 'default';
      case 'bulk_revoke': return 'destructive';
      case 'review': return 'secondary';
      default: return 'secondary';
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    });
  };

  const handleExportLogs = async () => {
    const result = await exportLogs();
    if (result.success) {
      toast.success('Audit logs exported successfully');
    } else {
      toast.error(result.error || 'Failed to export audit logs');
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <FileText className="h-5 w-5" />
            Audit Logs
          </div>
          <Button onClick={handleExportLogs} variant="outline" size="sm">
            <Download className="h-4 w-4 mr-2" />
            Export Logs
          </Button>
        </CardTitle>
        <CardDescription>
          Complete audit trail of all administrative actions and security events
        </CardDescription>
      </CardHeader>
      <CardContent>
        {/* Filters */}
        <div className="flex flex-col sm:flex-row gap-4 mb-6">
          <div className="flex-1 relative">
            <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search logs by admin, user, device, or reason..."
              value={searchTerm}
              onChange={(e) => handleSearchChange(e.target.value)}
              className="pl-8"
            />
          </div>
          <Select value={actionFilter} onValueChange={handleActionFilterChange}>
            <SelectTrigger className="w-[160px]">
              <SelectValue placeholder="Action Type" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Actions</SelectItem>
              <SelectItem value="revoke">Device Revoke</SelectItem>
              <SelectItem value="quarantine">Quarantine</SelectItem>
              <SelectItem value="bulk_revoke">Bulk Revoke</SelectItem>
              <SelectItem value="review">Review</SelectItem>
            </SelectContent>
          </Select>
        </div>

        {/* Audit Log Table */}
        <div className="rounded-md border">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Timestamp</TableHead>
                <TableHead>Admin</TableHead>
                <TableHead>Action</TableHead>
                <TableHead>Target</TableHead>
                <TableHead>Reason</TableHead>
                <TableHead>IP Address</TableHead>
                <TableHead>Details</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {loading ? (
                <TableRow>
                  <TableCell colSpan={7} className="text-center py-8">
                    Loading audit logs...
                  </TableCell>
                </TableRow>
              ) : error ? (
                <TableRow>
                  <TableCell colSpan={7} className="text-center py-8 text-red-600">
                    Error: {error}
                  </TableCell>
                </TableRow>
              ) : logs.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={7} className="text-center py-8">
                    No audit logs found matching your criteria
                  </TableCell>
                </TableRow>
              ) : (
                logs.map((log) => {
                  const ActionIcon = getActionIcon(log.action_type);
                  return (
                    <TableRow key={log.log_id}>
                      <TableCell className="font-mono text-xs">
                        {formatDate(log.timestamp)}
                      </TableCell>
                      <TableCell className="font-medium">
                        {log.admin_email}
                      </TableCell>
                      <TableCell>
                        <Badge variant={getActionColor(log.action_type)} className="capitalize">
                          <ActionIcon className="h-3 w-3 mr-1" />
                          {log.action_type.replace('_', ' ')}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        {log.target_user_email ? (
                          <div>
                            <p className="text-sm font-medium">{log.target_user_email}</p>
                            {log.device_details?.device_name && (
                              <p className="text-xs text-muted-foreground">
                                {log.device_details.device_name}
                              </p>
                            )}
                          </div>
                        ) : (
                          <span className="text-muted-foreground">Bulk Action</span>
                        )}
                      </TableCell>
                      <TableCell className="max-w-[200px]">
                        <p className="text-sm truncate" title={log.reason}>
                          {log.reason || 'No reason provided'}
                        </p>
                      </TableCell>
                      <TableCell className="font-mono text-xs">
                        {log.ip_address}
                      </TableCell>
                      <TableCell>
                        {log.device_details?.platform && (
                          <Badge variant="outline" className="text-xs">
                            {log.device_details.platform}
                          </Badge>
                        )}
                      </TableCell>
                    </TableRow>
                  );
                })
              )}
            </TableBody>
          </Table>
        </div>

        {/* Pagination */}
        <div className="flex items-center justify-between px-2 py-4">
          <div className="text-sm text-muted-foreground">
            Showing {((pagination.page - 1) * pagination.limit) + 1}-{Math.min(pagination.page * pagination.limit, pagination.total)} of {pagination.total} entries
          </div>
          <div className="flex items-center space-x-2">
            <Button 
              variant="outline" 
              size="sm" 
              disabled={pagination.page <= 1}
              onClick={() => updateFilters({ page: pagination.page - 1 })}
            >
              Previous
            </Button>
            <Button 
              variant="outline" 
              size="sm" 
              disabled={pagination.page >= pagination.totalPages}
              onClick={() => updateFilters({ page: pagination.page + 1 })}
            >
              Next
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}