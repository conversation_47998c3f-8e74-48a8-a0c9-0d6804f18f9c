import { useState, useEffect, useCallback } from 'react';

interface SecurityAlert {
  alert_id: string;
  device_id: string;
  user_id: string;
  user_email?: string;
  device_name?: string;
  alert_type: 'location_anomaly' | 'frequency_spike' | 'multiple_devices' | 'admin_quarantine';
  severity: 'low' | 'medium' | 'high';
  details: {
    description: string;
    location?: string;
    frequency?: number;
    deviceCount?: number;
    reason?: string;
    quarantined_by?: string;
  };
  created_at: string;
  reviewed_at?: string;
  reviewed_by?: string;
  status: 'pending' | 'reviewed' | 'resolved';
}

interface AlertFilters {
  status?: string;
  severity?: string;
  alert_type?: string;
  page?: number;
  limit?: number;
}

interface AlertResponse {
  alerts: SecurityAlert[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
  filters: AlertFilters;
}

export function useSecurityAlerts(initialFilters: AlertFilters = {}) {
  const [alerts, setAlerts] = useState<SecurityAlert[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 20,
    total: 0,
    totalPages: 0
  });
  const [filters, setFilters] = useState<AlertFilters>({
    page: 1,
    limit: 20,
    status: 'pending',
    ...initialFilters
  });

  const fetchAlerts = useCallback(async () => {
    setLoading(true);
    setError(null);

    try {
      const searchParams = new URLSearchParams();
      
      Object.entries(filters).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== '') {
          searchParams.append(key, value.toString());
        }
      });

      const response = await fetch(`/api/admin/security/alerts?${searchParams}`);
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data: AlertResponse = await response.json();
      
      setAlerts(data.alerts);
      setPagination(data.pagination);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch security alerts');
      console.error('Error fetching security alerts:', err);
    } finally {
      setLoading(false);
    }
  }, [filters]);

  useEffect(() => {
    fetchAlerts();
  }, [fetchAlerts]);

  const updateFilters = useCallback((newFilters: Partial<AlertFilters>) => {
    setFilters(prev => ({
      ...prev,
      ...newFilters,
      page: newFilters.page ?? 1 // Reset to page 1 when filters change (unless page is explicitly set)
    }));
  }, []);

  const reviewAlert = useCallback(async (alertId: string, action: 'reviewed' | 'resolved', reason?: string) => {
    try {
      const response = await fetch('/api/admin/security/alerts', {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          alert_id: alertId,
          action,
          reason
        }),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();
      
      if (result.success) {
        // Update local state
        setAlerts(prev => prev.map(alert => 
          alert.alert_id === alertId 
            ? { 
                ...alert, 
                status: action,
                reviewed_at: result.reviewed_at,
                reviewed_by: result.reviewed_by
              }
            : alert
        ));
        return { success: true };
      } else {
        throw new Error(result.error || `Failed to ${action} alert`);
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : `Failed to ${action} alert`;
      console.error(`Error ${action}ing alert:`, err);
      return { success: false, error: errorMessage };
    }
  }, []);

  const pendingAlerts = alerts.filter(alert => alert.status === 'pending');
  const reviewedAlerts = alerts.filter(alert => alert.status !== 'pending');

  return {
    alerts,
    pendingAlerts,
    reviewedAlerts,
    loading,
    error,
    pagination,
    filters,
    updateFilters,
    reviewAlert,
    refetch: fetchAlerts
  };
}