import { useState, useEffect, useCallback } from 'react';

interface Device {
  device_id: string;
  user_id: string;
  user_email?: string;
  device_name: string;
  platform: 'ios' | 'android';
  app_version: string;
  registered_at: string;
  last_activity: string;
  is_revoked: boolean;
  risk_score?: number;
}

interface DeviceFilters {
  search?: string;
  platform?: string;
  status?: string;
  page?: number;
  limit?: number;
}

interface DeviceResponse {
  devices: Device[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
  filters: DeviceFilters;
}

export function useAdminDevices(initialFilters: DeviceFilters = {}) {
  const [devices, setDevices] = useState<Device[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 20,
    total: 0,
    totalPages: 0
  });
  const [filters, setFilters] = useState<DeviceFilters>({
    page: 1,
    limit: 20,
    ...initialFilters
  });

  const fetchDevices = useCallback(async () => {
    setLoading(true);
    setError(null);

    try {
      const searchParams = new URLSearchParams();
      
      Object.entries(filters).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== '') {
          searchParams.append(key, value.toString());
        }
      });

      const response = await fetch(`/api/admin/devices/list?${searchParams}`);
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data: DeviceResponse = await response.json();
      
      setDevices(data.devices);
      setPagination(data.pagination);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch devices');
      console.error('Error fetching devices:', err);
    } finally {
      setLoading(false);
    }
  }, [filters]);

  useEffect(() => {
    fetchDevices();
  }, [fetchDevices]);

  const updateFilters = useCallback((newFilters: Partial<DeviceFilters>) => {
    setFilters(prev => ({
      ...prev,
      ...newFilters,
      page: newFilters.page ?? 1 // Reset to page 1 when filters change (unless page is explicitly set)
    }));
  }, []);

  const revokeDevice = useCallback(async (deviceId: string, reason?: string) => {
    try {
      const response = await fetch('/api/admin/devices/revoke', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          device_ids: [deviceId],
          reason
        }),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();
      
      if (result.success) {
        // Update local state
        setDevices(prev => prev.map(device => 
          device.device_id === deviceId 
            ? { ...device, is_revoked: true }
            : device
        ));
        return { success: true };
      } else {
        throw new Error(result.errors?.[0]?.error || 'Failed to revoke device');
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to revoke device';
      console.error('Error revoking device:', err);
      return { success: false, error: errorMessage };
    }
  }, []);

  const quarantineDevice = useCallback(async (deviceId: string, reason?: string) => {
    try {
      const response = await fetch('/api/admin/devices/quarantine', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          device_id: deviceId,
          reason
        }),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();
      
      if (result.success) {
        // Refresh the devices list to get updated data
        fetchDevices();
        return { success: true };
      } else {
        throw new Error(result.error || 'Failed to quarantine device');
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to quarantine device';
      console.error('Error quarantining device:', err);
      return { success: false, error: errorMessage };
    }
  }, [fetchDevices]);

  return {
    devices,
    loading,
    error,
    pagination,
    filters,
    updateFilters,
    revokeDevice,
    quarantineDevice,
    refetch: fetchDevices
  };
}