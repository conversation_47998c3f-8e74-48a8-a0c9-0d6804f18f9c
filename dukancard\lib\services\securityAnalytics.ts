import { createServiceRoleClient } from '@/utils/supabase/service-role';

interface DeviceActivity {
  device_id: string;
  user_id: string;
  timestamp: string;
  activity_type: 'login' | 'api_request' | 'logout' | 'token_refresh';
  ip_address: string;
  location?: {
    country: string;
    city: string;
    latitude: number;
    longitude: number;
  };
  user_agent: string;
  success: boolean;
}

interface AnomalyPattern {
  type: 'location_anomaly' | 'frequency_spike' | 'multiple_devices' | 'unusual_timing' | 'suspicious_ip';
  severity: 'low' | 'medium' | 'high';
  confidence: number; // 0-100
  details: any;
}

interface UserProfile {
  user_id: string;
  typical_locations: string[];
  typical_hours: number[];
  typical_frequency: number;
  typical_devices: string[];
  last_updated: string;
}

/**
 * Security analytics service for anomaly detection and pattern analysis
 */
export class SecurityAnalytics {
  private supabase = createServiceRoleClient();

  /**
   * Analyze device activity for anomalies
   */
  async analyzeDeviceActivity(deviceId: string): Promise<AnomalyPattern[]> {
    try {
      const anomalies: AnomalyPattern[] = [];

      // Get recent activity for this device
      const recentActivity = await this.getRecentDeviceActivity(deviceId, 7); // Last 7 days
      
      if (recentActivity.length === 0) {
        return anomalies;
      }

      const userId = recentActivity[0].user_id;
      const userProfile = await this.getUserProfile(userId);

      // 1. Location anomaly detection
      const locationAnomalies = this.detectLocationAnomalies(recentActivity, userProfile);
      anomalies.push(...locationAnomalies);

      // 2. Frequency spike detection
      const frequencyAnomalies = this.detectFrequencySpikes(recentActivity, userProfile);
      anomalies.push(...frequencyAnomalies);

      // 3. Multiple devices detection
      const multiDeviceAnomalies = await this.detectMultipleDevices(userId, deviceId);
      anomalies.push(...multiDeviceAnomalies);

      // 4. Unusual timing detection
      const timingAnomalies = this.detectUnusualTiming(recentActivity, userProfile);
      anomalies.push(...timingAnomalies);

      // 5. Suspicious IP detection
      const ipAnomalies = await this.detectSuspiciousIPs(recentActivity);
      anomalies.push(...ipAnomalies);

      return anomalies.filter(anomaly => anomaly.confidence >= 70); // Only high confidence anomalies

    } catch (error) {
      console.error('Error analyzing device activity:', error);
      return [];
    }
  }

  /**
   * Get recent activity for a device (mock implementation - would integrate with actual activity logs)
   */
  private async getRecentDeviceActivity(deviceId: string, days: number): Promise<DeviceActivity[]> {
    // In a real implementation, this would fetch from an activity log table
    // For now, we'll create some mock data based on device info
    
    const { data: device } = await this.supabase
      .from('devices')
      .select('user_id, last_seen_at')
      .eq('device_id', deviceId)
      .single();

    if (!device) return [];

    // Mock recent activity data
    const now = new Date();
    const activities: DeviceActivity[] = [];
    
    for (let i = 0; i < Math.min(days * 10, 50); i++) {
      const timestamp = new Date(now.getTime() - (i * 2 * 60 * 60 * 1000)); // Every 2 hours
      
      activities.push({
        device_id: deviceId,
        user_id: device.user_id,
        timestamp: timestamp.toISOString(),
        activity_type: Math.random() > 0.7 ? 'login' : 'api_request',
        ip_address: this.generateMockIP(),
        location: this.generateMockLocation(),
        user_agent: 'MockUserAgent/1.0',
        success: Math.random() > 0.05 // 95% success rate
      });
    }

    return activities;
  }

  /**
   * Get or create user behavioral profile
   */
  private async getUserProfile(userId: string): Promise<UserProfile> {
    // Mock user profile - in reality this would be built from historical data
    return {
      user_id: userId,
      typical_locations: ['US', 'Canada'],
      typical_hours: [8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18], // 8 AM to 6 PM
      typical_frequency: 20, // requests per day
      typical_devices: [], // Would be populated from device history
      last_updated: new Date().toISOString()
    };
  }

  /**
   * Detect location-based anomalies
   */
  private detectLocationAnomalies(activities: DeviceActivity[], profile: UserProfile): AnomalyPattern[] {
    const anomalies: AnomalyPattern[] = [];
    const recentLocations = activities
      .filter(a => a.location)
      .map(a => a.location!.country)
      .filter((country, index, arr) => arr.indexOf(country) === index); // unique

    for (const location of recentLocations) {
      if (!profile.typical_locations.includes(location)) {
        const confidence = this.calculateLocationAnomalyConfidence(activities, location, profile);
        
        if (confidence >= 70) {
          anomalies.push({
            type: 'location_anomaly',
            severity: confidence >= 90 ? 'high' : confidence >= 80 ? 'medium' : 'low',
            confidence,
            details: {
              unusual_location: location,
              typical_locations: profile.typical_locations,
              distance_from_typical: this.calculateLocationDistance(location, profile.typical_locations[0])
            }
          });
        }
      }
    }

    return anomalies;
  }

  /**
   * Detect frequency spikes
   */
  private detectFrequencySpikes(activities: DeviceActivity[], profile: UserProfile): AnomalyPattern[] {
    const anomalies: AnomalyPattern[] = [];
    
    // Group activities by day
    const activityByDay = new Map<string, number>();
    
    for (const activity of activities) {
      const day = activity.timestamp.split('T')[0];
      activityByDay.set(day, (activityByDay.get(day) || 0) + 1);
    }

    for (const [day, count] of activityByDay.entries()) {
      const spikeThreshold = profile.typical_frequency * 3; // 3x normal frequency
      
      if (count > spikeThreshold) {
        const confidence = Math.min(95, 60 + ((count / profile.typical_frequency) * 10));
        
        anomalies.push({
          type: 'frequency_spike',
          severity: confidence >= 90 ? 'high' : confidence >= 80 ? 'medium' : 'low',
          confidence,
          details: {
            date: day,
            activity_count: count,
            typical_count: profile.typical_frequency,
            spike_multiplier: Math.round(count / profile.typical_frequency * 10) / 10
          }
        });
      }
    }

    return anomalies;
  }

  /**
   * Detect multiple simultaneous devices
   */
  private async detectMultipleDevices(userId: string, currentDeviceId: string): Promise<AnomalyPattern[]> {
    const anomalies: AnomalyPattern[] = [];
    
    try {
      // Get all active devices for this user
      const { data: devices } = await this.supabase
        .from('devices')
        .select('device_id, last_seen_at, platform')
        .eq('user_id', userId)
        .eq('revoked', false);

      if (!devices || devices.length <= 2) return anomalies; // Normal to have 2 devices

      // Check for simultaneous activity (within 5 minutes)
      const now = new Date();
      const recentlyActive = devices.filter(device => {
        if (!device.last_seen_at) return false;
        const lastSeen = new Date(device.last_seen_at);
        return (now.getTime() - lastSeen.getTime()) < 5 * 60 * 1000; // 5 minutes
      });

      if (recentlyActive.length >= 4) {
        const confidence = Math.min(95, 70 + (recentlyActive.length * 5));
        
        anomalies.push({
          type: 'multiple_devices',
          severity: confidence >= 90 ? 'high' : 'medium',
          confidence,
          details: {
            simultaneous_devices: recentlyActive.length,
            device_platforms: recentlyActive.map(d => d.platform),
            current_device: currentDeviceId
          }
        });
      }

    } catch (error) {
      console.error('Error detecting multiple devices:', error);
    }

    return anomalies;
  }

  /**
   * Detect unusual timing patterns
   */
  private detectUnusualTiming(activities: DeviceActivity[], profile: UserProfile): AnomalyPattern[] {
    const anomalies: AnomalyPattern[] = [];
    
    const unusualActivities = activities.filter(activity => {
      const hour = new Date(activity.timestamp).getHours();
      return !profile.typical_hours.includes(hour);
    });

    if (unusualActivities.length > activities.length * 0.3) { // More than 30% unusual timing
      const confidence = Math.min(90, 60 + (unusualActivities.length / activities.length * 50));
      
      anomalies.push({
        type: 'unusual_timing',
        severity: confidence >= 85 ? 'medium' : 'low',
        confidence,
        details: {
          unusual_activity_count: unusualActivities.length,
          total_activities: activities.length,
          unusual_hours: [...new Set(unusualActivities.map(a => new Date(a.timestamp).getHours()))],
          typical_hours: profile.typical_hours
        }
      });
    }

    return anomalies;
  }

  /**
   * Detect suspicious IP addresses
   */
  private async detectSuspiciousIPs(activities: DeviceActivity[]): Promise<AnomalyPattern[]> {
    const anomalies: AnomalyPattern[] = [];
    
    // Known malicious IP patterns (simplified)
    const suspiciousPatterns = [
      /^10\.0\.0\./, // Tor exit nodes (simplified pattern)
      /^192\.168\.1\.1$/, // Default router (potentially compromised)
    ];

    const suspiciousIPs = activities
      .map(a => a.ip_address)
      .filter(ip => suspiciousPatterns.some(pattern => pattern.test(ip)));

    if (suspiciousIPs.length > 0) {
      anomalies.push({
        type: 'suspicious_ip',
        severity: 'high',
        confidence: 85,
        details: {
          suspicious_ips: [...new Set(suspiciousIPs)],
          patterns_matched: 'Known malicious IP patterns'
        }
      });
    }

    return anomalies;
  }

  /**
   * Create security alert for detected anomalies
   */
  async createSecurityAlert(
    deviceId: string, 
    userId: string, 
    anomalies: AnomalyPattern[]
  ): Promise<void> {
    try {
      for (const anomaly of anomalies) {
        await this.supabase
          .from('suspicious_activity_alerts')
          .insert({
            device_id: deviceId,
            user_id: userId,
            alert_type: anomaly.type,
            severity: anomaly.severity,
            details: {
              description: this.generateAnomalyDescription(anomaly),
              confidence: anomaly.confidence,
              ...anomaly.details
            },
            status: 'pending'
          });
      }
    } catch (error) {
      console.error('Error creating security alert:', error);
    }
  }

  /**
   * Generate human-readable anomaly description
   */
  private generateAnomalyDescription(anomaly: AnomalyPattern): string {
    switch (anomaly.type) {
      case 'location_anomaly':
        return `Login detected from unusual location: ${anomaly.details.unusual_location}`;
      case 'frequency_spike':
        return `Unusual spike in activity: ${anomaly.details.spike_multiplier}x normal frequency`;
      case 'multiple_devices':
        return `${anomaly.details.simultaneous_devices} devices active simultaneously`;
      case 'unusual_timing':
        return `Activity detected outside typical hours (${anomaly.details.unusual_activity_count} activities)`;
      case 'suspicious_ip':
        return `Activity from suspicious IP addresses: ${anomaly.details.suspicious_ips.join(', ')}`;
      default:
        return 'Suspicious activity detected';
    }
  }

  /**
   * Calculate risk score for a device
   */
  async calculateDeviceRiskScore(deviceId: string): Promise<number> {
    try {
      const anomalies = await this.analyzeDeviceActivity(deviceId);
      
      if (anomalies.length === 0) return 10; // Base score for normal devices

      let riskScore = 10;
      
      for (const anomaly of anomalies) {
        const severityMultiplier = anomaly.severity === 'high' ? 3 : anomaly.severity === 'medium' ? 2 : 1;
        const confidenceWeight = anomaly.confidence / 100;
        riskScore += (20 * severityMultiplier * confidenceWeight);
      }

      return Math.min(100, Math.round(riskScore));

    } catch (error) {
      console.error('Error calculating risk score:', error);
      return 50; // Default medium risk
    }
  }

  // Helper methods
  private generateMockIP(): string {
    return `${Math.floor(Math.random() * 256)}.${Math.floor(Math.random() * 256)}.${Math.floor(Math.random() * 256)}.${Math.floor(Math.random() * 256)}`;
  }

  private generateMockLocation() {
    const locations = [
      { country: 'US', city: 'New York', latitude: 40.7128, longitude: -74.0060 },
      { country: 'Canada', city: 'Toronto', latitude: 43.6532, longitude: -79.3832 },
      { country: 'UK', city: 'London', latitude: 51.5074, longitude: -0.1278 },
      { country: 'Germany', city: 'Berlin', latitude: 52.5200, longitude: 13.4050 },
    ];
    return locations[Math.floor(Math.random() * locations.length)];
  }

  private calculateLocationAnomalyConfidence(
    activities: DeviceActivity[], 
    location: string, 
    profile: UserProfile
  ): number {
    const locationActivities = activities.filter(a => a.location?.country === location);
    const recentLocationCount = locationActivities.length;
    
    // Higher confidence for more activities from unusual location
    return Math.min(95, 70 + (recentLocationCount * 5));
  }

  private calculateLocationDistance(location1: string, location2: string): number {
    // Simplified distance calculation - in reality would use geolocation
    const distances: { [key: string]: number } = {
      'US-UK': 3500,
      'US-Germany': 4000,
      'Canada-UK': 3200,
      'Canada-Germany': 3800
    };
    
    const key1 = `${location1}-${location2}`;
    const key2 = `${location2}-${location1}`;
    
    return distances[key1] || distances[key2] || 1000;
  }

  /**
   * Run automated anomaly detection for all active devices
   */
  async runAutomatedAnomalyDetection(): Promise<void> {
    try {
      // Get all active devices
      const { data: devices } = await this.supabase
        .from('devices')
        .select('device_id, user_id')
        .eq('revoked', false)
        .not('last_seen_at', 'is', null);

      if (!devices) return;

      for (const device of devices) {
        try {
          const anomalies = await this.analyzeDeviceActivity(device.device_id);
          
          if (anomalies.length > 0) {
            await this.createSecurityAlert(device.device_id, device.user_id, anomalies);
            
            // Update device risk score
            const riskScore = await this.calculateDeviceRiskScore(device.device_id);
            await this.supabase
              .from('devices')
              .update({ risk_score: riskScore })
              .eq('device_id', device.device_id);
          }
        } catch (error) {
          console.error(`Error analyzing device ${device.device_id}:`, error);
        }
      }

    } catch (error) {
      console.error('Error in automated anomaly detection:', error);
    }
  }
}

export const securityAnalytics = new SecurityAnalytics();