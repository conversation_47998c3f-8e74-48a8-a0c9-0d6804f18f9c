import { NextRequest, NextResponse } from "next/server";
import { z } from "zod";
import { hashSecret } from "@/lib/security/hashing";
import { withProtectedRoute } from "@/lib/middleware/routeProtection";
import { AuthContext } from "@/lib/auth/getAuthContext";
import { createServiceRoleClient } from "@/utils/supabase/service-role";
import crypto from "crypto";
// Device registration handlers

const registerSchema = z.object({
  deviceName: z.string().min(1, "Device name is required"),
  platform: z.enum(["ios", "android", "web"], {
    errorMap: () => ({
      message: "Platform must be 'ios', 'android', or 'web'",
    }),
  }),
  appVersion: z.string().min(1, "App version is required"),
  appSignatureHash: z.string().optional(),
});

// POST handler for device registration
async function handleDeviceRegister(req: NextRequest, context: AuthContext) {
  try {
    // Get Supabase client
    const supabase = context.supabase || createServiceRoleClient();

    if (!context.userId) {
      return new NextResponse(
        JSON.stringify({ error: "User not authenticated" }),
        {
          status: 401,
          headers: { "Content-Type": "application/json" },
        }
      );
    }

    // 3. Validate request body
    const body = await req.json();
    const validation = registerSchema.safeParse(body);
    if (!validation.success) {
      return new NextResponse(
        JSON.stringify({
          error: "Validation failed",
          details: validation.error.issues,
        }),
        {
          status: 400,
          headers: { "Content-Type": "application/json" },
        }
      );
    }

    const { deviceName, platform, appVersion, appSignatureHash } = validation.data;

    // 4. Generate device secret (64-character hex string) and HMAC key
    const deviceSecret = crypto.randomBytes(32).toString("hex");
    const hmacKey = crypto.randomBytes(32).toString("hex");

    // 5. Hash the device secret for storage (HMAC key is stored as plaintext for verification)
    const deviceSecretHash = await hashSecret(deviceSecret);

    // 6. Insert into database using service role client
    const { data, error } = await supabase
      .from("devices")
      .insert({
        user_id: context.userId,
        device_name: deviceName,
        platform: platform,
        app_version: appVersion,
        device_secret_hash: deviceSecretHash,
        hmac_key_hash: hmacKey, // Store HMAC key for signature verification
        app_signature_hash: appSignatureHash || null,
      })
      .select("device_id")
      .single();

    if (error) {
      console.error("Database error during device registration:", error);
      return new NextResponse(
        JSON.stringify({ error: "Internal Server Error" }),
        {
          status: 500,
          headers: { "Content-Type": "application/json" },
        }
      );
    }

    // 7. Return the new device ID and both secrets (only time they're ever sent)
    return NextResponse.json({
      deviceId: data.device_id,
      deviceSecret: deviceSecret,
      hmacKey: hmacKey,
    });
  } catch (error) {
    console.error("Unexpected error in device registration:", error);
    return new NextResponse(
      JSON.stringify({ error: "Internal Server Error" }),
      {
        status: 500,
        headers: { "Content-Type": "application/json" },
      }
    );
  }
}

/**
 * POST /api/devices/register - Register a new device
 * Protected route requiring authentication but NOT HMAC (since this provides HMAC keys)
 */
export const POST = withProtectedRoute(handleDeviceRegister, {
  requireHMAC: false,
  operation: "device_register",
});
