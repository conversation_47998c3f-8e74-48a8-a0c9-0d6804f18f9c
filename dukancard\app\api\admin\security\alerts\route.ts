import { NextRequest } from 'next/server';
import { createServiceRoleClient } from '@/utils/supabase/service-role';
import { requireAdmin } from '@/lib/utils/adminAuth';

export async function GET(request: NextRequest) {
  // Verify admin access
  const adminUser = await requireAdmin(request);
  if (adminUser instanceof Response) {
    return adminUser; // Return error response if not admin
  }

  const { searchParams } = new URL(request.url);
  const status = searchParams.get('status') || 'pending';
  const severity = searchParams.get('severity');
  const alertType = searchParams.get('alert_type');
  const page = parseInt(searchParams.get('page') || '1');
  const limit = parseInt(searchParams.get('limit') || '20');

  const offset = (page - 1) * limit;

  try {
    const supabase = createServiceRoleClient();
    
    // Build the query
    let query = supabase
      .from('suspicious_activity_alerts')
      .select(`
        alert_id,
        device_id,
        user_id,
        alert_type,
        severity,
        details,
        created_at,
        reviewed_at,
        reviewed_by,
        status,
        devices!inner(
          device_name,
          platform
        ),
        users!inner(
          email
        )
      `)
      .order('created_at', { ascending: false });

    // Apply filters
    if (status !== 'all') {
      query = query.eq('status', status);
    }

    if (severity) {
      query = query.eq('severity', severity);
    }

    if (alertType) {
      query = query.eq('alert_type', alertType);
    }

    // Get total count for pagination
    const { count } = await query.select('*', { count: 'exact', head: true });

    // Get paginated results
    const { data: alerts, error } = await query
      .range(offset, offset + limit - 1);

    if (error) {
      console.error('Error fetching security alerts:', error);
      return Response.json({ error: 'Failed to fetch security alerts' }, { status: 500 });
    }

    // Format the response
    const formattedAlerts = alerts?.map(alert => ({
      alert_id: alert.alert_id,
      device_id: alert.device_id,
      user_id: alert.user_id,
      user_email: alert.users?.email,
      device_name: alert.devices?.device_name,
      platform: alert.devices?.platform,
      alert_type: alert.alert_type,
      severity: alert.severity,
      details: alert.details,
      created_at: alert.created_at,
      reviewed_at: alert.reviewed_at,
      reviewed_by: alert.reviewed_by,
      status: alert.status
    })) || [];

    return Response.json({
      alerts: formattedAlerts,
      pagination: {
        page,
        limit,
        total: count || 0,
        totalPages: Math.ceil((count || 0) / limit)
      },
      filters: {
        status,
        severity,
        alert_type: alertType
      }
    });

  } catch (error) {
    console.error('Error in security alerts API:', error);
    return Response.json({ error: 'Internal server error' }, { status: 500 });
  }
}

// Review or resolve security alerts
export async function PATCH(request: NextRequest) {
  // Verify admin access
  const adminUser = await requireAdmin(request);
  if (adminUser instanceof Response) {
    return adminUser; // Return error response if not admin
  }

  try {
    const body = await request.json();
    const { alert_id, action, reason } = body;

    if (!alert_id || !action) {
      return Response.json({ error: 'alert_id and action are required' }, { status: 400 });
    }

    if (!['reviewed', 'resolved'].includes(action)) {
      return Response.json({ error: 'action must be "reviewed" or "resolved"' }, { status: 400 });
    }

    const supabase = createServiceRoleClient();
    const timestamp = new Date().toISOString();

    // 1. Update the alert
    const { data: updatedAlert, error: updateError } = await supabase
      .from('suspicious_activity_alerts')
      .update({
        status: action,
        reviewed_at: timestamp,
        reviewed_by: adminUser.id,
        review_reason: reason
      })
      .eq('alert_id', alert_id)
      .select(`
        alert_id,
        device_id,
        user_id,
        alert_type,
        devices!inner(device_name, platform),
        users!inner(email)
      `)
      .single();

    if (updateError || !updatedAlert) {
      console.error('Error updating alert:', updateError);
      return Response.json({ error: 'Failed to update alert' }, { status: 500 });
    }

    // 2. Create audit log entry
    const { error: auditError } = await supabase
      .from('device_audit_logs')
      .insert({
        admin_user_id: adminUser.id,
        action_type: 'review',
        target_device_id: updatedAlert.device_id,
        target_user_id: updatedAlert.user_id,
        reason: `Alert ${action}: ${reason || 'No reason provided'}`,
        metadata: {
          alert_id,
          alert_type: updatedAlert.alert_type,
          action,
          device_name: updatedAlert.devices?.device_name,
          platform: updatedAlert.devices?.platform,
          user_email: updatedAlert.users?.email,
          ip_address: request.headers.get('x-forwarded-for') || 'unknown'
        },
        timestamp,
        ip_address: request.headers.get('x-forwarded-for') || 'unknown'
      });

    if (auditError) {
      console.error('Audit log error:', auditError);
      // Continue even if audit log fails
    }

    return Response.json({
      success: true,
      alert_id,
      status: action,
      reviewed_at: timestamp,
      reviewed_by: adminUser.id
    });

  } catch (error) {
    console.error('Error in security alert review API:', error);
    return Response.json({ error: 'Internal server error' }, { status: 500 });
  }
}