# State Management & Caching Strategy

## 4.1 Enhanced Zustand Store Architecture

```typescript
// Shared store patterns between web and mobile
interface StoreConfig {
  // State structure
  state: StateShape;
  
  // Actions
  actions: StateActions;
  
  // Middleware configuration
  middleware: {
    persist: PersistConfig;
    immer: boolean;
    devtools: boolean;
    apiCache: ApiCacheConfig;
  };
  
  // Cache invalidation
  invalidation: {
    keys: string[];
    ttl: number;
    conditions: InvalidationCondition[];
  };
}

// Example: Enhanced Business Store
export const useBusinessStore = create<BusinessStore>()(
  devtools(
    persist(
      immer((set, get) => ({
        // State
        profile: null,
        products: [],
        analytics: null,
        loading: false,
        error: null,
        
        // API Actions with Caching
        fetchProfile: async () => {
          const cached = get().getCachedData("profile");
          if (cached && !isExpired(cached)) return cached;
          
          set((state) => { state.loading = true; });
          
          try {
            const profile = await apiClient.get("/api/business/profile");
            set((state) => {
              state.profile = profile;
              state.loading = false;
              state.setCachedData("profile", profile);
            });
          } catch (error) {
            set((state) => {
              state.error = error;
              state.loading = false;
            });
          }
        },
        
        // Cache Management
        getCachedData: (key: string) => get().cache[key],
        setCachedData: (key: string, data: any) => {
          set((state) => {
            state.cache[key] = {
              data,
              timestamp: Date.now(),
              ttl: CACHE_TTL[key]
            };
          });
        },
        
        invalidateCache: (keys: string[]) => {
          set((state) => {
            keys.forEach(key => delete state.cache[key]);
          });
        }
      })),
      {
        name: "business-store",
        partialize: (state) => ({ profile: state.profile }),
      }
    ),
    { name: "BusinessStore" }
  )
);
```

## 4.2 Smart Caching Strategy

```typescript
// lib/caching/strategy.ts
export interface CacheStrategy {
  // Cache Types
  types: {
    memory: "In-memory for frequent access";
    localStorage: "Persistent client storage";
    sessionStorage: "Session-based caching";
    apiResponse: "API response caching";
  };
  
  // Cache Policies
  policies: {
    userProfile: { ttl: "15 minutes", invalidateOn: ["profile_update"] };
    businessData: { ttl: "10 minutes", invalidateOn: ["business_update"] };
    products: { ttl: "30 minutes", invalidateOn: ["product_crud"] };
    feed: { ttl: "5 minutes", invalidateOn: ["post_crud", "social_interaction"] };
    analytics: { ttl: "1 hour", invalidateOn: ["analytics_refresh"] };
  };
  
  // Invalidation Events
  invalidationEvents: {
    profile_update: ["userProfile", "businessData"];
    product_crud: ["products", "businessData", "feed"];
    post_crud: ["feed", "userProfile"];
    social_interaction: ["feed", "analytics"];
  };
}

export class CacheManager {
  private stores: Map<string, any> = new Map();
  
  async invalidate(event: string) {
    const affectedCaches = CACHE_STRATEGY.invalidationEvents[event] || [];
    
    // Invalidate Zustand stores
    affectedCaches.forEach(cacheKey => {
      const store = this.stores.get(cacheKey);
      if (store) {
        store.getState().invalidateCache();
      }
    });
    
    // Invalidate API cache
    await this.invalidateApiCache(affectedCaches);
  }
}
```
