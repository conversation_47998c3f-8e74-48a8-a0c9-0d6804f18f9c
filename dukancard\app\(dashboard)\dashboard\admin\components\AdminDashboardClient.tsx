'use client';

import { useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Shield, Smartphone, AlertTriangle, FileText } from 'lucide-react';
import DeviceManagementTab from './DeviceManagementTab';
import SecurityAlertsTab from './SecurityAlertsTab';
import AuditLogsTab from './AuditLogsTab';

export default function AdminDashboardClient() {
  const [activeTab, setActiveTab] = useState('devices');

  const stats = [
    {
      title: 'Total Devices',
      value: '1,234',
      description: 'Active registered devices',
      icon: Smartphone,
      color: 'blue'
    },
    {
      title: 'Security Alerts',
      value: '23',
      description: 'Pending review',
      icon: AlertTriangle,
      color: 'yellow'
    },
    {
      title: 'Revoked Devices',
      value: '45',
      description: 'This month',
      icon: Shield,
      color: 'red'
    },
    {
      title: 'Audit Entries',
      value: '5,678',
      description: 'Last 30 days',
      icon: FileText,
      color: 'green'
    }
  ];

  return (
    <div className="space-y-6">
      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {stats.map((stat, index) => {
          const Icon = stat.icon;
          return (
            <Card key={index}>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">
                  {stat.title}
                </CardTitle>
                <Icon className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{stat.value}</div>
                <p className="text-xs text-muted-foreground">
                  {stat.description}
                </p>
              </CardContent>
            </Card>
          );
        })}
      </div>

      {/* Main Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="devices">Device Management</TabsTrigger>
          <TabsTrigger value="alerts">Security Alerts</TabsTrigger>
          <TabsTrigger value="audit">Audit Logs</TabsTrigger>
        </TabsList>

        <TabsContent value="devices" className="space-y-4">
          <DeviceManagementTab />
        </TabsContent>

        <TabsContent value="alerts" className="space-y-4">
          <SecurityAlertsTab />
        </TabsContent>

        <TabsContent value="audit" className="space-y-4">
          <AuditLogsTab />
        </TabsContent>
      </Tabs>
    </div>
  );
}