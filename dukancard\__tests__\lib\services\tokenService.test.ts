import { TokenService } from '@/lib/services/tokenService';
import { createServiceRoleClient } from '@/utils/supabase/service-role';

jest.mock('@/utils/supabase/service-role');

const mockCreateServiceRoleClient = createServiceRoleClient as jest.MockedFunction<typeof createServiceRoleClient>;

describe('TokenService', () => {
  let tokenService: TokenService;
  let mockSupabase: any;

  beforeEach(() => {
    mockSupabase = {
      from: jest.fn().mockReturnThis(),
      select: jest.fn().mockReturnThis(),
      insert: jest.fn().mockReturnThis(),
      delete: jest.fn().mockReturnThis(),
      eq: jest.fn().mockReturnThis(),
      gt: jest.fn().mockReturnThis(),
      lt: jest.fn().mockReturnThis(),
      single: jest.fn(),
      auth: {
        admin: {
          signOut: jest.fn()
        }
      }
    };

    mockCreateServiceRoleClient.mockReturnValue(mockSupabase);
    tokenService = new TokenService();
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('blacklistDeviceTokens', () => {
    it('should blacklist tokens for a device', async () => {
      const deviceData = { user_id: 'user-123' };
      mockSupabase.single.mockResolvedValueOnce({ data: deviceData, error: null });
      mockSupabase.insert.mockResolvedValueOnce({ error: null });
      mockSupabase.auth.admin.signOut.mockResolvedValueOnce({ error: null });

      await tokenService.blacklistDeviceTokens('device-123', 'Admin revocation', 'admin-123');

      expect(mockSupabase.from).toHaveBeenCalledWith('devices');
      expect(mockSupabase.from).toHaveBeenCalledWith('refresh_token_blacklist');
      expect(mockSupabase.insert).toHaveBeenCalled();
      expect(mockSupabase.auth.admin.signOut).toHaveBeenCalledWith('user-123');
    });

    it('should throw error if device not found', async () => {
      mockSupabase.single.mockResolvedValueOnce({ data: null, error: { message: 'Not found' } });

      await expect(tokenService.blacklistDeviceTokens('device-123', 'reason', 'admin-123'))
        .rejects.toThrow('Device not found');
    });
  });

  describe('blacklistJWT', () => {
    it('should blacklist a JWT token', async () => {
      mockSupabase.insert.mockResolvedValueOnce({ error: null });

      await tokenService.blacklistJWT('jwt-123', 'user-123', 'device-123', 'Admin action', 'admin-123');

      expect(mockSupabase.from).toHaveBeenCalledWith('jwt_blacklist');
      expect(mockSupabase.insert).toHaveBeenCalledWith(
        expect.objectContaining({
          jti: 'jwt-123',
          user_id: 'user-123',
          device_id: 'device-123',
          reason: 'Admin action',
          admin_user_id: 'admin-123'
        })
      );
    });
  });

  describe('isJWTBlacklisted', () => {
    it('should return true if JWT is blacklisted', async () => {
      mockSupabase.single.mockResolvedValueOnce({ data: { id: '123' }, error: null });

      const result = await tokenService.isJWTBlacklisted('jwt-123');

      expect(result).toBe(true);
      expect(mockSupabase.eq).toHaveBeenCalledWith('jti', 'jwt-123');
    });

    it('should return false if JWT is not blacklisted', async () => {
      mockSupabase.single.mockResolvedValueOnce({ data: null, error: { message: 'Not found' } });

      const result = await tokenService.isJWTBlacklisted('jwt-123');

      expect(result).toBe(false);
    });

    it('should return false on error (fail open)', async () => {
      mockSupabase.single.mockRejectedValueOnce(new Error('Database error'));

      const result = await tokenService.isJWTBlacklisted('jwt-123');

      expect(result).toBe(false);
    });
  });

  describe('cleanupExpiredTokens', () => {
    it('should remove expired tokens', async () => {
      mockSupabase.delete.mockResolvedValue({ error: null });

      await tokenService.cleanupExpiredTokens();

      expect(mockSupabase.from).toHaveBeenCalledWith('refresh_token_blacklist');
      expect(mockSupabase.from).toHaveBeenCalledWith('jwt_blacklist');
      expect(mockSupabase.delete).toHaveBeenCalledTimes(2);
    });
  });

  describe('getBlacklistStats', () => {
    it('should return blacklist statistics', async () => {
      mockSupabase.select
        .mockResolvedValueOnce({ count: 5 })
        .mockResolvedValueOnce({ count: 3 });

      const stats = await tokenService.getBlacklistStats();

      expect(stats).toEqual({
        activeRefreshTokens: 5,
        activeJWTs: 3,
        totalBlacklisted: 8
      });
    });

    it('should handle errors gracefully', async () => {
      mockSupabase.select.mockRejectedValue(new Error('Database error'));

      const stats = await tokenService.getBlacklistStats();

      expect(stats).toEqual({
        activeRefreshTokens: 0,
        activeJWTs: 0,
        totalBlacklisted: 0
      });
    });
  });

  describe('bulkBlacklistDeviceTokens', () => {
    it('should blacklist tokens for multiple devices', async () => {
      const deviceData = { user_id: 'user-123' };
      mockSupabase.single.mockResolvedValue({ data: deviceData, error: null });
      mockSupabase.insert.mockResolvedValue({ error: null });
      mockSupabase.auth.admin.signOut.mockResolvedValue({ error: null });

      await tokenService.bulkBlacklistDeviceTokens(['device-1', 'device-2'], 'Bulk revocation', 'admin-123');

      expect(mockSupabase.insert).toHaveBeenCalledTimes(2);
      expect(mockSupabase.auth.admin.signOut).toHaveBeenCalledTimes(2);
    });

    it('should continue processing other devices if one fails', async () => {
      mockSupabase.single
        .mockResolvedValueOnce({ data: null, error: { message: 'Not found' } })
        .mockResolvedValueOnce({ data: { user_id: 'user-123' }, error: null });
      mockSupabase.insert.mockResolvedValue({ error: null });
      mockSupabase.auth.admin.signOut.mockResolvedValue({ error: null });

      await tokenService.bulkBlacklistDeviceTokens(['device-1', 'device-2'], 'Bulk revocation', 'admin-123');

      // Should still process the second device even if first fails
      expect(mockSupabase.insert).toHaveBeenCalledTimes(1);
      expect(mockSupabase.auth.admin.signOut).toHaveBeenCalledTimes(1);
    });
  });
});