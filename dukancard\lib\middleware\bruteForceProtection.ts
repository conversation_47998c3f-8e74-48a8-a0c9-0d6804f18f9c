import { NextRequest, NextResponse } from "next/server";
import { RateLimitResult } from "./rateLimiter";
import { getClientIP } from "@/lib/utils/network";

export interface BruteForceProtectionConfig {
  /** Maximum login attempts per IP per hour (default: 10) */
  maxLoginAttemptsPerIP?: number;
  /** Time window for login attempts in seconds (default: 3600 = 1 hour) */
  loginWindowSeconds?: number;
  /** Maximum login attempts per email per hour (default: 5) */
  maxLoginAttemptsPerEmail?: number;
  /** Maximum token refresh attempts per device per hour (default: 20) */
  maxRefreshAttemptsPerDevice?: number;
  /** Time window for token refresh in seconds (default: 3600 = 1 hour) */
  refreshWindowSeconds?: number;
  /** Whether to enable progressive delays (default: true) */
  enableProgressiveDelays?: boolean;
}

export interface BruteForceContext {
  /** IP address of the request */
  ipAddress?: string;
  /** Email being used for login (if applicable) */
  email?: string;
  /** Device ID for token refresh (if applicable) */
  deviceId?: string;
  /** User ID (if available) */
  userId?: string;
  /** Type of authentication operation */
  operation:
    | "login"
    | "refresh"
    | "device_register"
    | "send_otp"
    | "verify_otp"
    | "business_api"
    | "business_me_api"
    | "business_access_check"
    | "business_search"
    | "sitemap_generation"
    | "profile_exists_api"
    | "customer_profile_api"
    | "likes_api"
    | "location_api"
    | "posts_api"
    | "product_api"
    | "products_api"
    | "storage_api"
    | "email_otp"
    | "email_update"
    | "otp_verify"
    | "gallery_api"
    | "gallery_public_api"
    | "user_profile"
    | "comments_api"
    | "reviews_api"
    | "subscriptions_api";
}

/**
 * Calculate progressive delay based on attempt count
 * Implements exponential backoff with jitter
 */
export function calculateProgressiveDelay(attemptCount: number): number {
  if (attemptCount <= 3) return 0;

  // Base delay starts at 1 second for 4th attempt
  const baseDelay = Math.pow(2, attemptCount - 4) * 1000;

  // Cap at 30 seconds
  const cappedDelay = Math.min(baseDelay, 30000);

  // Add jitter (±20%) but ensure result doesn't exceed cap
  const jitter = cappedDelay * 0.2 * (Math.random() - 0.5);
  const finalDelay = cappedDelay + jitter;

  // Ensure final delay doesn't exceed the cap and is not negative
  return Math.floor(Math.max(0, Math.min(finalDelay, 30000)));
}

/**
 * Apply brute-force protection for authentication endpoints
 */
export async function applyBruteForceProtection(
  _req: NextRequest,
  _context: BruteForceContext,
  _config: BruteForceProtectionConfig = {}
): Promise<RateLimitResult> {
  // For now, return a simple implementation that always allows requests
  // In production, this would implement proper rate limiting
  return {
    success: true,
    remaining: 100,
    resetTime: new Date(Date.now() + 60 * 60 * 1000), // 1 hour from now
  };
}

/**
 * Middleware wrapper for brute force protection
 */
export async function bruteForceProtectionMiddleware(
  req: NextRequest,
  context: BruteForceContext,
  config?: BruteForceProtectionConfig
): Promise<NextResponse | null> {
  const result = await applyBruteForceProtection(req, context, config);

  if (!result.success) {
    const retryAfter = result.retryAfter || 60;
    return new NextResponse(
      JSON.stringify({
        error: result.error || "Rate limit exceeded",
        retryAfter: Math.ceil(retryAfter / 1000),
      }),
      {
        status: 429,
        headers: {
          "Content-Type": "application/json",
          "Retry-After": Math.ceil(retryAfter / 1000).toString(),
          "X-RateLimit-Limit": (result.limit || 100).toString(),
          "X-RateLimit-Remaining": (result.remaining || 0).toString(),
        },
      }
    );
  }

  return null; // No rate limiting applied
}
