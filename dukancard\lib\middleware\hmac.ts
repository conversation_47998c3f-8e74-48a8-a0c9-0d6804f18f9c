import { NextRequest, NextResponse } from 'next/server';
import { 
  generateHMACSignature, 
  verifyHMACSignature, 
  validateTimestamp, 
  extractHMACHeaders 
} from '@/lib/security/hmac';

import { createServiceRoleClient } from '@/utils/supabase/service-role';
import { TABLES } from '@/lib/supabase/constants';
import { 
  generateNonce, 
  isNonceUsed, 
  markNonceUsed 
} from '@/lib/utils/redis';
import { securityService } from '@/lib/services/securityService';
import { getClientIP } from '@/lib/utils/network';

export interface HMACVerificationResult {
  success: boolean;
  error?: string;
  status?: number;
  deviceId?: string;
  userId?: string;
  performanceMs?: number;
}

/**
 * Middleware function to verify HMAC signatures on protected API requests
 * @param req - Next.js request object
 * @param requireHMAC - Whether HMAC verification is required (default: true)
 * @returns Result object indicating success/failure and error details
 */
export async function verifyHMACMiddleware(
  req: NextRequest,
  requireHMAC: boolean = true
): Promise<HMACVerificationResult> {
  const startTime = performance.now();
  const ipAddress = getClientIP(req);
  const userAgent = req.headers.get('user-agent') || 'unknown';
  const requestPath = new URL(req.url).pathname;
  const requestMethod = req.method;
  
  try {
    // Skip HMAC verification if not required (e.g., for login endpoint)
    if (!requireHMAC) {
      return { success: true };
    }

    // Skip HMAC verification for internal calls
    const isInternalCall = req.headers.get('X-Internal-Call') === 'true';
    if (isInternalCall) {
      return { success: true };
    }

    // 1. Extract required headers
    const hmacHeaders = extractHMACHeaders(req.headers);
    if (!hmacHeaders) {
      await securityService.logSecurityEvent({
        event_type: 'missing_headers',
        ip_address: ipAddress,
        user_agent: userAgent,
        request_path: requestPath,
        request_method: requestMethod,
        timestamp: new Date().toISOString(),
        severity: 'low',
        details: {
          message: 'Missing required HMAC headers',
          required_headers: ['X-Device-Id', 'X-Timestamp', 'X-Signature']
        }
      });
      
      return {
        success: false,
        error: 'Missing required headers: X-Device-Id, X-Timestamp, or X-Signature',
        status: 400,
        performanceMs: performance.now() - startTime
      };
    }

    const { deviceId, timestamp, signature } = hmacHeaders;

    // 2. Validate timestamp to prevent replay attacks
    const currentTime = Date.now();
    if (!validateTimestamp(timestamp, 300)) { // 5 minutes = 300 seconds
      await securityService.logTimestampFailure(
        deviceId,
        ipAddress,
        userAgent,
        requestPath,
        requestMethod,
        timestamp,
        currentTime
      );
      
      return {
        success: false,
        error: 'Request has expired',
        status: 408,
        performanceMs: performance.now() - startTime
      };
    }

    // 3. Check for replay attacks using nonce
    const nonce = generateNonce(deviceId, timestamp, signature);
    const isReplay = await isNonceUsed(nonce);
    
    if (isReplay) {
      await securityService.logReplayAttack(
        deviceId,
        ipAddress,
        userAgent,
        requestPath,
        requestMethod,
        nonce
      );
      
      return {
        success: false,
        error: 'Request has been processed before',
        status: 409,
        performanceMs: performance.now() - startTime
      };
    }

    // 4. Fetch device from database
    const supabase = createServiceRoleClient();
    const { data: device, error: deviceError } = await supabase
      .from(TABLES.DEVICES)
      .select('device_id, device_secret_hash, hmac_key_hash, revoked, user_id')
      .eq('device_id', deviceId)
      .single();

    if (deviceError || !device) {
      await securityService.logSecurityEvent({
        event_type: 'device_not_found',
        device_id: deviceId,
        ip_address: ipAddress,
        user_agent: userAgent,
        request_path: requestPath,
        request_method: requestMethod,
        timestamp: new Date().toISOString(),
        severity: 'medium',
        details: {
          error: deviceError?.message || 'Device not found',
          device_id: deviceId
        }
      });
      
      return {
        success: false,
        error: 'Invalid device ID',
        status: 403,
        performanceMs: performance.now() - startTime
      };
    }

    // 5. Check if device is revoked
    if (device.revoked) {
      await securityService.logSecurityEvent({
        event_type: 'device_revoked',
        device_id: deviceId,
        user_id: device.user_id,
        ip_address: ipAddress,
        user_agent: userAgent,
        request_path: requestPath,
        request_method: requestMethod,
        timestamp: new Date().toISOString(),
        severity: 'medium',
        details: {
          message: 'Attempt to use revoked device',
          device_id: deviceId
        }
      });
      
      return {
        success: false,
        error: 'Device has been revoked',
        status: 403,
        performanceMs: performance.now() - startTime
      };
    }

    // 6. Get request body for signature verification
    let requestBody = '';
    try {
      // Clone the request to read the body without consuming it
      const clonedReq = req.clone();
      requestBody = await clonedReq.text();
    } catch (_error) {
      requestBody = '';
    }

    // 7. Generate expected signature using stored HMAC key
    const method = req.method;
    const path = new URL(req.url).pathname;
    
    const expectedSignature = generateHMACSignature(
      method,
      path,
      timestamp,
      requestBody,
      device.hmac_key_hash || '' // Use the stored HMAC key
    );

    // 8. Verify signature using constant-time comparison
    const isValidSignature = verifyHMACSignature(signature, expectedSignature);

    if (!isValidSignature) {
      await securityService.logHMACValidationFailure(
        deviceId,
        ipAddress,
        userAgent,
        requestPath,
        requestMethod,
        'Invalid signature',
        {
          provided_signature: signature.substring(0, 16) + '...', // Log partial for security
          expected_signature: expectedSignature.substring(0, 16) + '...'
        }
      );
      
      return {
        success: false,
        error: 'Invalid signature',
        status: 403,
        performanceMs: performance.now() - startTime
      };
    }

    // 9. Mark nonce as used to prevent replay attacks
    await markNonceUsed(nonce, 300); // Store for 5 minutes
    
    // 10. HMAC verification successful
    const performanceMs = performance.now() - startTime;
    
    // Log successful validation if it took longer than expected (performance monitoring)
    if (performanceMs > 10) {
      console.warn('HMAC validation exceeded 10ms threshold', {
        deviceId,
        performanceMs,
        requestPath
      });
    }
    
    return {
      success: true,
      deviceId: deviceId,
      userId: device.user_id,
      performanceMs
    };
    
  } catch (_error) {
    console.error('Unexpected error in HMAC verification:', _error);
    
    // Log unexpected errors for monitoring
    await securityService.logSecurityEvent({
      event_type: 'malformed_request',
      ip_address: ipAddress,
      user_agent: userAgent,
      request_path: requestPath,
      request_method: requestMethod,
      timestamp: new Date().toISOString(),
      severity: 'medium',
      details: {
        error: _error instanceof Error ? _error.message : 'Unknown error',
        stack: _error instanceof Error ? _error.stack : undefined
      }
    });
    
    return {
      success: false,
      error: 'Internal Server Error',
      status: 500,
      performanceMs: performance.now() - startTime
    };
  }
}

/**
 * Next.js middleware wrapper for HMAC verification
 * @param req - Next.js request object
 * @param requireHMAC - Whether HMAC verification is required
 * @returns NextResponse or null to continue
 */
export async function hmacMiddleware(
  req: NextRequest,
  requireHMAC: boolean = true
): Promise<NextResponse | null> {
  const result = await verifyHMACMiddleware(req, requireHMAC);
  
  if (!result.success) {
    return new NextResponse(JSON.stringify({ error: result.error }), {
      status: result.status || 500,
      headers: { 'Content-Type': 'application/json' },
    });
  }
  
  return null; // Continue to next middleware/handler
}