[{"C:\\web-app\\dukancard\\app\\(auth)\\choose-role\\actions.ts": "1", "C:\\web-app\\dukancard\\app\\(auth)\\choose-role\\ChooseRoleClient.tsx": "2", "C:\\web-app\\dukancard\\app\\(auth)\\choose-role\\page.tsx": "3", "C:\\web-app\\dukancard\\app\\(auth)\\layout.tsx": "4", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\actions\\themeHeaderActions.ts": "5", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\actions.ts": "6", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\business-card\\getBusinessCardData.ts": "7", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\business-card\\updateBusinessCard.ts": "8", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\CardEditorClient.tsx": "9", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\components\\BusinessCardPreview.tsx": "10", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\components\\CardBackgroundEffects.tsx": "11", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\components\\CardBusinessInfo.tsx": "12", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\components\\CardCornerDecorations.tsx": "13", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\components\\CardDivider.tsx": "14", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\components\\CardEditForm\\AppearanceSection.tsx": "15", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\components\\CardEditForm\\BasicInfoSection.tsx": "16", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\components\\CardEditForm\\BusinessDetailsSection.tsx": "17", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\components\\CardEditForm\\BusinessHoursEditor.tsx": "18", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\components\\CardEditForm\\CardEditFormContent.tsx": "19", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\components\\CardEditForm\\ContactLocationSection.tsx": "20", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\components\\CardEditForm\\formStyles.ts": "21", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\components\\CardEditForm\\FormSubmitButton.tsx": "22", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\components\\CardEditForm\\LinksSection.tsx": "23", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\components\\CardEditForm\\StatusSlugSection.tsx": "24", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\components\\CardGlowEffects.tsx": "25", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\components\\CardHeader.tsx": "26", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\components\\CardPreviewSection\\DownloadButton.tsx": "27", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\components\\CardPreviewSection\\index.tsx": "28", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\components\\CardPreviewSection\\ShareButtons.tsx": "29", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\components\\CardProfile.tsx": "30", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\components\\CardTextures.ts": "31", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\components\\CustomAdCropDialog.tsx": "32", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\components\\EnhancedInteractionButtons.tsx": "33", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\components\\hooks\\useLogoUpload.ts": "34", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\components\\hooks\\usePincodeDetails.ts": "35", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\components\\hooks\\useSlugCheck.ts": "36", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\components\\ImageCropDialog.tsx": "37", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\components\\LogoDeleteDialog.tsx": "38", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\components\\UnsavedChangesReminder.tsx": "39", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\components\\utils\\cardUtils.ts": "40", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\data\\businessCardMapper.ts": "41", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\data\\subscriptionChecker.ts": "42", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\logo\\logoActions.ts": "43", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\page.tsx": "44", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\public\\publicCardActions.ts": "45", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\schema.ts": "46", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\slug\\slugUtils.ts": "47", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\utils\\businessHoursProcessor.ts": "48", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\utils\\constants.ts": "49", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\utils\\scrollToError.ts": "50", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\utils\\slugGenerator.ts": "51", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\validation\\businessCardValidation.ts": "52", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\components\\AnimatedMetricCard.tsx": "53", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\components\\BusinessDashboardClient.tsx": "54", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\components\\BusinessDashboardClientLayout.tsx": "55", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\components\\BusinessStatusAlert.tsx": "56", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\components\\DashboardOverviewClient.tsx": "57", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\components\\EnhancedQuickActions.tsx": "58", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\components\\FlipTimer.tsx": "59", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\gallery\\actions.ts": "60", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\gallery\\components\\DeleteDialog.tsx": "61", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\gallery\\components\\EmptyGallery.tsx": "62", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\gallery\\components\\GalleryGrid.tsx": "63", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\gallery\\components\\GalleryHeader.tsx": "64", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\gallery\\components\\Lightbox.tsx": "65", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\gallery\\components\\ReorderControls.tsx": "66", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\gallery\\components\\SortableImageItem.tsx": "67", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\gallery\\components\\StaticImageItem.tsx": "68", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\gallery\\components\\UploadBox.tsx": "69", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\gallery\\components\\UploadDialog.tsx": "70", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\gallery\\GalleryPageClient.tsx": "71", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\gallery\\hooks\\useDragAndDrop.ts": "72", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\gallery\\hooks\\useGalleryState.ts": "73", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\gallery\\hooks\\useReordering.ts": "74", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\gallery\\page.tsx": "75", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\gallery\\types\\galleryTypes.ts": "76", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\gallery\\types.ts": "77", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\gallery\\utils\\fileValidation.ts": "78", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\gallery\\utils.ts": "79", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\layout.tsx": "80", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\likes\\actions.ts": "81", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\likes\\components\\BusinessLikesPageClient.tsx": "82", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\likes\\components\\BusinessLikesReceivedList.tsx": "83", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\likes\\components\\BusinessMyLikesList.tsx": "84", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\likes\\LikeCardClient.tsx": "85", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\likes\\LikeListClient.tsx": "86", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\likes\\page.tsx": "87", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\overview\\page.tsx": "88", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\page.tsx": "89", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\actions\\addProduct.ts": "90", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\actions\\addVariant.ts": "91", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\actions\\bulkVariantOperations.ts": "92", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\actions\\deleteProduct.ts": "93", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\actions\\deleteVariant.ts": "94", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\actions\\getProducts.ts": "95", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\actions\\getProductWithVariants.ts": "96", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\actions\\image-library.ts": "97", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\actions\\imageHandlers.ts": "98", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\actions\\index.ts": "99", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\actions\\schemas.ts": "100", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\actions\\types.ts": "101", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\actions\\updateProduct.ts": "102", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\actions\\updateVariant.ts": "103", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\actions.ts": "104", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\add\\AddProductClient.tsx": "105", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\add\\page.tsx": "106", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\components\\BulkVariantOperations.tsx": "107", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\components\\hooks\\useProductImageUpload.ts": "108", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\components\\hooks\\useProductMultiImageUpload.ts": "109", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\components\\ImageLibraryDialog.tsx": "110", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\components\\product-ui\\index.ts": "111", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\components\\product-ui\\ProductDeleteDialog.tsx": "112", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\components\\product-ui\\ProductEmptyState.tsx": "113", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\components\\product-ui\\ProductFilters.tsx": "114", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\components\\product-ui\\ProductGrid.tsx": "115", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\components\\product-ui\\ProductHeader.tsx": "116", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\components\\product-ui\\ProductItemsPerPageSelector.tsx": "117", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\components\\product-ui\\ProductLoadingState.tsx": "118", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\components\\product-ui\\ProductPagination.tsx": "119", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\components\\product-ui\\ProductStats.tsx": "120", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\components\\product-ui\\ProductTable.tsx": "121", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\components\\product-ui\\ProductViewToggle.tsx": "122", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\components\\ProductImageCropDialog.tsx": "123", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\components\\ProductMultiImageUpload.tsx": "124", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\components\\ProductsPageClient.tsx": "125", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\components\\StandaloneProductForm.tsx": "126", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\components\\VariantCombinationGenerator.tsx": "127", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\components\\VariantForm.tsx": "128", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\components\\VariantTable.tsx": "129", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\components\\VariantTypeSelector.tsx": "130", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\context\\ProductsContext.tsx": "131", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\edit\\[productId]\\EditProductClient.tsx": "132", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\edit\\[productId]\\page.tsx": "133", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\page.tsx": "134", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\ProductsClient.tsx": "135", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\types.ts": "136", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\reviews\\actions.ts": "137", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\reviews\\components\\BusinessMyReviewListClient.tsx": "138", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\reviews\\components\\BusinessReviewListClient.tsx": "139", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\reviews\\components\\BusinessReviewsPageClient.tsx": "140", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\reviews\\page.tsx": "141", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\settings\\actions.ts": "142", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\settings\\components\\AccountDeletionSection.tsx": "143", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\settings\\components\\CardEditorLinkSection.tsx": "144", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\settings\\components\\EmailUpdateDialog.tsx": "145", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\settings\\components\\LinkEmailSection.tsx": "146", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\settings\\components\\LinkPhoneSection.tsx": "147", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\settings\\components\\SettingsPageClient.tsx": "148", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\settings\\page.tsx": "149", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\settings\\schema.ts": "150", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\subscriptions\\actions.ts": "151", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\subscriptions\\components\\BusinessSubscriptionsPageClient.tsx": "152", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\subscriptions\\page.tsx": "153", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\components\\CustomerAnimatedMetricCard.tsx": "154", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\components\\CustomerDashboardClient.tsx": "155", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\components\\CustomerMetricsOverview.tsx": "156", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\CustomerDashboardClientLayout.tsx": "157", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\layout.tsx": "158", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\likes\\actions.ts": "159", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\likes\\components\\LikesPageClient.tsx": "160", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\likes\\LikeListClient.tsx": "161", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\likes\\page.tsx": "162", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\overview\\page.tsx": "163", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\page.tsx": "164", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\profile\\actions.ts": "165", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\profile\\avatar-actions.ts": "166", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\profile\\components\\AddressForm.tsx": "167", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\profile\\components\\AvatarDeleteDialog.tsx": "168", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\profile\\components\\AvatarUpload.tsx": "169", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\profile\\components\\EmailForm.tsx": "170", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\profile\\components\\hooks\\usePincodeDetails.ts": "171", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\profile\\components\\MobileForm.tsx": "172", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\profile\\components\\PhoneForm.tsx": "173", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\profile\\components\\ProfilePageClient.tsx": "174", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\profile\\components\\ProfileRequirementDialog.tsx": "175", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\profile\\hooks\\useAvatarUpload.ts": "176", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\profile\\page.tsx": "177", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\profile\\ProfileForm.tsx": "178", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\reviews\\actions.ts": "179", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\reviews\\components\\EnhancedReviewListClient.tsx": "180", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\reviews\\components\\ReviewsBackground.tsx": "181", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\reviews\\components\\ReviewsPageClient.tsx": "182", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\reviews\\page.tsx": "183", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\reviews\\ReviewListClient.tsx": "184", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\settings\\actions.ts": "185", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\settings\\components\\EmailUpdateDialog.tsx": "186", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\settings\\components\\LinkEmailSection.tsx": "187", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\settings\\components\\LinkPhoneSection.tsx": "188", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\settings\\components\\SettingsPageClient.tsx": "189", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\settings\\CustomerSettingsForm.tsx": "190", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\settings\\DeleteAccountSection.tsx": "191", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\settings\\page.tsx": "192", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\settings\\schema.ts": "193", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\settings\\UpdateEmailForm.tsx": "194", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\subscriptions\\actions.ts": "195", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\subscriptions\\components\\SubscriptionCardSkeleton.tsx": "196", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\subscriptions\\components\\SubscriptionPagination.tsx": "197", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\subscriptions\\components\\SubscriptionsBackground.tsx": "198", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\subscriptions\\components\\SubscriptionSearch.tsx": "199", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\subscriptions\\components\\SubscriptionsPageClient.tsx": "200", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\subscriptions\\page.tsx": "201", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\subscriptions\\SubscriptionCardClient.tsx": "202", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\subscriptions\\SubscriptionListClient.tsx": "203", "C:\\web-app\\dukancard\\app\\(main)\\about\\AboutUsClient.tsx": "204", "C:\\web-app\\dukancard\\app\\(main)\\about\\components\\AboutCTASection.tsx": "205", "C:\\web-app\\dukancard\\app\\(main)\\about\\components\\AboutHeroSection.tsx": "206", "C:\\web-app\\dukancard\\app\\(main)\\about\\components\\animations\\AboutAnimatedBackground.tsx": "207", "C:\\web-app\\dukancard\\app\\(main)\\about\\components\\CoreValuesSection.tsx": "208", "C:\\web-app\\dukancard\\app\\(main)\\about\\components\\MilestonesSection.tsx": "209", "C:\\web-app\\dukancard\\app\\(main)\\about\\components\\MissionVisionSection.tsx": "210", "C:\\web-app\\dukancard\\app\\(main)\\about\\components\\StorySection.tsx": "211", "C:\\web-app\\dukancard\\app\\(main)\\about\\page.tsx": "212", "C:\\web-app\\dukancard\\app\\(main)\\actions\\getHomepageBusinessCard.ts": "213", "C:\\web-app\\dukancard\\app\\(main)\\advertise\\AdvertisePageClient.tsx": "214", "C:\\web-app\\dukancard\\app\\(main)\\advertise\\components\\BenefitsSection.tsx": "215", "C:\\web-app\\dukancard\\app\\(main)\\advertise\\components\\ContactSection.tsx": "216", "C:\\web-app\\dukancard\\app\\(main)\\advertise\\components\\FAQSection.tsx": "217", "C:\\web-app\\dukancard\\app\\(main)\\advertise\\components\\HeroSection.tsx": "218", "C:\\web-app\\dukancard\\app\\(main)\\advertise\\components\\HubSpotForm.tsx": "219", "C:\\web-app\\dukancard\\app\\(main)\\advertise\\page.tsx": "220", "C:\\web-app\\dukancard\\app\\(main)\\auth\\callback\\AuthCallbackClient.tsx": "221", "C:\\web-app\\dukancard\\app\\(main)\\auth\\callback\\AuthCallbackWrapper.tsx": "222", "C:\\web-app\\dukancard\\app\\(main)\\auth\\callback\\metadata.ts": "223", "C:\\web-app\\dukancard\\app\\(main)\\auth\\callback\\page.tsx": "224", "C:\\web-app\\dukancard\\app\\(main)\\blog\\components\\BlogListingClient.tsx": "225", "C:\\web-app\\dukancard\\app\\(main)\\blog\\page.tsx": "226", "C:\\web-app\\dukancard\\app\\(main)\\blog\\sitemap.ts": "227", "C:\\web-app\\dukancard\\app\\(main)\\blog\\[blogSlug]\\components\\BlogPostClient.tsx": "228", "C:\\web-app\\dukancard\\app\\(main)\\blog\\[blogSlug]\\page.tsx": "229", "C:\\web-app\\dukancard\\app\\(main)\\components\\AnimatedBackground.tsx": "230", "C:\\web-app\\dukancard\\app\\(main)\\components\\auth\\AnimatedAuthCard.tsx": "231", "C:\\web-app\\dukancard\\app\\(main)\\components\\auth\\AnimatedButton.tsx": "232", "C:\\web-app\\dukancard\\app\\(main)\\components\\auth\\AnimatedIcon.tsx": "233", "C:\\web-app\\dukancard\\app\\(main)\\components\\auth\\AuthPageBackground.tsx": "234", "C:\\web-app\\dukancard\\app\\(main)\\components\\CardShowcase.tsx": "235", "C:\\web-app\\dukancard\\app\\(main)\\components\\DeviceFrame.tsx": "236", "C:\\web-app\\dukancard\\app\\(main)\\components\\FeatureElements.tsx": "237", "C:\\web-app\\dukancard\\app\\(main)\\components\\FuturisticScanner.tsx": "238", "C:\\web-app\\dukancard\\app\\(main)\\components\\landing\\ActionButtonsSection.tsx": "239", "C:\\web-app\\dukancard\\app\\(main)\\components\\landing\\animations.ts": "240", "C:\\web-app\\dukancard\\app\\(main)\\components\\landing\\CardBackground.tsx": "241", "C:\\web-app\\dukancard\\app\\(main)\\components\\landing\\CTASection.tsx": "242", "C:\\web-app\\dukancard\\app\\(main)\\components\\landing\\DigitalCardFeature.tsx": "243", "C:\\web-app\\dukancard\\app\\(main)\\components\\landing\\EnhancedCardShowcase.tsx": "244", "C:\\web-app\\dukancard\\app\\(main)\\components\\landing\\EnhancedFeatureElements.tsx": "245", "C:\\web-app\\dukancard\\app\\(main)\\components\\landing\\EnhancedMetricsContainer.tsx": "246", "C:\\web-app\\dukancard\\app\\(main)\\components\\landing\\EnhancedMetricsDisplay.tsx": "247", "C:\\web-app\\dukancard\\app\\(main)\\components\\landing\\ErrorDialog.tsx": "248", "C:\\web-app\\dukancard\\app\\(main)\\components\\landing\\exampleCardData.ts": "249", "C:\\web-app\\dukancard\\app\\(main)\\components\\landing\\FeatureCard.tsx": "250", "C:\\web-app\\dukancard\\app\\(main)\\components\\landing\\FeaturesSection.tsx": "251", "C:\\web-app\\dukancard\\app\\(main)\\components\\landing\\FloatingParticlesBackground.tsx": "252", "C:\\web-app\\dukancard\\app\\(main)\\components\\landing\\FloatingPricingElements.tsx": "253", "C:\\web-app\\dukancard\\app\\(main)\\components\\landing\\HeroActionButtons.tsx": "254", "C:\\web-app\\dukancard\\app\\(main)\\components\\landing\\HeroSearchSection.tsx": "255", "C:\\web-app\\dukancard\\app\\(main)\\components\\landing\\HomeCategoriesSection.tsx": "256", "C:\\web-app\\dukancard\\app\\(main)\\components\\landing\\MobileFeatureCarousel.tsx": "257", "C:\\web-app\\dukancard\\app\\(main)\\components\\landing\\ModernSearchSection.tsx": "258", "C:\\web-app\\dukancard\\app\\(main)\\components\\landing\\NewArrivalsSection.tsx": "259", "C:\\web-app\\dukancard\\app\\(main)\\components\\landing\\PopularBusinessesSection.tsx": "260", "C:\\web-app\\dukancard\\app\\(main)\\components\\landing\\SectionDivider.tsx": "261", "C:\\web-app\\dukancard\\app\\(main)\\components\\landing\\StickyHeroSectionClient.tsx": "262", "C:\\web-app\\dukancard\\app\\(main)\\components\\landing\\TestimonialCard.tsx": "263", "C:\\web-app\\dukancard\\app\\(main)\\components\\landing\\TestimonialCarousel.tsx": "264", "C:\\web-app\\dukancard\\app\\(main)\\components\\landing\\TestimonialsSection.tsx": "265", "C:\\web-app\\dukancard\\app\\(main)\\components\\metrics\\index.ts": "266", "C:\\web-app\\dukancard\\app\\(main)\\components\\metrics\\MetricsContainer.tsx": "267", "C:\\web-app\\dukancard\\app\\(main)\\components\\metrics\\MetricsParticles.tsx": "268", "C:\\web-app\\dukancard\\app\\(main)\\components\\MetricsDisplay.tsx": "269", "C:\\web-app\\dukancard\\app\\(main)\\components\\policy\\PolicyBackground.tsx": "270", "C:\\web-app\\dukancard\\app\\(main)\\components\\policy\\PolicyCTASection.tsx": "271", "C:\\web-app\\dukancard\\app\\(main)\\components\\policy\\PolicyHeroSection.tsx": "272", "C:\\web-app\\dukancard\\app\\(main)\\components\\policy\\PolicyNavigation.tsx": "273", "C:\\web-app\\dukancard\\app\\(main)\\components\\policy\\PolicySection.tsx": "274", "C:\\web-app\\dukancard\\app\\(main)\\components\\SectionBackground.tsx": "275", "C:\\web-app\\dukancard\\app\\(main)\\components\\shared\\PopularCategoriesSection.tsx": "276", "C:\\web-app\\dukancard\\app\\(main)\\contact\\components\\animations\\ContactAnimatedBackground.tsx": "277", "C:\\web-app\\dukancard\\app\\(main)\\contact\\components\\ContactCTASection.tsx": "278", "C:\\web-app\\dukancard\\app\\(main)\\contact\\components\\ContactHeroSection.tsx": "279", "C:\\web-app\\dukancard\\app\\(main)\\contact\\components\\ContactInfoSection.tsx": "280", "C:\\web-app\\dukancard\\app\\(main)\\contact\\components\\ContactMapSection.tsx": "281", "C:\\web-app\\dukancard\\app\\(main)\\contact\\ModernContactClient.tsx": "282", "C:\\web-app\\dukancard\\app\\(main)\\contact\\page.tsx": "283", "C:\\web-app\\dukancard\\app\\(main)\\cookies\\ModernCookiePolicyClient.tsx": "284", "C:\\web-app\\dukancard\\app\\(main)\\cookies\\page.tsx": "285", "C:\\web-app\\dukancard\\app\\(main)\\discover\\actions\\businessActions.ts": "286", "C:\\web-app\\dukancard\\app\\(main)\\discover\\actions\\combinedActions.ts": "287", "C:\\web-app\\dukancard\\app\\(main)\\discover\\actions\\locationActions.ts": "288", "C:\\web-app\\dukancard\\app\\(main)\\discover\\actions\\productActions.ts": "289", "C:\\web-app\\dukancard\\app\\(main)\\discover\\actions\\types.ts": "290", "C:\\web-app\\dukancard\\app\\(main)\\discover\\actions.ts": "291", "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\AnimatedBadge.tsx": "292", "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\AnimatedBusinessCard.tsx": "293", "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\AnimatedBusinessGrid.tsx": "294", "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\AnimatedBusinessGridSkeleton.tsx": "295", "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\AnimatedButton.tsx": "296", "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\AnimatedDivider.tsx": "297", "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\BusinessCardTable.tsx": "298", "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\BusinessCardTableSkeleton.tsx": "299", "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\BusinessResultsGrid.tsx": "300", "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\BusinessSortControls.tsx": "301", "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\CategoryCarousel.tsx": "302", "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\CategoryFilter.tsx": "303", "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\CitySearchSkeleton.tsx": "304", "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\ErrorSection.tsx": "305", "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\FloatingCard.tsx": "306", "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\ImprovedSearchSection.tsx": "307", "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\LoadingSpinner.tsx": "308", "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\LocationIndicator.tsx": "309", "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\ModernBusinessFilterGrid.tsx": "310", "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\ModernBusinessResults.tsx": "311", "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\ModernResultsSection.tsx": "312", "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\ModernSearchSection.tsx": "313", "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\NoResultsMessage.tsx": "314", "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\ProductGrid.tsx": "315", "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\ProductGridSkeleton.tsx": "316", "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\ProductResults.tsx": "317", "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\ProductResultsGrid.tsx": "318", "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\ProductSortControls.tsx": "319", "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\ResultsSkeleton.tsx": "320", "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\RevealTitle.tsx": "321", "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\SearchResultsHeader.tsx": "322", "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\SectionTitle.tsx": "323", "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\SortingControls.tsx": "324", "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\ViewToggle.tsx": "325", "C:\\web-app\\dukancard\\app\\(main)\\discover\\constants\\paginationConstants.ts": "326", "C:\\web-app\\dukancard\\app\\(main)\\discover\\constants\\urlParamConstants.ts": "327", "C:\\web-app\\dukancard\\app\\(main)\\discover\\context\\businessContextFunctions.ts": "328", "C:\\web-app\\dukancard\\app\\(main)\\discover\\context\\commonContextFunctions.ts": "329", "C:\\web-app\\dukancard\\app\\(main)\\discover\\context\\DiscoverContext.tsx": "330", "C:\\web-app\\dukancard\\app\\(main)\\discover\\context\\productContextFunctions.ts": "331", "C:\\web-app\\dukancard\\app\\(main)\\discover\\context\\types.ts": "332", "C:\\web-app\\dukancard\\app\\(main)\\discover\\ModernDiscoverClient.tsx": "333", "C:\\web-app\\dukancard\\app\\(main)\\discover\\ModernResultsSkeleton.tsx": "334", "C:\\web-app\\dukancard\\app\\(main)\\discover\\page.tsx": "335", "C:\\web-app\\dukancard\\app\\(main)\\discover\\utils\\sortMappings.ts": "336", "C:\\web-app\\dukancard\\app\\(main)\\features\\components\\BusinessUseCasesSection.tsx": "337", "C:\\web-app\\dukancard\\app\\(main)\\features\\components\\FeatureAnimation.tsx": "338", "C:\\web-app\\dukancard\\app\\(main)\\features\\components\\FeatureBackground.tsx": "339", "C:\\web-app\\dukancard\\app\\(main)\\features\\components\\FeatureCard.tsx": "340", "C:\\web-app\\dukancard\\app\\(main)\\features\\components\\FeaturesCTASection.tsx": "341", "C:\\web-app\\dukancard\\app\\(main)\\features\\components\\HeroFeatureSection.tsx": "342", "C:\\web-app\\dukancard\\app\\(main)\\features\\ModernFeaturesClient.tsx": "343", "C:\\web-app\\dukancard\\app\\(main)\\features\\page.tsx": "344", "C:\\web-app\\dukancard\\app\\(main)\\hooks\\useIntersectionAnimation.ts": "345", "C:\\web-app\\dukancard\\app\\(main)\\LandingPageClient.tsx": "346", "C:\\web-app\\dukancard\\app\\(main)\\layout.tsx": "347", "C:\\web-app\\dukancard\\app\\(main)\\login\\actions.ts": "348", "C:\\web-app\\dukancard\\app\\(main)\\login\\components\\AuthMethodToggle.tsx": "349", "C:\\web-app\\dukancard\\app\\(main)\\login\\components\\EmailOTPForm.tsx": "350", "C:\\web-app\\dukancard\\app\\(main)\\login\\components\\MobilePasswordForm.tsx": "351", "C:\\web-app\\dukancard\\app\\(main)\\login\\components\\SocialLoginButton.tsx": "352", "C:\\web-app\\dukancard\\app\\(main)\\login\\LoginForm.tsx": "353", "C:\\web-app\\dukancard\\app\\(main)\\login\\page.tsx": "354", "C:\\web-app\\dukancard\\app\\(main)\\page.tsx": "355", "C:\\web-app\\dukancard\\app\\(main)\\privacy\\ModernPrivacyPolicyClient.tsx": "356", "C:\\web-app\\dukancard\\app\\(main)\\privacy\\page.tsx": "357", "C:\\web-app\\dukancard\\app\\(main)\\refund\\ModernRefundPolicyClient.tsx": "358", "C:\\web-app\\dukancard\\app\\(main)\\refund\\page.tsx": "359", "C:\\web-app\\dukancard\\app\\(main)\\support\\account-billing\\AccountBillingClient.tsx": "360", "C:\\web-app\\dukancard\\app\\(main)\\support\\account-billing\\page.tsx": "361", "C:\\web-app\\dukancard\\app\\(main)\\support\\business-card-setup\\BusinessCardSetupClient.tsx": "362", "C:\\web-app\\dukancard\\app\\(main)\\support\\business-card-setup\\page.tsx": "363", "C:\\web-app\\dukancard\\app\\(main)\\support\\components\\AnimatedTitle.tsx": "364", "C:\\web-app\\dukancard\\app\\(main)\\support\\components\\EnhancedFAQSection.tsx": "365", "C:\\web-app\\dukancard\\app\\(main)\\support\\components\\EnhancedSupportFAQ.tsx": "366", "C:\\web-app\\dukancard\\app\\(main)\\support\\components\\EnhancedSupportSubPage.tsx": "367", "C:\\web-app\\dukancard\\app\\(main)\\support\\page.tsx": "368", "C:\\web-app\\dukancard\\app\\(main)\\support\\product-management\\page.tsx": "369", "C:\\web-app\\dukancard\\app\\(main)\\support\\product-management\\ProductManagementClient.tsx": "370", "C:\\web-app\\dukancard\\app\\(main)\\support\\settings\\page.tsx": "371", "C:\\web-app\\dukancard\\app\\(main)\\support\\settings\\SettingsClient.tsx": "372", "C:\\web-app\\dukancard\\app\\(main)\\support\\SupportPageClient.tsx": "373", "C:\\web-app\\dukancard\\app\\(main)\\support\\technical-issues\\page.tsx": "374", "C:\\web-app\\dukancard\\app\\(main)\\support\\technical-issues\\TechnicalIssuesClient.tsx": "375", "C:\\web-app\\dukancard\\app\\(main)\\terms\\ModernTermsOfServiceClient.tsx": "376", "C:\\web-app\\dukancard\\app\\(main)\\terms\\page.tsx": "377", "C:\\web-app\\dukancard\\app\\(onboarding)\\layout.tsx": "378", "C:\\web-app\\dukancard\\app\\(onboarding)\\onboarding\\actions.ts": "379", "C:\\web-app\\dukancard\\app\\(onboarding)\\onboarding\\components\\LoadingOverlay.tsx": "380", "C:\\web-app\\dukancard\\app\\(onboarding)\\onboarding\\components\\NavigationButtons.tsx": "381", "C:\\web-app\\dukancard\\app\\(onboarding)\\onboarding\\components\\StepProgress.tsx": "382", "C:\\web-app\\dukancard\\app\\(onboarding)\\onboarding\\components\\steps\\AddressStep.tsx": "383", "C:\\web-app\\dukancard\\app\\(onboarding)\\onboarding\\components\\steps\\BusinessDetailsStep.tsx": "384", "C:\\web-app\\dukancard\\app\\(onboarding)\\onboarding\\components\\steps\\CardInformationStep.tsx": "385", "C:\\web-app\\dukancard\\app\\(onboarding)\\onboarding\\constants\\onboardingSteps.ts": "386", "C:\\web-app\\dukancard\\app\\(onboarding)\\onboarding\\hooks\\useExistingData.ts": "387", "C:\\web-app\\dukancard\\app\\(onboarding)\\onboarding\\hooks\\useOnboardingForm.ts": "388", "C:\\web-app\\dukancard\\app\\(onboarding)\\onboarding\\hooks\\usePincodeDetails.ts": "389", "C:\\web-app\\dukancard\\app\\(onboarding)\\onboarding\\hooks\\useSlugAvailability.ts": "390", "C:\\web-app\\dukancard\\app\\(onboarding)\\onboarding\\hooks\\useUserData.ts": "391", "C:\\web-app\\dukancard\\app\\(onboarding)\\onboarding\\layout.tsx": "392", "C:\\web-app\\dukancard\\app\\(onboarding)\\onboarding\\OnboardingClient.tsx": "393", "C:\\web-app\\dukancard\\app\\(onboarding)\\onboarding\\page.tsx": "394", "C:\\web-app\\dukancard\\app\\(onboarding)\\onboarding\\types\\onboarding.ts": "395", "C:\\web-app\\dukancard\\app\\api\\check-user-type\\route.ts": "396", "C:\\web-app\\dukancard\\app\\auth\\actions.ts": "397", "C:\\web-app\\dukancard\\app\\cards\\sitemap.ts": "398", "C:\\web-app\\dukancard\\app\\components\\AdSlot.tsx": "399", "C:\\web-app\\dukancard\\app\\components\\AdvertiseButton.tsx": "400", "C:\\web-app\\dukancard\\app\\components\\BottomNav.tsx": "401", "C:\\web-app\\dukancard\\app\\components\\EnhancedPublicCardActions.tsx": "402", "C:\\web-app\\dukancard\\app\\components\\FloatingAIButton.tsx": "403", "C:\\web-app\\dukancard\\app\\components\\FloatingInteractionButtons.tsx": "404", "C:\\web-app\\dukancard\\app\\components\\Footer.tsx": "405", "C:\\web-app\\dukancard\\app\\components\\GoogleAnalytics.tsx": "406", "C:\\web-app\\dukancard\\app\\components\\Header.tsx": "407", "C:\\web-app\\dukancard\\app\\components\\icons\\FacebookIcon.tsx": "408", "C:\\web-app\\dukancard\\app\\components\\icons\\InstagramIcon.tsx": "409", "C:\\web-app\\dukancard\\app\\components\\icons\\LinkedInIcon.tsx": "410", "C:\\web-app\\dukancard\\app\\components\\icons\\PinterestIcon.tsx": "411", "C:\\web-app\\dukancard\\app\\components\\icons\\TelegramIcon.tsx": "412", "C:\\web-app\\dukancard\\app\\components\\icons\\TwitterIcon.tsx": "413", "C:\\web-app\\dukancard\\app\\components\\icons\\WhatsAppIcon.tsx": "414", "C:\\web-app\\dukancard\\app\\components\\icons\\YouTubeIcon.tsx": "415", "C:\\web-app\\dukancard\\app\\components\\LoadingOverlay.tsx": "416", "C:\\web-app\\dukancard\\app\\components\\MetaPixel.tsx": "417", "C:\\web-app\\dukancard\\app\\components\\MinimalFooter.tsx": "418", "C:\\web-app\\dukancard\\app\\components\\MinimalHeader.tsx": "419", "C:\\web-app\\dukancard\\app\\components\\MobileFooter.tsx": "420", "C:\\web-app\\dukancard\\app\\components\\ProductListItem.tsx": "421", "C:\\web-app\\dukancard\\app\\components\\PublicCardView.tsx": "422", "C:\\web-app\\dukancard\\app\\components\\reviews\\AuthMessage.tsx": "423", "C:\\web-app\\dukancard\\app\\components\\reviews\\ReviewForm.tsx": "424", "C:\\web-app\\dukancard\\app\\components\\reviews\\ReviewSignInPrompt.tsx": "425", "C:\\web-app\\dukancard\\app\\components\\reviews\\ReviewsList.tsx": "426", "C:\\web-app\\dukancard\\app\\components\\reviews\\ReviewsSummary.tsx": "427", "C:\\web-app\\dukancard\\app\\components\\reviews\\ReviewsTab.tsx": "428", "C:\\web-app\\dukancard\\app\\components\\ReviewsTab.tsx": "429", "C:\\web-app\\dukancard\\app\\components\\shared\\EnhancedCardActions.tsx": "430", "C:\\web-app\\dukancard\\app\\components\\shared\\likes\\index.ts": "431", "C:\\web-app\\dukancard\\app\\components\\shared\\likes\\LikeCard.tsx": "432", "C:\\web-app\\dukancard\\app\\components\\shared\\likes\\LikeCardSkeleton.tsx": "433", "C:\\web-app\\dukancard\\app\\components\\shared\\likes\\LikeList.tsx": "434", "C:\\web-app\\dukancard\\app\\components\\shared\\likes\\LikePagination.tsx": "435", "C:\\web-app\\dukancard\\app\\components\\shared\\likes\\LikeSearch.tsx": "436", "C:\\web-app\\dukancard\\app\\components\\shared\\reviews\\ReviewCard.tsx": "437", "C:\\web-app\\dukancard\\app\\components\\shared\\reviews\\ReviewCardSkeleton.tsx": "438", "C:\\web-app\\dukancard\\app\\components\\shared\\reviews\\ReviewSortDropdown.tsx": "439", "C:\\web-app\\dukancard\\app\\components\\shared\\subscriptions\\index.ts": "440", "C:\\web-app\\dukancard\\app\\components\\shared\\subscriptions\\SubscriptionCard.tsx": "441", "C:\\web-app\\dukancard\\app\\components\\shared\\subscriptions\\SubscriptionCardSkeleton.tsx": "442", "C:\\web-app\\dukancard\\app\\components\\shared\\subscriptions\\SubscriptionList.tsx": "443", "C:\\web-app\\dukancard\\app\\components\\shared\\subscriptions\\SubscriptionPagination.tsx": "444", "C:\\web-app\\dukancard\\app\\components\\shared\\subscriptions\\SubscriptionSearch.tsx": "445", "C:\\web-app\\dukancard\\app\\components\\ThemeToggle.tsx": "446", "C:\\web-app\\dukancard\\app\\components\\ui\\container.tsx": "447", "C:\\web-app\\dukancard\\app\\layout.tsx": "448", "C:\\web-app\\dukancard\\app\\locality\\actions\\businessActions.ts": "449", "C:\\web-app\\dukancard\\app\\locality\\actions\\combinedActions.ts": "450", "C:\\web-app\\dukancard\\app\\locality\\actions\\locationActions.ts": "451", "C:\\web-app\\dukancard\\app\\locality\\actions\\productActions.ts": "452", "C:\\web-app\\dukancard\\app\\locality\\actions.ts": "453", "C:\\web-app\\dukancard\\app\\locality\\components\\BreadcrumbNav.tsx": "454", "C:\\web-app\\dukancard\\app\\locality\\components\\ErrorSection.tsx": "455", "C:\\web-app\\dukancard\\app\\locality\\components\\ImprovedSearchSection.tsx": "456", "C:\\web-app\\dukancard\\app\\locality\\components\\LocationIndicator.tsx": "457", "C:\\web-app\\dukancard\\app\\locality\\components\\ModernResultsSection.tsx": "458", "C:\\web-app\\dukancard\\app\\locality\\constants\\paginationConstants.ts": "459", "C:\\web-app\\dukancard\\app\\locality\\context\\businessContextFunctions.ts": "460", "C:\\web-app\\dukancard\\app\\locality\\context\\commonContextFunctions.ts": "461", "C:\\web-app\\dukancard\\app\\locality\\context\\LocalityContext.tsx": "462", "C:\\web-app\\dukancard\\app\\locality\\context\\productContextFunctions.ts": "463", "C:\\web-app\\dukancard\\app\\locality\\context\\types.ts": "464", "C:\\web-app\\dukancard\\app\\locality\\layout.tsx": "465", "C:\\web-app\\dukancard\\app\\locality\\[localSlug]\\LocalityPageClient.tsx": "466", "C:\\web-app\\dukancard\\app\\locality\\[localSlug]\\page.tsx": "467", "C:\\web-app\\dukancard\\app\\post\\[postId]\\error.tsx": "468", "C:\\web-app\\dukancard\\app\\post\\[postId]\\loading.tsx": "469", "C:\\web-app\\dukancard\\app\\post\\[postId]\\not-found.tsx": "470", "C:\\web-app\\dukancard\\app\\post\\[postId]\\page.tsx": "471", "C:\\web-app\\dukancard\\app\\products\\sitemap.ts": "472", "C:\\web-app\\dukancard\\app\\robots.ts": "473", "C:\\web-app\\dukancard\\app\\sitemap.ts": "474", "C:\\web-app\\dukancard\\app\\[cardSlug]\\actions.ts": "475", "C:\\web-app\\dukancard\\app\\[cardSlug]\\components\\animations.ts": "476", "C:\\web-app\\dukancard\\app\\[cardSlug]\\components\\BusinessDetails\\EnhancedMetricsCards.tsx": "477", "C:\\web-app\\dukancard\\app\\[cardSlug]\\components\\BusinessDetails\\ProfessionalBusinessTable.tsx": "478", "C:\\web-app\\dukancard\\app\\[cardSlug]\\components\\EnhancedAdSection.tsx": "479", "C:\\web-app\\dukancard\\app\\[cardSlug]\\components\\EnhancedBusinessCardSection.tsx": "480", "C:\\web-app\\dukancard\\app\\[cardSlug]\\components\\EnhancedBusinessDetails.tsx": "481", "C:\\web-app\\dukancard\\app\\[cardSlug]\\components\\EnhancedBusinessGallery.tsx": "482", "C:\\web-app\\dukancard\\app\\[cardSlug]\\components\\EnhancedPublicCardPageWrapper.tsx": "483", "C:\\web-app\\dukancard\\app\\[cardSlug]\\components\\EnhancedTabsToggle.tsx": "484", "C:\\web-app\\dukancard\\app\\[cardSlug]\\components\\GalleryTab.tsx": "485", "C:\\web-app\\dukancard\\app\\[cardSlug]\\components\\OfflineBusinessMessage.tsx": "486", "C:\\web-app\\dukancard\\app\\[cardSlug]\\components\\ProductGridSkeleton.tsx": "487", "C:\\web-app\\dukancard\\app\\[cardSlug]\\components\\ProductsTab.tsx": "488", "C:\\web-app\\dukancard\\app\\[cardSlug]\\gallery\\GalleryPageClient.tsx": "489", "C:\\web-app\\dukancard\\app\\[cardSlug]\\gallery\\page.tsx": "490", "C:\\web-app\\dukancard\\app\\[cardSlug]\\layout.tsx": "491", "C:\\web-app\\dukancard\\app\\[cardSlug]\\loading.tsx": "492", "C:\\web-app\\dukancard\\app\\[cardSlug]\\page.tsx": "493", "C:\\web-app\\dukancard\\app\\[cardSlug]\\product\\actions.ts": "494", "C:\\web-app\\dukancard\\app\\[cardSlug]\\product\\components\\BuyNowButton.tsx": "495", "C:\\web-app\\dukancard\\app\\[cardSlug]\\product\\components\\ImageZoomModal.tsx": "496", "C:\\web-app\\dukancard\\app\\[cardSlug]\\product\\components\\OfflineProductMessage.tsx": "497", "C:\\web-app\\dukancard\\app\\[cardSlug]\\product\\components\\PhoneButton.tsx": "498", "C:\\web-app\\dukancard\\app\\[cardSlug]\\product\\components\\ProductAdSection.tsx": "499", "C:\\web-app\\dukancard\\app\\[cardSlug]\\product\\components\\ProductDetail.tsx": "500", "C:\\web-app\\dukancard\\app\\[cardSlug]\\product\\components\\ProductRecommendations.tsx": "501", "C:\\web-app\\dukancard\\app\\[cardSlug]\\product\\components\\VariantSelector.tsx": "502", "C:\\web-app\\dukancard\\app\\[cardSlug]\\product\\components\\WhatsAppButton.tsx": "503", "C:\\web-app\\dukancard\\app\\[cardSlug]\\product\\hooks\\usePinchZoom.ts": "504", "C:\\web-app\\dukancard\\app\\[cardSlug]\\product\\[productSlug]\\page.tsx": "505", "C:\\web-app\\dukancard\\app\\[cardSlug]\\product\\[productSlug]\\ProductDetailClient.tsx": "506", "C:\\web-app\\dukancard\\app\\[cardSlug]\\PublicCardPageClient.tsx": "507", "C:\\web-app\\dukancard\\components\\blog\\BlogCard.tsx": "508", "C:\\web-app\\dukancard\\components\\blog\\BlogContent.tsx": "509", "C:\\web-app\\dukancard\\components\\blog\\BlogListingSkeleton.tsx": "510", "C:\\web-app\\dukancard\\components\\blog\\BlogNavigation.tsx": "511", "C:\\web-app\\dukancard\\components\\blog\\BlogPagination.tsx": "512", "C:\\web-app\\dukancard\\components\\blog\\BlogPostSkeleton.tsx": "513", "C:\\web-app\\dukancard\\components\\blog\\BlogSearch.tsx": "514", "C:\\web-app\\dukancard\\components\\feed\\ModernBusinessFeedList.tsx": "515", "C:\\web-app\\dukancard\\components\\feed\\ModernCustomerFeedList.tsx": "516", "C:\\web-app\\dukancard\\components\\feed\\shared\\dialogs\\PostDeleteDialog.tsx": "517", "C:\\web-app\\dukancard\\components\\feed\\shared\\editors\\InlinePostAndProductEditor.tsx": "518", "C:\\web-app\\dukancard\\components\\feed\\shared\\editors\\InlinePostEditor.tsx": "519", "C:\\web-app\\dukancard\\components\\feed\\shared\\FilterPills.tsx": "520", "C:\\web-app\\dukancard\\components\\feed\\shared\\forms\\ImageCropper.tsx": "521", "C:\\web-app\\dukancard\\components\\feed\\shared\\forms\\LocationDisplay.tsx": "522", "C:\\web-app\\dukancard\\components\\feed\\shared\\forms\\ProductSelector.tsx": "523", "C:\\web-app\\dukancard\\components\\feed\\shared\\hooks\\useCustomerPostMediaUpload.ts": "524", "C:\\web-app\\dukancard\\components\\feed\\shared\\hooks\\usePostMediaUpload.ts": "525", "C:\\web-app\\dukancard\\components\\feed\\shared\\hooks\\usePostOwnership.ts": "526", "C:\\web-app\\dukancard\\components\\feed\\shared\\ModernCustomerPostCard.tsx": "527", "C:\\web-app\\dukancard\\components\\feed\\shared\\ModernFeedContainer.tsx": "528", "C:\\web-app\\dukancard\\components\\feed\\shared\\ModernFeedHeader.tsx": "529", "C:\\web-app\\dukancard\\components\\feed\\shared\\ModernPostCard.tsx": "530", "C:\\web-app\\dukancard\\components\\feed\\shared\\PostActions.tsx": "531", "C:\\web-app\\dukancard\\components\\feed\\shared\\PostCardSkeleton.tsx": "532", "C:\\web-app\\dukancard\\components\\feed\\shared\\SocialMediaBusinessPostCreator.tsx": "533", "C:\\web-app\\dukancard\\components\\feed\\shared\\SocialMediaPostCreator.tsx": "534", "C:\\web-app\\dukancard\\components\\feed\\shared\\UnifiedPostCard.tsx": "535", "C:\\web-app\\dukancard\\components\\post\\BackNavigation.tsx": "536", "C:\\web-app\\dukancard\\components\\post\\ConditionalPostLayout.tsx": "537", "C:\\web-app\\dukancard\\components\\post\\PostShareButton.tsx": "538", "C:\\web-app\\dukancard\\components\\post\\SinglePostView.tsx": "539", "C:\\web-app\\dukancard\\components\\qr\\QRScanner.tsx": "540", "C:\\web-app\\dukancard\\components\\qr\\QRScannerModal.tsx": "541", "C:\\web-app\\dukancard\\components\\sidebar\\BusinessAppSidebar.tsx": "542", "C:\\web-app\\dukancard\\components\\sidebar\\CustomerAppSidebar.tsx": "543", "C:\\web-app\\dukancard\\components\\sidebar\\NavBusinessMain.tsx": "544", "C:\\web-app\\dukancard\\components\\sidebar\\NavBusinessUser.tsx": "545", "C:\\web-app\\dukancard\\components\\sidebar\\NavCustomerMain.tsx": "546", "C:\\web-app\\dukancard\\components\\sidebar\\NavCustomerUser.tsx": "547", "C:\\web-app\\dukancard\\components\\sidebar\\SidebarLink.tsx": "548", "C:\\web-app\\dukancard\\components\\ui\\accordion.tsx": "549", "C:\\web-app\\dukancard\\components\\ui\\alert-dialog.tsx": "550", "C:\\web-app\\dukancard\\components\\ui\\alert.tsx": "551", "C:\\web-app\\dukancard\\components\\ui\\avatar.tsx": "552", "C:\\web-app\\dukancard\\components\\ui\\badge.tsx": "553", "C:\\web-app\\dukancard\\components\\ui\\breadcrumb.tsx": "554", "C:\\web-app\\dukancard\\components\\ui\\button.tsx": "555", "C:\\web-app\\dukancard\\components\\ui\\calendar.tsx": "556", "C:\\web-app\\dukancard\\components\\ui\\card.tsx": "557", "C:\\web-app\\dukancard\\components\\ui\\carousel.tsx": "558", "C:\\web-app\\dukancard\\components\\ui\\category-combobox.tsx": "559", "C:\\web-app\\dukancard\\components\\ui\\chart.tsx": "560", "C:\\web-app\\dukancard\\components\\ui\\checkbox.tsx": "561", "C:\\web-app\\dukancard\\components\\ui\\collapsible.tsx": "562", "C:\\web-app\\dukancard\\components\\ui\\command.tsx": "563", "C:\\web-app\\dukancard\\components\\ui\\dialog.tsx": "564", "C:\\web-app\\dukancard\\components\\ui\\dropdown-menu.tsx": "565", "C:\\web-app\\dukancard\\components\\ui\\form.tsx": "566", "C:\\web-app\\dukancard\\components\\ui\\input-otp.tsx": "567", "C:\\web-app\\dukancard\\components\\ui\\input.tsx": "568", "C:\\web-app\\dukancard\\components\\ui\\label.tsx": "569", "C:\\web-app\\dukancard\\components\\ui\\multi-select-combobox.tsx": "570", "C:\\web-app\\dukancard\\components\\ui\\pagination.tsx": "571", "C:\\web-app\\dukancard\\components\\ui\\popover.tsx": "572", "C:\\web-app\\dukancard\\components\\ui\\progress.tsx": "573", "C:\\web-app\\dukancard\\components\\ui\\radio-group.tsx": "574", "C:\\web-app\\dukancard\\components\\ui\\resizable-navbar.tsx": "575", "C:\\web-app\\dukancard\\components\\ui\\scroll-area.tsx": "576", "C:\\web-app\\dukancard\\components\\ui\\scroll-to-top-button.tsx": "577", "C:\\web-app\\dukancard\\components\\ui\\select.tsx": "578", "C:\\web-app\\dukancard\\components\\ui\\separator.tsx": "579", "C:\\web-app\\dukancard\\components\\ui\\sheet.tsx": "580", "C:\\web-app\\dukancard\\components\\ui\\sidebar.tsx": "581", "C:\\web-app\\dukancard\\components\\ui\\skeleton.tsx": "582", "C:\\web-app\\dukancard\\components\\ui\\slider.tsx": "583", "C:\\web-app\\dukancard\\components\\ui\\sonner.tsx": "584", "C:\\web-app\\dukancard\\components\\ui\\switch.tsx": "585", "C:\\web-app\\dukancard\\components\\ui\\table.tsx": "586", "C:\\web-app\\dukancard\\components\\ui\\tabs.tsx": "587", "C:\\web-app\\dukancard\\components\\ui\\textarea.tsx": "588", "C:\\web-app\\dukancard\\components\\ui\\toast.tsx": "589", "C:\\web-app\\dukancard\\components\\ui\\tooltip.tsx": "590", "C:\\web-app\\dukancard\\components\\ui\\use-toast.ts": "591", "C:\\web-app\\dukancard\\components\\ui\\visually-hidden.tsx": "592", "C:\\web-app\\dukancard\\lib\\actions\\blogs.ts": "593", "C:\\web-app\\dukancard\\lib\\actions\\businessProfiles\\access.ts": "594", "C:\\web-app\\dukancard\\lib\\actions\\businessProfiles\\discovery.ts": "595", "C:\\web-app\\dukancard\\lib\\actions\\businessProfiles\\index.ts": "596", "C:\\web-app\\dukancard\\lib\\actions\\businessProfiles\\location.ts": "597", "C:\\web-app\\dukancard\\lib\\actions\\businessProfiles\\profileRetrieval.ts": "598", "C:\\web-app\\dukancard\\lib\\actions\\businessProfiles\\search.ts": "599", "C:\\web-app\\dukancard\\lib\\actions\\businessProfiles\\sitemap.ts": "600", "C:\\web-app\\dukancard\\lib\\actions\\businessProfiles\\types.ts": "601", "C:\\web-app\\dukancard\\lib\\actions\\businessProfiles\\utils.ts": "602", "C:\\web-app\\dukancard\\lib\\actions\\categories\\locationBasedFetching.ts": "603", "C:\\web-app\\dukancard\\lib\\actions\\categories\\pincodeTypes.ts": "604", "C:\\web-app\\dukancard\\lib\\actions\\customerPosts\\crud.ts": "605", "C:\\web-app\\dukancard\\lib\\actions\\customerPosts\\index.ts": "606", "C:\\web-app\\dukancard\\lib\\actions\\customerProfiles\\addressValidation.ts": "607", "C:\\web-app\\dukancard\\lib\\actions\\gallery.ts": "608", "C:\\web-app\\dukancard\\lib\\actions\\interactions.ts": "609", "C:\\web-app\\dukancard\\lib\\actions\\location\\locationBySlug.ts": "610", "C:\\web-app\\dukancard\\lib\\actions\\location.ts": "611", "C:\\web-app\\dukancard\\lib\\actions\\posts\\crud.ts": "612", "C:\\web-app\\dukancard\\lib\\actions\\posts\\fetchSinglePost.ts": "613", "C:\\web-app\\dukancard\\lib\\actions\\posts\\index.ts": "614", "C:\\web-app\\dukancard\\lib\\actions\\posts\\types.ts": "615", "C:\\web-app\\dukancard\\lib\\actions\\posts\\unifiedFeed.ts": "616", "C:\\web-app\\dukancard\\lib\\actions\\posts\\utils.ts": "617", "C:\\web-app\\dukancard\\lib\\actions\\posts.ts": "618", "C:\\web-app\\dukancard\\lib\\actions\\products\\fetchProductsByIds.ts": "619", "C:\\web-app\\dukancard\\lib\\actions\\products\\sitemapHelpers.ts": "620", "C:\\web-app\\dukancard\\lib\\actions\\redirectAfterLogin.ts": "621", "C:\\web-app\\dukancard\\lib\\actions\\reviews.ts": "622", "C:\\web-app\\dukancard\\lib\\actions\\secureCustomerProfiles.ts": "623", "C:\\web-app\\dukancard\\lib\\actions\\shared\\delete-customer-post-media.ts": "624", "C:\\web-app\\dukancard\\lib\\actions\\shared\\productActions.ts": "625", "C:\\web-app\\dukancard\\lib\\actions\\shared\\upload-business-post-media.ts": "626", "C:\\web-app\\dukancard\\lib\\actions\\shared\\upload-customer-post-media.ts": "627", "C:\\web-app\\dukancard\\lib\\actions\\shared\\upload-post-media.ts": "628", "C:\\web-app\\dukancard\\lib\\api\\response.ts": "629", "C:\\web-app\\dukancard\\lib\\cardDownloader.ts": "630", "C:\\web-app\\dukancard\\lib\\client\\locationUtils.ts": "631", "C:\\web-app\\dukancard\\lib\\config\\categories.ts": "632", "C:\\web-app\\dukancard\\lib\\config\\states.ts": "633", "C:\\web-app\\dukancard\\lib\\constants\\predefinedVariants.ts": "634", "C:\\web-app\\dukancard\\lib\\csrf.ts": "635", "C:\\web-app\\dukancard\\lib\\errorHandling.ts": "636", "C:\\web-app\\dukancard\\lib\\qrCodeGenerator.ts": "637", "C:\\web-app\\dukancard\\lib\\rateLimiter.ts": "638", "C:\\web-app\\dukancard\\lib\\schemas\\authSchemas.ts": "639", "C:\\web-app\\dukancard\\lib\\schemas\\locationSchemas.ts": "640", "C:\\web-app\\dukancard\\lib\\services\\realtimeService.ts": "641", "C:\\web-app\\dukancard\\lib\\services\\socialService.ts": "642", "C:\\web-app\\dukancard\\lib\\site-config.ts": "643", "C:\\web-app\\dukancard\\lib\\siteContent.ts": "644", "C:\\web-app\\dukancard\\lib\\supabase\\constants.ts": "645", "C:\\web-app\\dukancard\\lib\\types\\activities.ts": "646", "C:\\web-app\\dukancard\\lib\\types\\api.ts": "647", "C:\\web-app\\dukancard\\lib\\types\\blog.ts": "648", "C:\\web-app\\dukancard\\lib\\types\\posts.ts": "649", "C:\\web-app\\dukancard\\lib\\utils\\addressUtils.ts": "650", "C:\\web-app\\dukancard\\lib\\utils\\addressValidation.ts": "651", "C:\\web-app\\dukancard\\lib\\utils\\cameraUtils.ts": "652", "C:\\web-app\\dukancard\\lib\\utils\\client-image-compression.ts": "653", "C:\\web-app\\dukancard\\lib\\utils\\customBranding.ts": "654", "C:\\web-app\\dukancard\\lib\\utils\\debounce.ts": "655", "C:\\web-app\\dukancard\\lib\\utils\\feed\\diversityEngine.ts": "656", "C:\\web-app\\dukancard\\lib\\utils\\feed\\feedMerger.ts": "657", "C:\\web-app\\dukancard\\lib\\utils\\feed\\hybridTimeAndPlanAlgorithm.ts": "658", "C:\\web-app\\dukancard\\lib\\utils\\feed\\optimizedHybridAlgorithm.ts": "659", "C:\\web-app\\dukancard\\lib\\utils\\feed\\planPrioritizer.ts": "660", "C:\\web-app\\dukancard\\lib\\utils\\feed\\postCreationHandler.ts": "661", "C:\\web-app\\dukancard\\lib\\utils\\feed\\smartFeedAlgorithm.ts": "662", "C:\\web-app\\dukancard\\lib\\utils\\image-compression.ts": "663", "C:\\web-app\\dukancard\\lib\\utils\\markdown.ts": "664", "C:\\web-app\\dukancard\\lib\\utils\\pagination.ts": "665", "C:\\web-app\\dukancard\\lib\\utils\\postUrl.ts": "666", "C:\\web-app\\dukancard\\lib\\utils\\qrCodeUtils.ts": "667", "C:\\web-app\\dukancard\\lib\\utils\\seo.ts": "668", "C:\\web-app\\dukancard\\lib\\utils\\slugUtils.ts": "669", "C:\\web-app\\dukancard\\lib\\utils\\storage-paths.ts": "670", "C:\\web-app\\dukancard\\lib\\utils\\supabaseErrorHandler.ts": "671", "C:\\web-app\\dukancard\\lib\\utils\\variantHelpers.ts": "672", "C:\\web-app\\dukancard\\lib\\utils.ts": "673", "C:\\web-app\\dukancard\\components\\ui\\CommentDisplay.tsx": "674", "C:\\web-app\\dukancard\\components\\ui\\CommentInput.tsx": "675", "C:\\web-app\\dukancard\\components\\ui\\CommentSection.tsx": "676", "C:\\web-app\\dukancard\\components\\ui\\LikeButton.tsx": "677", "C:\\web-app\\dukancard\\lib\\actions\\comments\\postComments.ts": "678", "C:\\web-app\\dukancard\\lib\\actions\\likes\\commentLikes.ts": "679", "C:\\web-app\\dukancard\\lib\\actions\\likes\\postLikes.ts": "680", "C:\\web-app\\dukancard\\lib\\stores\\likeCommentStore.ts": "681", "C:\\web-app\\dukancard\\lib\\utils\\formatNumber.ts": "682", "C:\\web-app\\dukancard\\components\\post\\PostLikesList.tsx": "683", "C:\\web-app\\dukancard\\lib\\services\\pagination\\paginationService.ts": "684", "C:\\web-app\\dukancard\\app\\(onboarding)\\onboarding\\components\\steps\\WelcomeStep.tsx": "685", "C:\\web-app\\dukancard\\app\\api\\auth\\login\\route.ts": "686", "C:\\web-app\\dukancard\\app\\api\\auth\\logout\\route.ts": "687", "C:\\web-app\\dukancard\\app\\api\\auth\\refresh\\route.ts": "688", "C:\\web-app\\dukancard\\app\\api\\devices\\register\\route.ts": "689", "C:\\web-app\\dukancard\\lib\\auth\\jwt.ts": "690", "C:\\web-app\\dukancard\\lib\\auth\\utils.ts": "691", "C:\\web-app\\dukancard\\lib\\middleware\\hmac.ts": "692", "C:\\web-app\\dukancard\\lib\\security\\hashing.ts": "693", "C:\\web-app\\dukancard\\lib\\security\\hmac.ts": "694", "C:\\web-app\\dukancard\\lib\\middleware\\bruteForceProtection.ts": "695", "C:\\web-app\\dukancard\\lib\\actions\\auth.ts": "696", "C:\\web-app\\dukancard\\lib\\stores\\authStore.ts": "697", "C:\\web-app\\dukancard\\lib\\utils\\authenticatedFetch.ts": "698", "C:\\web-app\\dukancard\\app\\api\\auth\\send-otp\\route.ts": "699", "C:\\web-app\\dukancard\\app\\api\\auth\\verify-otp\\route.ts": "700", "C:\\web-app\\dukancard\\lib\\auth\\middleware-jwt.ts": "701", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\overview\\BusinessOverviewClient.tsx": "702", "C:\\web-app\\dukancard\\app\\api\\business\\access\\route.ts": "703", "C:\\web-app\\dukancard\\app\\api\\business\\me\\route.ts": "704", "C:\\web-app\\dukancard\\app\\api\\business\\profile\\exists\\route.ts": "705", "C:\\web-app\\dukancard\\app\\api\\business\\route.ts": "706", "C:\\web-app\\dukancard\\app\\api\\business\\search\\route.ts": "707", "C:\\web-app\\dukancard\\app\\api\\business\\sitemap\\route.ts": "708", "C:\\web-app\\dukancard\\app\\api\\business\\slug\\[slug]\\route.ts": "709", "C:\\web-app\\dukancard\\app\\api\\business\\[id]\\route.ts": "710", "C:\\web-app\\dukancard\\app\\api\\customer\\profile\\exists\\route.ts": "711", "C:\\web-app\\dukancard\\app\\api\\customer\\profile\\route.ts": "712", "C:\\web-app\\dukancard\\app\\api\\likes\\route.ts": "713", "C:\\web-app\\dukancard\\app\\api\\location\\city\\[city]\\route.ts": "714", "C:\\web-app\\dukancard\\app\\api\\location\\pincode\\[pincode]\\route.ts": "715", "C:\\web-app\\dukancard\\app\\api\\posts\\route.ts": "716", "C:\\web-app\\dukancard\\app\\api\\products\\route.ts": "717", "C:\\web-app\\dukancard\\app\\api\\products\\[id]\\route.ts": "718", "C:\\web-app\\dukancard\\app\\api\\storage\\upload\\route.ts": "719", "C:\\web-app\\dukancard\\lib\\stores\\businessProfileStore.ts": "720", "C:\\web-app\\dukancard\\lib\\stores\\postsStore.ts": "721", "C:\\web-app\\dukancard\\lib\\stores\\productsStore.ts": "722", "C:\\web-app\\dukancard\\lib\\stores\\storageStore.ts": "723", "C:\\web-app\\dukancard\\app\\api\\comments\\route.ts": "724", "C:\\web-app\\dukancard\\lib\\stores\\customerProfileStore.ts": "725", "C:\\web-app\\dukancard\\app\\api\\comments\\[id]\\route.ts": "726", "C:\\web-app\\dukancard\\lib\\utils\\internalApiClient.ts": "727", "C:\\web-app\\dukancard\\app\\api\\subscriptions\\route.ts": "728", "C:\\web-app\\dukancard\\app\\api\\reviews\\route.ts": "729", "C:\\web-app\\dukancard\\app\\api\\gallery\\public\\[businessSlug]\\route.ts": "730", "C:\\web-app\\dukancard\\app\\api\\gallery\\route.ts": "731", "C:\\web-app\\dukancard\\app\\api\\gallery\\[imageId]\\route.ts": "732", "C:\\web-app\\dukancard\\app\\api\\posts\\[postId]\\route.ts": "733", "C:\\web-app\\dukancard\\app\\api\\user\\profile\\route.ts": "734", "C:\\web-app\\dukancard\\lib\\auth\\getAuthContext.ts": "735", "C:\\web-app\\dukancard\\lib\\middleware\\routeProtection.ts": "736", "C:\\web-app\\dukancard\\lib\\security\\csrf.ts": "737", "C:\\web-app\\dukancard\\lib\\utils\\jwt-error-handler.ts": "738", "C:\\web-app\\dukancard\\lib\\middleware\\rateLimiter.ts": "739", "C:\\web-app\\dukancard\\lib\\services\\deviceService.ts": "740", "C:\\web-app\\dukancard\\lib\\services\\securityService.ts": "741", "C:\\web-app\\dukancard\\lib\\utils\\redis.ts": "742", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\admin\\components\\AdminDashboardClient.tsx": "743", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\admin\\components\\AuditLogsTab.tsx": "744", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\admin\\components\\DeviceDetailModal.tsx": "745", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\admin\\components\\DeviceManagementTab.tsx": "746", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\admin\\components\\SecurityAlertsTab.tsx": "747", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\admin\\page.tsx": "748", "C:\\web-app\\dukancard\\app\\api\\admin\\audit\\logs\\route.ts": "749", "C:\\web-app\\dukancard\\app\\api\\admin\\devices\\list\\route.ts": "750", "C:\\web-app\\dukancard\\app\\api\\admin\\devices\\quarantine\\route.ts": "751", "C:\\web-app\\dukancard\\app\\api\\admin\\devices\\revoke\\route.ts": "752", "C:\\web-app\\dukancard\\app\\api\\admin\\devices\\[deviceId]\\route.ts": "753", "C:\\web-app\\dukancard\\app\\api\\admin\\security\\alerts\\route.ts": "754", "C:\\web-app\\dukancard\\lib\\hooks\\admin\\useAdminDevices.ts": "755", "C:\\web-app\\dukancard\\lib\\hooks\\admin\\useAuditLogs.ts": "756", "C:\\web-app\\dukancard\\lib\\hooks\\admin\\useSecurityAlerts.ts": "757", "C:\\web-app\\dukancard\\lib\\services\\deviceAnalytics.ts": "758", "C:\\web-app\\dukancard\\lib\\services\\securityAnalytics.ts": "759", "C:\\web-app\\dukancard\\lib\\services\\tokenService.ts": "760", "C:\\web-app\\dukancard\\lib\\utils\\adminAuth.ts": "761"}, {"size": 3965, "mtime": 1754649239254, "results": "762", "hashOfConfig": "763"}, {"size": 11792, "mtime": 1754319312875, "results": "764", "hashOfConfig": "763"}, {"size": 4230, "mtime": 1753520831364, "results": "765", "hashOfConfig": "763"}, {"size": 2270, "mtime": 1753437208359, "results": "766", "hashOfConfig": "763"}, {"size": 7333, "mtime": 1753436763325, "results": "767", "hashOfConfig": "763"}, {"size": 273, "mtime": 1752078894640, "results": "768", "hashOfConfig": "763"}, {"size": 4153, "mtime": 1754779581110, "results": "769", "hashOfConfig": "763"}, {"size": 5032, "mtime": 1754779478004, "results": "770", "hashOfConfig": "763"}, {"size": 20428, "mtime": 1754686812839, "results": "771", "hashOfConfig": "763"}, {"size": 20203, "mtime": 1754686932336, "results": "772", "hashOfConfig": "763"}, {"size": 2220, "mtime": 1752078894653, "results": "773", "hashOfConfig": "763"}, {"size": 10914, "mtime": 1752078894653, "results": "774", "hashOfConfig": "763"}, {"size": 1630, "mtime": 1752078894653, "results": "775", "hashOfConfig": "763"}, {"size": 721, "mtime": 1752078894653, "results": "776", "hashOfConfig": "763"}, {"size": 7408, "mtime": 1754687066766, "results": "777", "hashOfConfig": "763"}, {"size": 21052, "mtime": 1752078894664, "results": "778", "hashOfConfig": "763"}, {"size": 4489, "mtime": 1752078894665, "results": "779", "hashOfConfig": "763"}, {"size": 13138, "mtime": 1752078894667, "results": "780", "hashOfConfig": "763"}, {"size": 2250, "mtime": 1754686772279, "results": "781", "hashOfConfig": "763"}, {"size": 14802, "mtime": 1752080815930, "results": "782", "hashOfConfig": "763"}, {"size": 1088, "mtime": 1752078894667, "results": "783", "hashOfConfig": "763"}, {"size": 2708, "mtime": 1752078894667, "results": "784", "hashOfConfig": "763"}, {"size": 9768, "mtime": 1752078894667, "results": "785", "hashOfConfig": "763"}, {"size": 23953, "mtime": 1752078894667, "results": "786", "hashOfConfig": "763"}, {"size": 3361, "mtime": 1752078894667, "results": "787", "hashOfConfig": "763"}, {"size": 3637, "mtime": 1752078894678, "results": "788", "hashOfConfig": "763"}, {"size": 6688, "mtime": 1752078894680, "results": "789", "hashOfConfig": "763"}, {"size": 1987, "mtime": 1754686868244, "results": "790", "hashOfConfig": "763"}, {"size": 3868, "mtime": 1752078894681, "results": "791", "hashOfConfig": "763"}, {"size": 4167, "mtime": 1752078894682, "results": "792", "hashOfConfig": "763"}, {"size": 3004, "mtime": 1752078894683, "results": "793", "hashOfConfig": "763"}, {"size": 6921, "mtime": 1752078894684, "results": "794", "hashOfConfig": "763"}, {"size": 8477, "mtime": 1754649966466, "results": "795", "hashOfConfig": "763"}, {"size": 9562, "mtime": 1752078894708, "results": "796", "hashOfConfig": "763"}, {"size": 4802, "mtime": 1752078894708, "results": "797", "hashOfConfig": "763"}, {"size": 1794, "mtime": 1752078894708, "results": "798", "hashOfConfig": "763"}, {"size": 5533, "mtime": 1752078894708, "results": "799", "hashOfConfig": "763"}, {"size": 2140, "mtime": 1752078894708, "results": "800", "hashOfConfig": "763"}, {"size": 5309, "mtime": 1752078894708, "results": "801", "hashOfConfig": "763"}, {"size": 3245, "mtime": 1752078894717, "results": "802", "hashOfConfig": "763"}, {"size": 6811, "mtime": 1754689429292, "results": "803", "hashOfConfig": "763"}, {"size": 1012, "mtime": 1754689054078, "results": "804", "hashOfConfig": "763"}, {"size": 7583, "mtime": 1753436763340, "results": "805", "hashOfConfig": "763"}, {"size": 1240, "mtime": 1754689071053, "results": "806", "hashOfConfig": "763"}, {"size": 1543, "mtime": 1754781364571, "results": "807", "hashOfConfig": "763"}, {"size": 6426, "mtime": 1754687845822, "results": "808", "hashOfConfig": "763"}, {"size": 2435, "mtime": 1752078894723, "results": "809", "hashOfConfig": "763"}, {"size": 1001, "mtime": 1752078894723, "results": "810", "hashOfConfig": "763"}, {"size": 559, "mtime": 1753031839333, "results": "811", "hashOfConfig": "763"}, {"size": 2573, "mtime": 1752078894723, "results": "812", "hashOfConfig": "763"}, {"size": 404, "mtime": 1752078894723, "results": "813", "hashOfConfig": "763"}, {"size": 1825, "mtime": 1752078894723, "results": "814", "hashOfConfig": "763"}, {"size": 4687, "mtime": 1752520901592, "results": "815", "hashOfConfig": "763"}, {"size": 4992, "mtime": 1754779008814, "results": "816", "hashOfConfig": "763"}, {"size": 2514, "mtime": 1754687380447, "results": "817", "hashOfConfig": "763"}, {"size": 2722, "mtime": 1752521491570, "results": "818", "hashOfConfig": "763"}, {"size": 5665, "mtime": 1754779092008, "results": "819", "hashOfConfig": "763"}, {"size": 4935, "mtime": 1754687616421, "results": "820", "hashOfConfig": "763"}, {"size": 8561, "mtime": 1752078894735, "results": "821", "hashOfConfig": "763"}, {"size": 4475, "mtime": 1754790287521, "results": "822", "hashOfConfig": "763"}, {"size": 1993, "mtime": 1752078894735, "results": "823", "hashOfConfig": "763"}, {"size": 3314, "mtime": 1752522195446, "results": "824", "hashOfConfig": "763"}, {"size": 6883, "mtime": 1753436763340, "results": "825", "hashOfConfig": "763"}, {"size": 2180, "mtime": 1752521782818, "results": "826", "hashOfConfig": "763"}, {"size": 1799, "mtime": 1752078894735, "results": "827", "hashOfConfig": "763"}, {"size": 2794, "mtime": 1752078894751, "results": "828", "hashOfConfig": "763"}, {"size": 3181, "mtime": 1753436763340, "results": "829", "hashOfConfig": "763"}, {"size": 2096, "mtime": 1753436763340, "results": "830", "hashOfConfig": "763"}, {"size": 2833, "mtime": 1752078894751, "results": "831", "hashOfConfig": "763"}, {"size": 6615, "mtime": 1752522674608, "results": "832", "hashOfConfig": "763"}, {"size": 8681, "mtime": 1753436763340, "results": "833", "hashOfConfig": "763"}, {"size": 2305, "mtime": 1752078894751, "results": "834", "hashOfConfig": "763"}, {"size": 2046, "mtime": 1753436763340, "results": "835", "hashOfConfig": "763"}, {"size": 3165, "mtime": 1753436763340, "results": "836", "hashOfConfig": "763"}, {"size": 1673, "mtime": 1754683844676, "results": "837", "hashOfConfig": "763"}, {"size": 767, "mtime": 1753436763340, "results": "838", "hashOfConfig": "763"}, {"size": 555, "mtime": 1753436763340, "results": "839", "hashOfConfig": "763"}, {"size": 1142, "mtime": 1752078894766, "results": "840", "hashOfConfig": "763"}, {"size": 593, "mtime": 1754683088529, "results": "841", "hashOfConfig": "763"}, {"size": 2957, "mtime": 1754687323847, "results": "842", "hashOfConfig": "763"}, {"size": 1949, "mtime": 1753520944546, "results": "843", "hashOfConfig": "763"}, {"size": 10378, "mtime": 1753531001335, "results": "844", "hashOfConfig": "763"}, {"size": 4483, "mtime": 1752925036472, "results": "845", "hashOfConfig": "763"}, {"size": 3675, "mtime": 1752524849734, "results": "846", "hashOfConfig": "763"}, {"size": 4030, "mtime": 1752078894766, "results": "847", "hashOfConfig": "763"}, {"size": 1698, "mtime": 1752078894766, "results": "848", "hashOfConfig": "763"}, {"size": 3845, "mtime": 1753589702934, "results": "849", "hashOfConfig": "763"}, {"size": 521, "mtime": 1754778918947, "results": "850", "hashOfConfig": "763"}, {"size": 2331, "mtime": 1754787386256, "results": "851", "hashOfConfig": "763"}, {"size": 7738, "mtime": 1754789714224, "results": "852", "hashOfConfig": "763"}, {"size": 13026, "mtime": 1754051312710, "results": "853", "hashOfConfig": "763"}, {"size": 8366, "mtime": 1753436763340, "results": "854", "hashOfConfig": "763"}, {"size": 5098, "mtime": 1753436763340, "results": "855", "hashOfConfig": "763"}, {"size": 15095, "mtime": 1753436763356, "results": "856", "hashOfConfig": "763"}, {"size": 2477, "mtime": 1754790009108, "results": "857", "hashOfConfig": "763"}, {"size": 11161, "mtime": 1753975945921, "results": "858", "hashOfConfig": "763"}, {"size": 7625, "mtime": 1753976075953, "results": "859", "hashOfConfig": "763"}, {"size": 9106, "mtime": 1753521094750, "results": "860", "hashOfConfig": "763"}, {"size": 774, "mtime": 1752078894839, "results": "861", "hashOfConfig": "763"}, {"size": 5527, "mtime": 1753104677045, "results": "862", "hashOfConfig": "763"}, {"size": 585, "mtime": 1752170193569, "results": "863", "hashOfConfig": "763"}, {"size": 8460, "mtime": 1754789805392, "results": "864", "hashOfConfig": "763"}, {"size": 12397, "mtime": 1753436763363, "results": "865", "hashOfConfig": "763"}, {"size": 99, "mtime": 1752078894830, "results": "866", "hashOfConfig": "763"}, {"size": 10565, "mtime": 1754683399868, "results": "867", "hashOfConfig": "763"}, {"size": 2050, "mtime": 1754683328621, "results": "868", "hashOfConfig": "763"}, {"size": 16600, "mtime": 1753977090407, "results": "869", "hashOfConfig": "763"}, {"size": 6920, "mtime": 1752078894861, "results": "870", "hashOfConfig": "763"}, {"size": 8800, "mtime": 1752078894863, "results": "871", "hashOfConfig": "763"}, {"size": 7997, "mtime": 1752078894845, "results": "872", "hashOfConfig": "763"}, {"size": 743, "mtime": 1752083877657, "results": "873", "hashOfConfig": "763"}, {"size": 2089, "mtime": 1752078894863, "results": "874", "hashOfConfig": "763"}, {"size": 2029, "mtime": 1752170340365, "results": "875", "hashOfConfig": "763"}, {"size": 5665, "mtime": 1752087097784, "results": "876", "hashOfConfig": "763"}, {"size": 9160, "mtime": 1752086184726, "results": "877", "hashOfConfig": "763"}, {"size": 2212, "mtime": 1752084237937, "results": "878", "hashOfConfig": "763"}, {"size": 2762, "mtime": 1752165254320, "results": "879", "hashOfConfig": "763"}, {"size": 4770, "mtime": 1752085989931, "results": "880", "hashOfConfig": "763"}, {"size": 7051, "mtime": 1752165128789, "results": "881", "hashOfConfig": "763"}, {"size": 5000, "mtime": 1754687866954, "results": "882", "hashOfConfig": "763"}, {"size": 18791, "mtime": 1753106058738, "results": "883", "hashOfConfig": "763"}, {"size": 4142, "mtime": 1754687886146, "results": "884", "hashOfConfig": "763"}, {"size": 5264, "mtime": 1752078894845, "results": "885", "hashOfConfig": "763"}, {"size": 16573, "mtime": 1752078894852, "results": "886", "hashOfConfig": "763"}, {"size": 1270, "mtime": 1754683708553, "results": "887", "hashOfConfig": "763"}, {"size": 30029, "mtime": 1754683464926, "results": "888", "hashOfConfig": "763"}, {"size": 17692, "mtime": 1752078894855, "results": "889", "hashOfConfig": "763"}, {"size": 31473, "mtime": 1753976419791, "results": "890", "hashOfConfig": "763"}, {"size": 25712, "mtime": 1753977056767, "results": "891", "hashOfConfig": "763"}, {"size": 10885, "mtime": 1752078894855, "results": "892", "hashOfConfig": "763"}, {"size": 7797, "mtime": 1754687705844, "results": "893", "hashOfConfig": "763"}, {"size": 12996, "mtime": 1754683526681, "results": "894", "hashOfConfig": "763"}, {"size": 3126, "mtime": 1754687510171, "results": "895", "hashOfConfig": "763"}, {"size": 1564, "mtime": 1754789068921, "results": "896", "hashOfConfig": "763"}, {"size": 3020, "mtime": 1754683758576, "results": "897", "hashOfConfig": "763"}, {"size": 2542, "mtime": 1754687739927, "results": "898", "hashOfConfig": "763"}, {"size": 1190, "mtime": 1753794776157, "results": "899", "hashOfConfig": "763"}, {"size": 10361, "mtime": 1753513706129, "results": "900", "hashOfConfig": "763"}, {"size": 12000, "mtime": 1753616611181, "results": "901", "hashOfConfig": "763"}, {"size": 5373, "mtime": 1752525844741, "results": "902", "hashOfConfig": "763"}, {"size": 2006, "mtime": 1753026919824, "results": "903", "hashOfConfig": "763"}, {"size": 15120, "mtime": 1754792826952, "results": "904", "hashOfConfig": "763"}, {"size": 23396, "mtime": 1752691514782, "results": "905", "hashOfConfig": "763"}, {"size": 1822, "mtime": 1752526641904, "results": "906", "hashOfConfig": "763"}, {"size": 15608, "mtime": 1752763145906, "results": "907", "hashOfConfig": "763"}, {"size": 4060, "mtime": 1752762369862, "results": "908", "hashOfConfig": "763"}, {"size": 3182, "mtime": 1752526585020, "results": "909", "hashOfConfig": "763"}, {"size": 4559, "mtime": 1752763551631, "results": "910", "hashOfConfig": "763"}, {"size": 2104, "mtime": 1753251129502, "results": "911", "hashOfConfig": "763"}, {"size": 547, "mtime": 1752761809080, "results": "912", "hashOfConfig": "763"}, {"size": 3243, "mtime": 1754684394964, "results": "913", "hashOfConfig": "763"}, {"size": 11679, "mtime": 1754684394965, "results": "914", "hashOfConfig": "763"}, {"size": 4330, "mtime": 1754684394967, "results": "915", "hashOfConfig": "763"}, {"size": 2300, "mtime": 1752078894894, "results": "916", "hashOfConfig": "763"}, {"size": 1807, "mtime": 1752078894894, "results": "917", "hashOfConfig": "763"}, {"size": 2559, "mtime": 1752078894894, "results": "918", "hashOfConfig": "763"}, {"size": 2308, "mtime": 1752652107359, "results": "919", "hashOfConfig": "763"}, {"size": 2136, "mtime": 1752078894894, "results": "920", "hashOfConfig": "763"}, {"size": 591, "mtime": 1753445796006, "results": "921", "hashOfConfig": "763"}, {"size": 6530, "mtime": 1752078894894, "results": "922", "hashOfConfig": "763"}, {"size": 1372, "mtime": 1752925036472, "results": "923", "hashOfConfig": "763"}, {"size": 3511, "mtime": 1752078894894, "results": "924", "hashOfConfig": "763"}, {"size": 2499, "mtime": 1753251129502, "results": "925", "hashOfConfig": "763"}, {"size": 2705, "mtime": 1754787393562, "results": "926", "hashOfConfig": "763"}, {"size": 13774, "mtime": 1754792425971, "results": "927", "hashOfConfig": "763"}, {"size": 6372, "mtime": 1752078894909, "results": "928", "hashOfConfig": "763"}, {"size": 12390, "mtime": 1753515317601, "results": "929", "hashOfConfig": "763"}, {"size": 1376, "mtime": 1752688533290, "results": "930", "hashOfConfig": "763"}, {"size": 5647, "mtime": 1752688601357, "results": "931", "hashOfConfig": "763"}, {"size": 7356, "mtime": 1752078894910, "results": "932", "hashOfConfig": "763"}, {"size": 4583, "mtime": 1752078894910, "results": "933", "hashOfConfig": "763"}, {"size": 6598, "mtime": 1752078894910, "results": "934", "hashOfConfig": "763"}, {"size": 5978, "mtime": 1752078894910, "results": "935", "hashOfConfig": "763"}, {"size": 13585, "mtime": 1752678735198, "results": "936", "hashOfConfig": "763"}, {"size": 6909, "mtime": 1752078894910, "results": "937", "hashOfConfig": "763"}, {"size": 7677, "mtime": 1752078894910, "results": "938", "hashOfConfig": "763"}, {"size": 2322, "mtime": 1752078894910, "results": "939", "hashOfConfig": "763"}, {"size": 7209, "mtime": 1752678129103, "results": "940", "hashOfConfig": "763"}, {"size": 558, "mtime": 1753513910116, "results": "941", "hashOfConfig": "763"}, {"size": 10540, "mtime": 1753514869215, "results": "942", "hashOfConfig": "763"}, {"size": 733, "mtime": 1752078894910, "results": "943", "hashOfConfig": "763"}, {"size": 1203, "mtime": 1752078894910, "results": "944", "hashOfConfig": "763"}, {"size": 1827, "mtime": 1752078894910, "results": "945", "hashOfConfig": "763"}, {"size": 2950, "mtime": 1753514279872, "results": "946", "hashOfConfig": "763"}, {"size": 18116, "mtime": 1753794776165, "results": "947", "hashOfConfig": "763"}, {"size": 15615, "mtime": 1752763206921, "results": "948", "hashOfConfig": "763"}, {"size": 4083, "mtime": 1752762551074, "results": "949", "hashOfConfig": "763"}, {"size": 2929, "mtime": 1752527064479, "results": "950", "hashOfConfig": "763"}, {"size": 4305, "mtime": 1752763625974, "results": "951", "hashOfConfig": "763"}, {"size": 5476, "mtime": 1752763083916, "results": "952", "hashOfConfig": "763"}, {"size": 23276, "mtime": 1752527499610, "results": "953", "hashOfConfig": "763"}, {"size": 1602, "mtime": 1753251129502, "results": "954", "hashOfConfig": "763"}, {"size": 1380, "mtime": 1752762960440, "results": "955", "hashOfConfig": "763"}, {"size": 3464, "mtime": 1752696729504, "results": "956", "hashOfConfig": "763"}, {"size": 684, "mtime": 1754684394970, "results": "957", "hashOfConfig": "763"}, {"size": 2007, "mtime": 1754684394970, "results": "958", "hashOfConfig": "763"}, {"size": 3579, "mtime": 1754684394971, "results": "959", "hashOfConfig": "763"}, {"size": 1181, "mtime": 1754684394972, "results": "960", "hashOfConfig": "763"}, {"size": 2665, "mtime": 1754684394972, "results": "961", "hashOfConfig": "763"}, {"size": 6679, "mtime": 1754684394973, "results": "962", "hashOfConfig": "763"}, {"size": 3744, "mtime": 1754684394974, "results": "963", "hashOfConfig": "763"}, {"size": 6038, "mtime": 1754684394968, "results": "964", "hashOfConfig": "763"}, {"size": 1270, "mtime": 1754684394969, "results": "965", "hashOfConfig": "763"}, {"size": 2007, "mtime": 1752078894925, "results": "966", "hashOfConfig": "763"}, {"size": 5704, "mtime": 1752078894941, "results": "967", "hashOfConfig": "763"}, {"size": 4918, "mtime": 1754684984809, "results": "968", "hashOfConfig": "763"}, {"size": 4615, "mtime": 1752078894941, "results": "969", "hashOfConfig": "763"}, {"size": 5211, "mtime": 1752078894941, "results": "970", "hashOfConfig": "763"}, {"size": 5725, "mtime": 1752078894941, "results": "971", "hashOfConfig": "763"}, {"size": 4799, "mtime": 1752078894941, "results": "972", "hashOfConfig": "763"}, {"size": 7774, "mtime": 1752078894941, "results": "973", "hashOfConfig": "763"}, {"size": 2147, "mtime": 1752078894941, "results": "974", "hashOfConfig": "763"}, {"size": 5445, "mtime": 1754781877844, "results": "975", "hashOfConfig": "763"}, {"size": 737, "mtime": 1752078894941, "results": "976", "hashOfConfig": "763"}, {"size": 5712, "mtime": 1752078894941, "results": "977", "hashOfConfig": "763"}, {"size": 8862, "mtime": 1752078894941, "results": "978", "hashOfConfig": "763"}, {"size": 3996, "mtime": 1752078894941, "results": "979", "hashOfConfig": "763"}, {"size": 6753, "mtime": 1752078894941, "results": "980", "hashOfConfig": "763"}, {"size": 6562, "mtime": 1752078894941, "results": "981", "hashOfConfig": "763"}, {"size": 1779, "mtime": 1752078894941, "results": "982", "hashOfConfig": "763"}, {"size": 9940, "mtime": 1753436763363, "results": "983", "hashOfConfig": "763"}, {"size": 161, "mtime": 1752078894941, "results": "984", "hashOfConfig": "763"}, {"size": 189, "mtime": 1752078894956, "results": "985", "hashOfConfig": "763"}, {"size": 565, "mtime": 1752078894956, "results": "986", "hashOfConfig": "763"}, {"size": 8610, "mtime": 1752078894956, "results": "987", "hashOfConfig": "763"}, {"size": 1870, "mtime": 1752078894956, "results": "988", "hashOfConfig": "763"}, {"size": 2516, "mtime": 1752078894956, "results": "989", "hashOfConfig": "763"}, {"size": 14853, "mtime": 1752080815937, "results": "990", "hashOfConfig": "763"}, {"size": 4728, "mtime": 1753978897165, "results": "991", "hashOfConfig": "763"}, {"size": 2890, "mtime": 1752078894956, "results": "992", "hashOfConfig": "763"}, {"size": 1924, "mtime": 1752078894956, "results": "993", "hashOfConfig": "763"}, {"size": 2239, "mtime": 1752078894956, "results": "994", "hashOfConfig": "763"}, {"size": 1646, "mtime": 1752078894956, "results": "995", "hashOfConfig": "763"}, {"size": 5334, "mtime": 1752078894972, "results": "996", "hashOfConfig": "763"}, {"size": 1022, "mtime": 1754687848231, "results": "997", "hashOfConfig": "763"}, {"size": 3451, "mtime": 1752078894956, "results": "998", "hashOfConfig": "763"}, {"size": 5179, "mtime": 1752078894956, "results": "999", "hashOfConfig": "763"}, {"size": 4986, "mtime": 1752078894956, "results": "1000", "hashOfConfig": "763"}, {"size": 4326, "mtime": 1752078894972, "results": "1001", "hashOfConfig": "763"}, {"size": 411, "mtime": 1752078894988, "results": "1002", "hashOfConfig": "763"}, {"size": 4822, "mtime": 1752078894975, "results": "1003", "hashOfConfig": "763"}, {"size": 4706, "mtime": 1752078894975, "results": "1004", "hashOfConfig": "763"}, {"size": 4957, "mtime": 1752078894976, "results": "1005", "hashOfConfig": "763"}, {"size": 3810, "mtime": 1754689119726, "results": "1006", "hashOfConfig": "763"}, {"size": 18438, "mtime": 1752078894978, "results": "1007", "hashOfConfig": "763"}, {"size": 1266, "mtime": 1752078894978, "results": "1008", "hashOfConfig": "763"}, {"size": 8177, "mtime": 1752078894979, "results": "1009", "hashOfConfig": "763"}, {"size": 1581, "mtime": 1752078894980, "results": "1010", "hashOfConfig": "763"}, {"size": 1353, "mtime": 1754688208018, "results": "1011", "hashOfConfig": "763"}, {"size": 1799, "mtime": 1752078894980, "results": "1012", "hashOfConfig": "763"}, {"size": 6966, "mtime": 1752078894981, "results": "1013", "hashOfConfig": "763"}, {"size": 295, "mtime": 1752078894981, "results": "1014", "hashOfConfig": "763"}, {"size": 3916, "mtime": 1752078894982, "results": "1015", "hashOfConfig": "763"}, {"size": 5877, "mtime": 1752078894983, "results": "1016", "hashOfConfig": "763"}, {"size": 18346, "mtime": 1752078894983, "results": "1017", "hashOfConfig": "763"}, {"size": 897, "mtime": 1752078894983, "results": "1018", "hashOfConfig": "763"}, {"size": 3936, "mtime": 1752078894983, "results": "1019", "hashOfConfig": "763"}, {"size": 23617, "mtime": 1752078894983, "results": "1020", "hashOfConfig": "763"}, {"size": 3305, "mtime": 1752078894983, "results": "1021", "hashOfConfig": "763"}, {"size": 3489, "mtime": 1752078894983, "results": "1022", "hashOfConfig": "763"}, {"size": 1739, "mtime": 1752078894988, "results": "1023", "hashOfConfig": "763"}, {"size": 6529, "mtime": 1752078894988, "results": "1024", "hashOfConfig": "763"}, {"size": 3222, "mtime": 1752078894988, "results": "1025", "hashOfConfig": "763"}, {"size": 5762, "mtime": 1752078894988, "results": "1026", "hashOfConfig": "763"}, {"size": 3984, "mtime": 1752078894988, "results": "1027", "hashOfConfig": "763"}, {"size": 134, "mtime": 1752078894988, "results": "1028", "hashOfConfig": "763"}, {"size": 848, "mtime": 1752078894988, "results": "1029", "hashOfConfig": "763"}, {"size": 2993, "mtime": 1752078894988, "results": "1030", "hashOfConfig": "763"}, {"size": 5827, "mtime": 1752078894956, "results": "1031", "hashOfConfig": "763"}, {"size": 4054, "mtime": 1752078894988, "results": "1032", "hashOfConfig": "763"}, {"size": 5287, "mtime": 1752078894988, "results": "1033", "hashOfConfig": "763"}, {"size": 3121, "mtime": 1752078894988, "results": "1034", "hashOfConfig": "763"}, {"size": 3264, "mtime": 1752078894988, "results": "1035", "hashOfConfig": "763"}, {"size": 2619, "mtime": 1752078894988, "results": "1036", "hashOfConfig": "763"}, {"size": 3681, "mtime": 1752078894956, "results": "1037", "hashOfConfig": "763"}, {"size": 5286, "mtime": 1752080815937, "results": "1038", "hashOfConfig": "763"}, {"size": 4433, "mtime": 1752078895003, "results": "1039", "hashOfConfig": "763"}, {"size": 5822, "mtime": 1752078894988, "results": "1040", "hashOfConfig": "763"}, {"size": 5776, "mtime": 1752078894988, "results": "1041", "hashOfConfig": "763"}, {"size": 13788, "mtime": 1752078894988, "results": "1042", "hashOfConfig": "763"}, {"size": 9337, "mtime": 1752078895003, "results": "1043", "hashOfConfig": "763"}, {"size": 3219, "mtime": 1752078894988, "results": "1044", "hashOfConfig": "763"}, {"size": 3306, "mtime": 1752078895003, "results": "1045", "hashOfConfig": "763"}, {"size": 14293, "mtime": 1752078895003, "results": "1046", "hashOfConfig": "763"}, {"size": 827, "mtime": 1752078895003, "results": "1047", "hashOfConfig": "763"}, {"size": 5306, "mtime": 1754688273590, "results": "1048", "hashOfConfig": "763"}, {"size": 6730, "mtime": 1752080815952, "results": "1049", "hashOfConfig": "763"}, {"size": 20200, "mtime": 1754688352894, "results": "1050", "hashOfConfig": "763"}, {"size": 10373, "mtime": 1754782777333, "results": "1051", "hashOfConfig": "763"}, {"size": 2735, "mtime": 1752078895003, "results": "1052", "hashOfConfig": "763"}, {"size": 925, "mtime": 1752078895003, "results": "1053", "hashOfConfig": "763"}, {"size": 1213, "mtime": 1752078895003, "results": "1054", "hashOfConfig": "763"}, {"size": 8134, "mtime": 1752078895003, "results": "1055", "hashOfConfig": "763"}, {"size": 957, "mtime": 1752078895003, "results": "1056", "hashOfConfig": "763"}, {"size": 2264, "mtime": 1752078895003, "results": "1057", "hashOfConfig": "763"}, {"size": 1677, "mtime": 1752078895003, "results": "1058", "hashOfConfig": "763"}, {"size": 1034, "mtime": 1752078895003, "results": "1059", "hashOfConfig": "763"}, {"size": 5544, "mtime": 1752078895003, "results": "1060", "hashOfConfig": "763"}, {"size": 2483, "mtime": 1752078895019, "results": "1061", "hashOfConfig": "763"}, {"size": 1092, "mtime": 1752078895019, "results": "1062", "hashOfConfig": "763"}, {"size": 4532, "mtime": 1752078895019, "results": "1063", "hashOfConfig": "763"}, {"size": 6920, "mtime": 1752080815952, "results": "1064", "hashOfConfig": "763"}, {"size": 3574, "mtime": 1752080815952, "results": "1065", "hashOfConfig": "763"}, {"size": 794, "mtime": 1752078895019, "results": "1066", "hashOfConfig": "763"}, {"size": 1902, "mtime": 1752078895019, "results": "1067", "hashOfConfig": "763"}, {"size": 1420, "mtime": 1752078895023, "results": "1068", "hashOfConfig": "763"}, {"size": 24194, "mtime": 1752080815952, "results": "1069", "hashOfConfig": "763"}, {"size": 555, "mtime": 1752078895023, "results": "1070", "hashOfConfig": "763"}, {"size": 4100, "mtime": 1752078895023, "results": "1071", "hashOfConfig": "763"}, {"size": 15578, "mtime": 1752078895023, "results": "1072", "hashOfConfig": "763"}, {"size": 3228, "mtime": 1752078895023, "results": "1073", "hashOfConfig": "763"}, {"size": 3514, "mtime": 1752078895023, "results": "1074", "hashOfConfig": "763"}, {"size": 23175, "mtime": 1752078895023, "results": "1075", "hashOfConfig": "763"}, {"size": 2060, "mtime": 1752078895023, "results": "1076", "hashOfConfig": "763"}, {"size": 16492, "mtime": 1752078895023, "results": "1077", "hashOfConfig": "763"}, {"size": 1149, "mtime": 1752078895023, "results": "1078", "hashOfConfig": "763"}, {"size": 3631, "mtime": 1752078895023, "results": "1079", "hashOfConfig": "763"}, {"size": 1859, "mtime": 1752078895023, "results": "1080", "hashOfConfig": "763"}, {"size": 4207, "mtime": 1752078895023, "results": "1081", "hashOfConfig": "763"}, {"size": 5060, "mtime": 1752078895023, "results": "1082", "hashOfConfig": "763"}, {"size": 3993, "mtime": 1752078895023, "results": "1083", "hashOfConfig": "763"}, {"size": 3872, "mtime": 1752078895023, "results": "1084", "hashOfConfig": "763"}, {"size": 1420, "mtime": 1752078895023, "results": "1085", "hashOfConfig": "763"}, {"size": 4730, "mtime": 1752078895035, "results": "1086", "hashOfConfig": "763"}, {"size": 5956, "mtime": 1752078895035, "results": "1087", "hashOfConfig": "763"}, {"size": 244, "mtime": 1752078895035, "results": "1088", "hashOfConfig": "763"}, {"size": 671, "mtime": 1752080815952, "results": "1089", "hashOfConfig": "763"}, {"size": 9220, "mtime": 1752078895035, "results": "1090", "hashOfConfig": "763"}, {"size": 9703, "mtime": 1752080815952, "results": "1091", "hashOfConfig": "763"}, {"size": 10536, "mtime": 1752080815952, "results": "1092", "hashOfConfig": "763"}, {"size": 12559, "mtime": 1752078895035, "results": "1093", "hashOfConfig": "763"}, {"size": 2402, "mtime": 1752080815952, "results": "1094", "hashOfConfig": "763"}, {"size": 1517, "mtime": 1752078895003, "results": "1095", "hashOfConfig": "763"}, {"size": 1951, "mtime": 1752078895003, "results": "1096", "hashOfConfig": "763"}, {"size": 4017, "mtime": 1752078895035, "results": "1097", "hashOfConfig": "763"}, {"size": 3456, "mtime": 1752078895035, "results": "1098", "hashOfConfig": "763"}, {"size": 4833, "mtime": 1752078895035, "results": "1099", "hashOfConfig": "763"}, {"size": 3938, "mtime": 1752078895035, "results": "1100", "hashOfConfig": "763"}, {"size": 5522, "mtime": 1752078895035, "results": "1101", "hashOfConfig": "763"}, {"size": 5183, "mtime": 1752078895035, "results": "1102", "hashOfConfig": "763"}, {"size": 7170, "mtime": 1752078895035, "results": "1103", "hashOfConfig": "763"}, {"size": 8695, "mtime": 1752078895051, "results": "1104", "hashOfConfig": "763"}, {"size": 1462, "mtime": 1754682285579, "results": "1105", "hashOfConfig": "763"}, {"size": 2053, "mtime": 1752078895051, "results": "1106", "hashOfConfig": "763"}, {"size": 1135, "mtime": 1752078895051, "results": "1107", "hashOfConfig": "763"}, {"size": 7593, "mtime": 1754688394557, "results": "1108", "hashOfConfig": "763"}, {"size": 1655, "mtime": 1752078895051, "results": "1109", "hashOfConfig": "763"}, {"size": 6285, "mtime": 1754776494017, "results": "1110", "hashOfConfig": "763"}, {"size": 1388, "mtime": 1754774546147, "results": "1111", "hashOfConfig": "763"}, {"size": 8153, "mtime": 1752498088570, "results": "1112", "hashOfConfig": "763"}, {"size": 4771, "mtime": 1752485239443, "results": "1113", "hashOfConfig": "763"}, {"size": 5040, "mtime": 1753436763374, "results": "1114", "hashOfConfig": "763"}, {"size": 13600, "mtime": 1754827925914, "results": "1115", "hashOfConfig": "763"}, {"size": 1389, "mtime": 1752596854741, "results": "1116", "hashOfConfig": "763"}, {"size": 3608, "mtime": 1754054811704, "results": "1117", "hashOfConfig": "763"}, {"size": 14232, "mtime": 1752078895066, "results": "1118", "hashOfConfig": "763"}, {"size": 1626, "mtime": 1752078895066, "results": "1119", "hashOfConfig": "763"}, {"size": 14197, "mtime": 1752078895066, "results": "1120", "hashOfConfig": "763"}, {"size": 820, "mtime": 1752078895066, "results": "1121", "hashOfConfig": "763"}, {"size": 15316, "mtime": 1752078895066, "results": "1122", "hashOfConfig": "763"}, {"size": 1887, "mtime": 1752078895066, "results": "1123", "hashOfConfig": "763"}, {"size": 12380, "mtime": 1752078895066, "results": "1124", "hashOfConfig": "763"}, {"size": 1946, "mtime": 1752078895066, "results": "1125", "hashOfConfig": "763"}, {"size": 3999, "mtime": 1752078895066, "results": "1126", "hashOfConfig": "763"}, {"size": 6385, "mtime": 1752078895066, "results": "1127", "hashOfConfig": "763"}, {"size": 9551, "mtime": 1752078895066, "results": "1128", "hashOfConfig": "763"}, {"size": 13651, "mtime": 1752078895066, "results": "1129", "hashOfConfig": "763"}, {"size": 1826, "mtime": 1752078895066, "results": "1130", "hashOfConfig": "763"}, {"size": 1920, "mtime": 1752078895082, "results": "1131", "hashOfConfig": "763"}, {"size": 13936, "mtime": 1752078895082, "results": "1132", "hashOfConfig": "763"}, {"size": 1862, "mtime": 1752078895082, "results": "1133", "hashOfConfig": "763"}, {"size": 13015, "mtime": 1752078895082, "results": "1134", "hashOfConfig": "763"}, {"size": 13703, "mtime": 1752991606206, "results": "1135", "hashOfConfig": "763"}, {"size": 1899, "mtime": 1752078895086, "results": "1136", "hashOfConfig": "763"}, {"size": 11444, "mtime": 1752078895086, "results": "1137", "hashOfConfig": "763"}, {"size": 14967, "mtime": 1754685036558, "results": "1138", "hashOfConfig": "763"}, {"size": 852, "mtime": 1752078895086, "results": "1139", "hashOfConfig": "763"}, {"size": 1676, "mtime": 1753436763374, "results": "1140", "hashOfConfig": "763"}, {"size": 13805, "mtime": 1754781936396, "results": "1141", "hashOfConfig": "763"}, {"size": 759, "mtime": 1752588678090, "results": "1142", "hashOfConfig": "763"}, {"size": 4539, "mtime": 1754679915142, "results": "1143", "hashOfConfig": "763"}, {"size": 2734, "mtime": 1754682896407, "results": "1144", "hashOfConfig": "763"}, {"size": 9997, "mtime": 1752588678105, "results": "1145", "hashOfConfig": "763"}, {"size": 3634, "mtime": 1752078895086, "results": "1146", "hashOfConfig": "763"}, {"size": 10759, "mtime": 1752078895086, "results": "1147", "hashOfConfig": "763"}, {"size": 916, "mtime": 1754679309005, "results": "1148", "hashOfConfig": "763"}, {"size": 2307, "mtime": 1754679646492, "results": "1149", "hashOfConfig": "763"}, {"size": 7300, "mtime": 1754688132602, "results": "1150", "hashOfConfig": "763"}, {"size": 4953, "mtime": 1752945992633, "results": "1151", "hashOfConfig": "763"}, {"size": 2913, "mtime": 1752078895099, "results": "1152", "hashOfConfig": "763"}, {"size": 1680, "mtime": 1752213788048, "results": "1153", "hashOfConfig": "763"}, {"size": 586, "mtime": 1752078895100, "results": "1154", "hashOfConfig": "763"}, {"size": 6465, "mtime": 1754679811177, "results": "1155", "hashOfConfig": "763"}, {"size": 1230, "mtime": 1752078895100, "results": "1156", "hashOfConfig": "763"}, {"size": 3294, "mtime": 1754679775783, "results": "1157", "hashOfConfig": "763"}, {"size": 1506, "mtime": 1754818278715, "results": "1158", "hashOfConfig": "763"}, {"size": 1215, "mtime": 1753436763421, "results": "1159", "hashOfConfig": "763"}, {"size": 2508, "mtime": 1752078895195, "results": "1160", "hashOfConfig": "763"}, {"size": 852, "mtime": 1752078895240, "results": "1161", "hashOfConfig": "763"}, {"size": 4790, "mtime": 1752078895240, "results": "1162", "hashOfConfig": "763"}, {"size": 9134, "mtime": 1754779839125, "results": "1163", "hashOfConfig": "763"}, {"size": 696, "mtime": 1752078895240, "results": "1164", "hashOfConfig": "763"}, {"size": 3247, "mtime": 1752078895240, "results": "1165", "hashOfConfig": "763"}, {"size": 16794, "mtime": 1754649919219, "results": "1166", "hashOfConfig": "763"}, {"size": 6011, "mtime": 1754684965840, "results": "1167", "hashOfConfig": "763"}, {"size": 605, "mtime": 1752078895240, "results": "1168", "hashOfConfig": "763"}, {"size": 9700, "mtime": 1752686119805, "results": "1169", "hashOfConfig": "763"}, {"size": 706, "mtime": 1752078895240, "results": "1170", "hashOfConfig": "763"}, {"size": 1299, "mtime": 1752078895256, "results": "1171", "hashOfConfig": "763"}, {"size": 918, "mtime": 1752078895256, "results": "1172", "hashOfConfig": "763"}, {"size": 1155, "mtime": 1752078895256, "results": "1173", "hashOfConfig": "763"}, {"size": 1050, "mtime": 1752078895256, "results": "1174", "hashOfConfig": "763"}, {"size": 573, "mtime": 1752078895256, "results": "1175", "hashOfConfig": "763"}, {"size": 1580, "mtime": 1752078895256, "results": "1176", "hashOfConfig": "763"}, {"size": 766, "mtime": 1752078895256, "results": "1177", "hashOfConfig": "763"}, {"size": 3070, "mtime": 1752078895240, "results": "1178", "hashOfConfig": "763"}, {"size": 1187, "mtime": 1752078895240, "results": "1179", "hashOfConfig": "763"}, {"size": 560, "mtime": 1752078895240, "results": "1180", "hashOfConfig": "763"}, {"size": 10211, "mtime": 1754681172346, "results": "1181", "hashOfConfig": "763"}, {"size": 387, "mtime": 1752078895240, "results": "1182", "hashOfConfig": "763"}, {"size": 10905, "mtime": 1752078895240, "results": "1183", "hashOfConfig": "763"}, {"size": 1430, "mtime": 1754687983587, "results": "1184", "hashOfConfig": "763"}, {"size": 928, "mtime": 1752078895256, "results": "1185", "hashOfConfig": "763"}, {"size": 11881, "mtime": 1752078895256, "results": "1186", "hashOfConfig": "763"}, {"size": 5025, "mtime": 1754649942004, "results": "1187", "hashOfConfig": "763"}, {"size": 12508, "mtime": 1754649979094, "results": "1188", "hashOfConfig": "763"}, {"size": 8293, "mtime": 1752078895256, "results": "1189", "hashOfConfig": "763"}, {"size": 8100, "mtime": 1752078895256, "results": "1190", "hashOfConfig": "763"}, {"size": 468, "mtime": 1752078895240, "results": "1191", "hashOfConfig": "763"}, {"size": 8404, "mtime": 1752078895256, "results": "1192", "hashOfConfig": "763"}, {"size": 420, "mtime": 1752078895271, "results": "1193", "hashOfConfig": "763"}, {"size": 8986, "mtime": 1753531001344, "results": "1194", "hashOfConfig": "763"}, {"size": 2536, "mtime": 1752078895256, "results": "1195", "hashOfConfig": "763"}, {"size": 3620, "mtime": 1753514198416, "results": "1196", "hashOfConfig": "763"}, {"size": 2764, "mtime": 1752078895256, "results": "1197", "hashOfConfig": "763"}, {"size": 2336, "mtime": 1752078895256, "results": "1198", "hashOfConfig": "763"}, {"size": 15343, "mtime": 1752078895271, "results": "1199", "hashOfConfig": "763"}, {"size": 2399, "mtime": 1752078895271, "results": "1200", "hashOfConfig": "763"}, {"size": 2458, "mtime": 1753515135770, "results": "1201", "hashOfConfig": "763"}, {"size": 532, "mtime": 1754678641865, "results": "1202", "hashOfConfig": "763"}, {"size": 7698, "mtime": 1754678641851, "results": "1203", "hashOfConfig": "763"}, {"size": 2870, "mtime": 1754678641861, "results": "1204", "hashOfConfig": "763"}, {"size": 3578, "mtime": 1754678641862, "results": "1205", "hashOfConfig": "763"}, {"size": 3654, "mtime": 1754678641863, "results": "1206", "hashOfConfig": "763"}, {"size": 2711, "mtime": 1754678641864, "results": "1207", "hashOfConfig": "763"}, {"size": 5073, "mtime": 1752078895240, "results": "1208", "hashOfConfig": "763"}, {"size": 468, "mtime": 1752078895293, "results": "1209", "hashOfConfig": "763"}, {"size": 2875, "mtime": 1752078895293, "results": "1210", "hashOfConfig": "763"}, {"size": 7631, "mtime": 1754787417179, "results": "1211", "hashOfConfig": "763"}, {"size": 3476, "mtime": 1752078895293, "results": "1212", "hashOfConfig": "763"}, {"size": 2968, "mtime": 1752078895293, "results": "1213", "hashOfConfig": "763"}, {"size": 7352, "mtime": 1754392096900, "results": "1214", "hashOfConfig": "763"}, {"size": 543, "mtime": 1752078895293, "results": "1215", "hashOfConfig": "763"}, {"size": 881, "mtime": 1752078895303, "results": "1216", "hashOfConfig": "763"}, {"size": 563, "mtime": 1752078895303, "results": "1217", "hashOfConfig": "763"}, {"size": 5611, "mtime": 1752078895303, "results": "1218", "hashOfConfig": "763"}, {"size": 2417, "mtime": 1752078895303, "results": "1219", "hashOfConfig": "763"}, {"size": 2958, "mtime": 1752078895303, "results": "1220", "hashOfConfig": "763"}, {"size": 140, "mtime": 1752078895303, "results": "1221", "hashOfConfig": "763"}, {"size": 5043, "mtime": 1752078895303, "results": "1222", "hashOfConfig": "763"}, {"size": 3818, "mtime": 1752078895303, "results": "1223", "hashOfConfig": "763"}, {"size": 6246, "mtime": 1752078895303, "results": "1224", "hashOfConfig": "763"}, {"size": 6801, "mtime": 1752078895303, "results": "1225", "hashOfConfig": "763"}, {"size": 2232, "mtime": 1752078895303, "results": "1226", "hashOfConfig": "763"}, {"size": 1235, "mtime": 1752078895303, "results": "1227", "hashOfConfig": "763"}, {"size": 2119, "mtime": 1752078895293, "results": "1228", "hashOfConfig": "763"}, {"size": 3747, "mtime": 1752078895293, "results": "1229", "hashOfConfig": "763"}, {"size": 2394, "mtime": 1752078895303, "results": "1230", "hashOfConfig": "763"}, {"size": 431, "mtime": 1752078895303, "results": "1231", "hashOfConfig": "763"}, {"size": 1606, "mtime": 1752078895303, "results": "1232", "hashOfConfig": "763"}, {"size": 3178, "mtime": 1753436763424, "results": "1233", "hashOfConfig": "763"}, {"size": 2689, "mtime": 1752078895303, "results": "1234", "hashOfConfig": "763"}, {"size": 1735, "mtime": 1752080815962, "results": "1235", "hashOfConfig": "763"}, {"size": 1639, "mtime": 1752080815962, "results": "1236", "hashOfConfig": "763"}, {"size": 4133, "mtime": 1754689148380, "results": "1237", "hashOfConfig": "763"}, {"size": 2860, "mtime": 1752078895114, "results": "1238", "hashOfConfig": "763"}, {"size": 8058, "mtime": 1752078895100, "results": "1239", "hashOfConfig": "763"}, {"size": 19772, "mtime": 1752080815952, "results": "1240", "hashOfConfig": "763"}, {"size": 6916, "mtime": 1752078895100, "results": "1241", "hashOfConfig": "763"}, {"size": 5528, "mtime": 1754689166011, "results": "1242", "hashOfConfig": "763"}, {"size": 2806, "mtime": 1752078895100, "results": "1243", "hashOfConfig": "763"}, {"size": 8383, "mtime": 1753436763388, "results": "1244", "hashOfConfig": "763"}, {"size": 14166, "mtime": 1754688410700, "results": "1245", "hashOfConfig": "763"}, {"size": 6718, "mtime": 1752078895111, "results": "1246", "hashOfConfig": "763"}, {"size": 5950, "mtime": 1753436763388, "results": "1247", "hashOfConfig": "763"}, {"size": 3064, "mtime": 1752078895114, "results": "1248", "hashOfConfig": "763"}, {"size": 1050, "mtime": 1752078895114, "results": "1249", "hashOfConfig": "763"}, {"size": 22169, "mtime": 1752078895114, "results": "1250", "hashOfConfig": "763"}, {"size": 9131, "mtime": 1753436763388, "results": "1251", "hashOfConfig": "763"}, {"size": 4097, "mtime": 1754689319994, "results": "1252", "hashOfConfig": "763"}, {"size": 1237, "mtime": 1752078895114, "results": "1253", "hashOfConfig": "763"}, {"size": 456, "mtime": 1752078895114, "results": "1254", "hashOfConfig": "763"}, {"size": 13308, "mtime": 1754822114215, "results": "1255", "hashOfConfig": "763"}, {"size": 16038, "mtime": 1754779682302, "results": "1256", "hashOfConfig": "763"}, {"size": 1555, "mtime": 1752078895123, "results": "1257", "hashOfConfig": "763"}, {"size": 12447, "mtime": 1752078895123, "results": "1258", "hashOfConfig": "763"}, {"size": 3075, "mtime": 1752078895123, "results": "1259", "hashOfConfig": "763"}, {"size": 2658, "mtime": 1752078895123, "results": "1260", "hashOfConfig": "763"}, {"size": 4697, "mtime": 1752078895123, "results": "1261", "hashOfConfig": "763"}, {"size": 22440, "mtime": 1753105300361, "results": "1262", "hashOfConfig": "763"}, {"size": 3730, "mtime": 1752078895123, "results": "1263", "hashOfConfig": "763"}, {"size": 12541, "mtime": 1753977611947, "results": "1264", "hashOfConfig": "763"}, {"size": 3044, "mtime": 1752078895129, "results": "1265", "hashOfConfig": "763"}, {"size": 5097, "mtime": 1752078895129, "results": "1266", "hashOfConfig": "763"}, {"size": 9601, "mtime": 1754689376978, "results": "1267", "hashOfConfig": "763"}, {"size": 2253, "mtime": 1752078895123, "results": "1268", "hashOfConfig": "763"}, {"size": 3263, "mtime": 1753436763388, "results": "1269", "hashOfConfig": "763"}, {"size": 5326, "mtime": 1752080815962, "results": "1270", "hashOfConfig": "763"}, {"size": 5995, "mtime": 1752078895316, "results": "1271", "hashOfConfig": "763"}, {"size": 3946, "mtime": 1752078895319, "results": "1272", "hashOfConfig": "763"}, {"size": 8264, "mtime": 1752080815962, "results": "1273", "hashOfConfig": "763"}, {"size": 3007, "mtime": 1752078895320, "results": "1274", "hashOfConfig": "763"}, {"size": 4189, "mtime": 1752078895321, "results": "1275", "hashOfConfig": "763"}, {"size": 9778, "mtime": 1752078895321, "results": "1276", "hashOfConfig": "763"}, {"size": 10169, "mtime": 1752080815962, "results": "1277", "hashOfConfig": "763"}, {"size": 10217, "mtime": 1752080815971, "results": "1278", "hashOfConfig": "763"}, {"size": 6291, "mtime": 1752078895323, "results": "1279", "hashOfConfig": "763"}, {"size": 7264, "mtime": 1752078895323, "results": "1280", "hashOfConfig": "763"}, {"size": 7194, "mtime": 1752078895323, "results": "1281", "hashOfConfig": "763"}, {"size": 3629, "mtime": 1752682891985, "results": "1282", "hashOfConfig": "763"}, {"size": 8662, "mtime": 1752078895323, "results": "1283", "hashOfConfig": "763"}, {"size": 4435, "mtime": 1753101910685, "results": "1284", "hashOfConfig": "763"}, {"size": 19439, "mtime": 1752078895335, "results": "1285", "hashOfConfig": "763"}, {"size": 7315, "mtime": 1752078895335, "results": "1286", "hashOfConfig": "763"}, {"size": 8073, "mtime": 1752078895335, "results": "1287", "hashOfConfig": "763"}, {"size": 2529, "mtime": 1753794776171, "results": "1288", "hashOfConfig": "763"}, {"size": 8788, "mtime": 1752080815971, "results": "1289", "hashOfConfig": "763"}, {"size": 459, "mtime": 1752683108071, "results": "1290", "hashOfConfig": "763"}, {"size": 919, "mtime": 1752078895323, "results": "1291", "hashOfConfig": "763"}, {"size": 26895, "mtime": 1754649551289, "results": "1292", "hashOfConfig": "763"}, {"size": 6952, "mtime": 1754667999946, "results": "1293", "hashOfConfig": "763"}, {"size": 3447, "mtime": 1752078895323, "results": "1294", "hashOfConfig": "763"}, {"size": 41540, "mtime": 1752925036472, "results": "1295", "hashOfConfig": "763"}, {"size": 18331, "mtime": 1752925036472, "results": "1296", "hashOfConfig": "763"}, {"size": 2729, "mtime": 1754649572073, "results": "1297", "hashOfConfig": "763"}, {"size": 935, "mtime": 1752078895335, "results": "1298", "hashOfConfig": "763"}, {"size": 5900, "mtime": 1754787189956, "results": "1299", "hashOfConfig": "763"}, {"size": 1935, "mtime": 1752078895335, "results": "1300", "hashOfConfig": "763"}, {"size": 8518, "mtime": 1754787731339, "results": "1301", "hashOfConfig": "763"}, {"size": 7855, "mtime": 1754392096910, "results": "1302", "hashOfConfig": "763"}, {"size": 8965, "mtime": 1754392096910, "results": "1303", "hashOfConfig": "763"}, {"size": 9258, "mtime": 1754687423156, "results": "1304", "hashOfConfig": "763"}, {"size": 7671, "mtime": 1754678664660, "results": "1305", "hashOfConfig": "763"}, {"size": 5281, "mtime": 1752078895335, "results": "1306", "hashOfConfig": "763"}, {"size": 5670, "mtime": 1753436763427, "results": "1307", "hashOfConfig": "763"}, {"size": 4400, "mtime": 1752078895335, "results": "1308", "hashOfConfig": "763"}, {"size": 5504, "mtime": 1753436763427, "results": "1309", "hashOfConfig": "763"}, {"size": 638, "mtime": 1752078895335, "results": "1310", "hashOfConfig": "763"}, {"size": 2119, "mtime": 1752078895335, "results": "1311", "hashOfConfig": "763"}, {"size": 4021, "mtime": 1752078895335, "results": "1312", "hashOfConfig": "763"}, {"size": 1680, "mtime": 1752078895335, "results": "1313", "hashOfConfig": "763"}, {"size": 1150, "mtime": 1752078895335, "results": "1314", "hashOfConfig": "763"}, {"size": 1677, "mtime": 1752078895335, "results": "1315", "hashOfConfig": "763"}, {"size": 2466, "mtime": 1752078895335, "results": "1316", "hashOfConfig": "763"}, {"size": 2199, "mtime": 1754076729971, "results": "1317", "hashOfConfig": "763"}, {"size": 2995, "mtime": 1752078895335, "results": "1318", "hashOfConfig": "763"}, {"size": 2081, "mtime": 1752078895350, "results": "1319", "hashOfConfig": "763"}, {"size": 5798, "mtime": 1752078895350, "results": "1320", "hashOfConfig": "763"}, {"size": 2814, "mtime": 1752078895350, "results": "1321", "hashOfConfig": "763"}, {"size": 10137, "mtime": 1752078895350, "results": "1322", "hashOfConfig": "763"}, {"size": 1258, "mtime": 1752078895350, "results": "1323", "hashOfConfig": "763"}, {"size": 833, "mtime": 1752078895350, "results": "1324", "hashOfConfig": "763"}, {"size": 4833, "mtime": 1752078895350, "results": "1325", "hashOfConfig": "763"}, {"size": 4119, "mtime": 1752078895350, "results": "1326", "hashOfConfig": "763"}, {"size": 8541, "mtime": 1752078895350, "results": "1327", "hashOfConfig": "763"}, {"size": 3926, "mtime": 1752078895350, "results": "1328", "hashOfConfig": "763"}, {"size": 2331, "mtime": 1752078895350, "results": "1329", "hashOfConfig": "763"}, {"size": 988, "mtime": 1752078895350, "results": "1330", "hashOfConfig": "763"}, {"size": 635, "mtime": 1752078895350, "results": "1331", "hashOfConfig": "763"}, {"size": 6792, "mtime": 1752078895350, "results": "1332", "hashOfConfig": "763"}, {"size": 3090, "mtime": 1752078895350, "results": "1333", "hashOfConfig": "763"}, {"size": 1683, "mtime": 1752078895350, "results": "1334", "hashOfConfig": "763"}, {"size": 771, "mtime": 1752078895350, "results": "1335", "hashOfConfig": "763"}, {"size": 1511, "mtime": 1752078895350, "results": "1336", "hashOfConfig": "763"}, {"size": 8203, "mtime": 1752078895350, "results": "1337", "hashOfConfig": "763"}, {"size": 1703, "mtime": 1752078895350, "results": "1338", "hashOfConfig": "763"}, {"size": 2479, "mtime": 1752078895350, "results": "1339", "hashOfConfig": "763"}, {"size": 6438, "mtime": 1752078895350, "results": "1340", "hashOfConfig": "763"}, {"size": 732, "mtime": 1752078895350, "results": "1341", "hashOfConfig": "763"}, {"size": 4244, "mtime": 1752078895350, "results": "1342", "hashOfConfig": "763"}, {"size": 22737, "mtime": 1752078895366, "results": "1343", "hashOfConfig": "763"}, {"size": 289, "mtime": 1752078895366, "results": "1344", "hashOfConfig": "763"}, {"size": 2064, "mtime": 1752078895366, "results": "1345", "hashOfConfig": "763"}, {"size": 589, "mtime": 1752078895366, "results": "1346", "hashOfConfig": "763"}, {"size": 1208, "mtime": 1752078895366, "results": "1347", "hashOfConfig": "763"}, {"size": 2564, "mtime": 1752078895366, "results": "1348", "hashOfConfig": "763"}, {"size": 2035, "mtime": 1752078895366, "results": "1349", "hashOfConfig": "763"}, {"size": 777, "mtime": 1752078895366, "results": "1350", "hashOfConfig": "763"}, {"size": 3457, "mtime": 1752078895366, "results": "1351", "hashOfConfig": "763"}, {"size": 1952, "mtime": 1752078895366, "results": "1352", "hashOfConfig": "763"}, {"size": 145, "mtime": 1752078895366, "results": "1353", "hashOfConfig": "763"}, {"size": 831, "mtime": 1752078895366, "results": "1354", "hashOfConfig": "763"}, {"size": 3614, "mtime": 1753105671578, "results": "1355", "hashOfConfig": "763"}, {"size": 2546, "mtime": 1754780733076, "results": "1356", "hashOfConfig": "763"}, {"size": 5846, "mtime": 1754789545888, "results": "1357", "hashOfConfig": "763"}, {"size": 668, "mtime": 1752078895397, "results": "1358", "hashOfConfig": "763"}, {"size": 3381, "mtime": 1754789545889, "results": "1359", "hashOfConfig": "763"}, {"size": 2667, "mtime": 1754779771884, "results": "1360", "hashOfConfig": "763"}, {"size": 3269, "mtime": 1754789545889, "results": "1361", "hashOfConfig": "763"}, {"size": 1716, "mtime": 1754782056461, "results": "1362", "hashOfConfig": "763"}, {"size": 761, "mtime": 1754688103699, "results": "1363", "hashOfConfig": "763"}, {"size": 2364, "mtime": 1752078895397, "results": "1364", "hashOfConfig": "763"}, {"size": 10170, "mtime": 1754789545890, "results": "1365", "hashOfConfig": "763"}, {"size": 178, "mtime": 1752078895397, "results": "1366", "hashOfConfig": "763"}, {"size": 8054, "mtime": 1752925036485, "results": "1367", "hashOfConfig": "763"}, {"size": 198, "mtime": 1752078895397, "results": "1368", "hashOfConfig": "763"}, {"size": 6353, "mtime": 1752078895413, "results": "1369", "hashOfConfig": "763"}, {"size": 9007, "mtime": 1754790396293, "results": "1370", "hashOfConfig": "763"}, {"size": 14262, "mtime": 1754789206287, "results": "1371", "hashOfConfig": "763"}, {"size": 1712, "mtime": 1753101910685, "results": "1372", "hashOfConfig": "763"}, {"size": 5843, "mtime": 1754789381662, "results": "1373", "hashOfConfig": "763"}, {"size": 7801, "mtime": 1754790923755, "results": "1374", "hashOfConfig": "763"}, {"size": 3402, "mtime": 1754787876129, "results": "1375", "hashOfConfig": "763"}, {"size": 651, "mtime": 1752078895413, "results": "1376", "hashOfConfig": "763"}, {"size": 878, "mtime": 1752078895413, "results": "1377", "hashOfConfig": "763"}, {"size": 5991, "mtime": 1754810406599, "results": "1378", "hashOfConfig": "763"}, {"size": 1318, "mtime": 1752078895413, "results": "1379", "hashOfConfig": "763"}, {"size": 152, "mtime": 1752078895413, "results": "1380", "hashOfConfig": "763"}, {"size": 1407, "mtime": 1753070813299, "results": "1381", "hashOfConfig": "763"}, {"size": 3482, "mtime": 1753106562909, "results": "1382", "hashOfConfig": "763"}, {"size": 2311, "mtime": 1753436763427, "results": "1383", "hashOfConfig": "763"}, {"size": 3622, "mtime": 1754817349314, "results": "1384", "hashOfConfig": "763"}, {"size": 10123, "mtime": 1754789690710, "results": "1385", "hashOfConfig": "763"}, {"size": 2324, "mtime": 1753794776175, "results": "1386", "hashOfConfig": "763"}, {"size": 3208, "mtime": 1754789217933, "results": "1387", "hashOfConfig": "763"}, {"size": 3822, "mtime": 1753030756833, "results": "1388", "hashOfConfig": "763"}, {"size": 3630, "mtime": 1753030782254, "results": "1389", "hashOfConfig": "763"}, {"size": 6461, "mtime": 1754334168034, "results": "1390", "hashOfConfig": "763"}, {"size": 1264, "mtime": 1752078895429, "results": "1391", "hashOfConfig": "763"}, {"size": 6440, "mtime": 1752078895429, "results": "1392", "hashOfConfig": "763"}, {"size": 7233, "mtime": 1752078895429, "results": "1393", "hashOfConfig": "763"}, {"size": 22742, "mtime": 1752078895429, "results": "1394", "hashOfConfig": "763"}, {"size": 2503, "mtime": 1752078895429, "results": "1395", "hashOfConfig": "763"}, {"size": 17737, "mtime": 1752078895444, "results": "1396", "hashOfConfig": "763"}, {"size": 1604, "mtime": 1752078895444, "results": "1397", "hashOfConfig": "763"}, {"size": 4395, "mtime": 1752078895444, "results": "1398", "hashOfConfig": "763"}, {"size": 25034, "mtime": 1752078895444, "results": "1399", "hashOfConfig": "763"}, {"size": 2857, "mtime": 1752078895444, "results": "1400", "hashOfConfig": "763"}, {"size": 1425, "mtime": 1752692226155, "results": "1401", "hashOfConfig": "763"}, {"size": 3060, "mtime": 1752080816015, "results": "1402", "hashOfConfig": "763"}, {"size": 4548, "mtime": 1754684670660, "results": "1403", "hashOfConfig": "763"}, {"size": 22694, "mtime": 1754810602194, "results": "1404", "hashOfConfig": "763"}, {"size": 1344, "mtime": 1752078895508, "results": "1405", "hashOfConfig": "763"}, {"size": 2864, "mtime": 1754684860825, "results": "1406", "hashOfConfig": "763"}, {"size": 2207, "mtime": 1754846138798, "results": "1407", "hashOfConfig": "763"}, {"size": 504, "mtime": 1753101910738, "results": "1408", "hashOfConfig": "763"}, {"size": 416, "mtime": 1752078895520, "results": "1409", "hashOfConfig": "763"}, {"size": 1666, "mtime": 1753101910738, "results": "1410", "hashOfConfig": "763"}, {"size": 5564, "mtime": 1754647879393, "results": "1411", "hashOfConfig": "763"}, {"size": 3163, "mtime": 1752078895525, "results": "1412", "hashOfConfig": "763"}, {"size": 4249, "mtime": 1752078895525, "results": "1413", "hashOfConfig": "763"}, {"size": 6892, "mtime": 1752685339243, "results": "1414", "hashOfConfig": "763"}, {"size": 4659, "mtime": 1752078895525, "results": "1415", "hashOfConfig": "763"}, {"size": 5890, "mtime": 1754054911028, "results": "1416", "hashOfConfig": "763"}, {"size": 1337, "mtime": 1752078895525, "results": "1417", "hashOfConfig": "763"}, {"size": 3715, "mtime": 1753436763427, "results": "1418", "hashOfConfig": "763"}, {"size": 5943, "mtime": 1752078895525, "results": "1419", "hashOfConfig": "763"}, {"size": 9375, "mtime": 1753436763427, "results": "1420", "hashOfConfig": "763"}, {"size": 10403, "mtime": 1753436763427, "results": "1421", "hashOfConfig": "763"}, {"size": 4149, "mtime": 1753436763442, "results": "1422", "hashOfConfig": "763"}, {"size": 7483, "mtime": 1752078895525, "results": "1423", "hashOfConfig": "763"}, {"size": 5633, "mtime": 1753436763442, "results": "1424", "hashOfConfig": "763"}, {"size": 6872, "mtime": 1752078895540, "results": "1425", "hashOfConfig": "763"}, {"size": 5929, "mtime": 1752078895540, "results": "1426", "hashOfConfig": "763"}, {"size": 1824, "mtime": 1752078895540, "results": "1427", "hashOfConfig": "763"}, {"size": 2184, "mtime": 1752078895540, "results": "1428", "hashOfConfig": "763"}, {"size": 5432, "mtime": 1752685361427, "results": "1429", "hashOfConfig": "763"}, {"size": 7773, "mtime": 1752080816015, "results": "1430", "hashOfConfig": "763"}, {"size": 1869, "mtime": 1753436763442, "results": "1431", "hashOfConfig": "763"}, {"size": 6965, "mtime": 1752078895540, "results": "1432", "hashOfConfig": "763"}, {"size": 7497, "mtime": 1752427779267, "results": "1433", "hashOfConfig": "763"}, {"size": 10358, "mtime": 1754789624527, "results": "1434", "hashOfConfig": "763"}, {"size": 6604, "mtime": 1752078895525, "results": "1435", "hashOfConfig": "763"}, {"size": 12212, "mtime": 1754667481425, "results": "1436", "hashOfConfig": "763"}, {"size": 7340, "mtime": 1754666000084, "results": "1437", "hashOfConfig": "763"}, {"size": 10056, "mtime": 1754665923000, "results": "1438", "hashOfConfig": "763"}, {"size": 8667, "mtime": 1754649046686, "results": "1439", "hashOfConfig": "763"}, {"size": 23962, "mtime": 1754791953316, "results": "1440", "hashOfConfig": "763"}, {"size": 7622, "mtime": 1754793633869, "results": "1441", "hashOfConfig": "763"}, {"size": 9445, "mtime": 1754810391518, "results": "1442", "hashOfConfig": "763"}, {"size": 30747, "mtime": 1754787776566, "results": "1443", "hashOfConfig": "763"}, {"size": 2443, "mtime": 1754046892204, "results": "1444", "hashOfConfig": "763"}, {"size": 5530, "mtime": 1754666022221, "results": "1445", "hashOfConfig": "763"}, {"size": 15355, "mtime": 1754682915822, "results": "1446", "hashOfConfig": "763"}, {"size": 5035, "mtime": 1754679452962, "results": "1447", "hashOfConfig": "763"}, {"size": 5021, "mtime": 1754817678305, "results": "1448", "hashOfConfig": "763"}, {"size": 4639, "mtime": 1754815871709, "results": "1449", "hashOfConfig": "763"}, {"size": 7275, "mtime": 1754817713115, "results": "1450", "hashOfConfig": "763"}, {"size": 3746, "mtime": 1754835699417, "results": "1451", "hashOfConfig": "763"}, {"size": 3549, "mtime": 1754822538665, "results": "1452", "hashOfConfig": "763"}, {"size": 1627, "mtime": 1754785594930, "results": "1453", "hashOfConfig": "763"}, {"size": 9326, "mtime": 1754846169946, "results": "1454", "hashOfConfig": "763"}, {"size": 734, "mtime": 1754744986121, "results": "1455", "hashOfConfig": "763"}, {"size": 3442, "mtime": 1754787439029, "results": "1456", "hashOfConfig": "763"}, {"size": 3916, "mtime": 1754817637783, "results": "1457", "hashOfConfig": "763"}, {"size": 1010, "mtime": 1754775474424, "results": "1458", "hashOfConfig": "763"}, {"size": 15734, "mtime": 1754817535105, "results": "1459", "hashOfConfig": "763"}, {"size": 1672, "mtime": 1754775458107, "results": "1460", "hashOfConfig": "763"}, {"size": 2748, "mtime": 1754817738074, "results": "1461", "hashOfConfig": "763"}, {"size": 4936, "mtime": 1754817764186, "results": "1462", "hashOfConfig": "763"}, {"size": 2133, "mtime": 1754786033444, "results": "1463", "hashOfConfig": "763"}, {"size": 3398, "mtime": 1754785867464, "results": "1464", "hashOfConfig": "763"}, {"size": 3624, "mtime": 1754819121786, "results": "1465", "hashOfConfig": "763"}, {"size": 3157, "mtime": 1754813604955, "results": "1466", "hashOfConfig": "763"}, {"size": 2866, "mtime": 1754815894679, "results": "1467", "hashOfConfig": "763"}, {"size": 10740, "mtime": 1754811667100, "results": "1468", "hashOfConfig": "763"}, {"size": 5347, "mtime": 1754811218107, "results": "1469", "hashOfConfig": "763"}, {"size": 2980, "mtime": 1754818632525, "results": "1470", "hashOfConfig": "763"}, {"size": 3613, "mtime": 1754822029820, "results": "1471", "hashOfConfig": "763"}, {"size": 9951, "mtime": 1754818532663, "results": "1472", "hashOfConfig": "763"}, {"size": 2866, "mtime": 1754815876161, "results": "1473", "hashOfConfig": "763"}, {"size": 7652, "mtime": 1754821718153, "results": "1474", "hashOfConfig": "763"}, {"size": 10532, "mtime": 1754813092218, "results": "1475", "hashOfConfig": "763"}, {"size": 3016, "mtime": 1754818658781, "results": "1476", "hashOfConfig": "763"}, {"size": 3137, "mtime": 1754818699816, "results": "1477", "hashOfConfig": "763"}, {"size": 8267, "mtime": 1754812273848, "results": "1478", "hashOfConfig": "763"}, {"size": 14283, "mtime": 1754818223681, "results": "1479", "hashOfConfig": "763"}, {"size": 4838, "mtime": 1754822662834, "results": "1480", "hashOfConfig": "763"}, {"size": 6281, "mtime": 1754818259855, "results": "1481", "hashOfConfig": "763"}, {"size": 11799, "mtime": 1754788027400, "results": "1482", "hashOfConfig": "763"}, {"size": 6684, "mtime": 1754784554253, "results": "1483", "hashOfConfig": "763"}, {"size": 11049, "mtime": 1754784535134, "results": "1484", "hashOfConfig": "763"}, {"size": 6955, "mtime": 1754784610694, "results": "1485", "hashOfConfig": "763"}, {"size": 5772, "mtime": 1754816716691, "results": "1486", "hashOfConfig": "763"}, {"size": 4847, "mtime": 1754787599533, "results": "1487", "hashOfConfig": "763"}, {"size": 9090, "mtime": 1754817193936, "results": "1488", "hashOfConfig": "763"}, {"size": 6795, "mtime": 1754817509237, "results": "1489", "hashOfConfig": "763"}, {"size": 8517, "mtime": 1754813744664, "results": "1490", "hashOfConfig": "763"}, {"size": 7479, "mtime": 1754816722827, "results": "1491", "hashOfConfig": "763"}, {"size": 5763, "mtime": 1754822046455, "results": "1492", "hashOfConfig": "763"}, {"size": 9024, "mtime": 1754818157541, "results": "1493", "hashOfConfig": "763"}, {"size": 3916, "mtime": 1754813558557, "results": "1494", "hashOfConfig": "763"}, {"size": 8996, "mtime": 1754818193004, "results": "1495", "hashOfConfig": "763"}, {"size": 9738, "mtime": 1754817365973, "results": "1496", "hashOfConfig": "763"}, {"size": 5413, "mtime": 1754840655599, "results": "1497", "hashOfConfig": "763"}, {"size": 7548, "mtime": 1754840668869, "results": "1498", "hashOfConfig": "763"}, {"size": 4323, "mtime": 1754810198621, "results": "1499", "hashOfConfig": "763"}, {"size": 2394, "mtime": 1754806644376, "results": "1500", "hashOfConfig": "763"}, {"size": 1512, "mtime": 1754817225492, "results": "1501", "hashOfConfig": "763"}, {"size": 5397, "mtime": 1754835772790, "results": "1502", "hashOfConfig": "763"}, {"size": 8392, "mtime": 1754846157918, "results": "1503", "hashOfConfig": "763"}, {"size": 4311, "mtime": 1754840655599, "results": "1504", "hashOfConfig": "763"}, {"size": 2912, "mtime": 1754846841146, "results": "1505", "hashOfConfig": "763"}, {"size": 8906, "mtime": 1754847401448, "results": "1506", "hashOfConfig": "763"}, {"size": 17879, "mtime": 1754848039154, "results": "1507", "hashOfConfig": "763"}, {"size": 10813, "mtime": 1754848087801, "results": "1508", "hashOfConfig": "763"}, {"size": 7899, "mtime": 1754847330767, "results": "1509", "hashOfConfig": "763"}, {"size": 919, "mtime": 1754846972126, "results": "1510", "hashOfConfig": "763"}, {"size": 3474, "mtime": 1754847591749, "results": "1511", "hashOfConfig": "763"}, {"size": 3125, "mtime": 1754847644312, "results": "1512", "hashOfConfig": "763"}, {"size": 3653, "mtime": 1754847553497, "results": "1513", "hashOfConfig": "763"}, {"size": 4203, "mtime": 1754847855921, "results": "1514", "hashOfConfig": "763"}, {"size": 2489, "mtime": 1754848054106, "results": "1515", "hashOfConfig": "763"}, {"size": 5689, "mtime": 1754847573988, "results": "1516", "hashOfConfig": "763"}, {"size": 4604, "mtime": 1754847208628, "results": "1517", "hashOfConfig": "763"}, {"size": 4166, "mtime": 1754847240963, "results": "1518", "hashOfConfig": "763"}, {"size": 4280, "mtime": 1754847224814, "results": "1519", "hashOfConfig": "763"}, {"size": 14547, "mtime": 1754847975439, "results": "1520", "hashOfConfig": "763"}, {"size": 15388, "mtime": 1754847919344, "results": "1521", "hashOfConfig": "763"}, {"size": 6321, "mtime": 1754847832172, "results": "1522", "hashOfConfig": "763"}, {"size": 1475, "mtime": 1754847082730, "results": "1523", "hashOfConfig": "763"}, {"filePath": "1524", "messages": "1525", "suppressedMessages": "1526", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "3j0uch", {"filePath": "1527", "messages": "1528", "suppressedMessages": "1529", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1530", "messages": "1531", "suppressedMessages": "1532", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1533", "messages": "1534", "suppressedMessages": "1535", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1536", "messages": "1537", "suppressedMessages": "1538", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1539", "messages": "1540", "suppressedMessages": "1541", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1542", "messages": "1543", "suppressedMessages": "1544", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1545", "messages": "1546", "suppressedMessages": "1547", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1548", "messages": "1549", "suppressedMessages": "1550", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1551", "messages": "1552", "suppressedMessages": "1553", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1554", "messages": "1555", "suppressedMessages": "1556", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1557", "messages": "1558", "suppressedMessages": "1559", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1560", "messages": "1561", "suppressedMessages": "1562", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1563", "messages": "1564", "suppressedMessages": "1565", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1566", "messages": "1567", "suppressedMessages": "1568", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1569", "messages": "1570", "suppressedMessages": "1571", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1572", "messages": "1573", "suppressedMessages": "1574", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1575", "messages": "1576", "suppressedMessages": "1577", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1578", "messages": "1579", "suppressedMessages": "1580", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1581", "messages": "1582", "suppressedMessages": "1583", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1584", "messages": "1585", "suppressedMessages": "1586", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1587", "messages": "1588", "suppressedMessages": "1589", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1590", "messages": "1591", "suppressedMessages": "1592", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1593", "messages": "1594", "suppressedMessages": "1595", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1596", "messages": "1597", "suppressedMessages": "1598", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1599", "messages": "1600", "suppressedMessages": "1601", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1602", "messages": "1603", "suppressedMessages": "1604", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1605", "messages": "1606", "suppressedMessages": "1607", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1608", "messages": "1609", "suppressedMessages": "1610", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1611", "messages": "1612", "suppressedMessages": "1613", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1614", "messages": "1615", "suppressedMessages": "1616", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1617", "messages": "1618", "suppressedMessages": "1619", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1620", "messages": "1621", "suppressedMessages": "1622", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1623", "messages": "1624", "suppressedMessages": "1625", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1626", "messages": "1627", "suppressedMessages": "1628", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1629", "messages": "1630", "suppressedMessages": "1631", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1632", "messages": "1633", "suppressedMessages": "1634", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1635", "messages": "1636", "suppressedMessages": "1637", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1638", "messages": "1639", "suppressedMessages": "1640", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1641", "messages": "1642", "suppressedMessages": "1643", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1644", "messages": "1645", "suppressedMessages": "1646", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1647", "messages": "1648", "suppressedMessages": "1649", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1650", "messages": "1651", "suppressedMessages": "1652", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1653", "messages": "1654", "suppressedMessages": "1655", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1656", "messages": "1657", "suppressedMessages": "1658", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1659", "messages": "1660", "suppressedMessages": "1661", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1662", "messages": "1663", "suppressedMessages": "1664", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1665", "messages": "1666", "suppressedMessages": "1667", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1668", "messages": "1669", "suppressedMessages": "1670", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1671", "messages": "1672", "suppressedMessages": "1673", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1674", "messages": "1675", "suppressedMessages": "1676", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1677", "messages": "1678", "suppressedMessages": "1679", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1680", "messages": "1681", "suppressedMessages": "1682", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1683", "messages": "1684", "suppressedMessages": "1685", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1686", "messages": "1687", "suppressedMessages": "1688", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1689", "messages": "1690", "suppressedMessages": "1691", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1692", "messages": "1693", "suppressedMessages": "1694", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1695", "messages": "1696", "suppressedMessages": "1697", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1698", "messages": "1699", "suppressedMessages": "1700", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1701", "messages": "1702", "suppressedMessages": "1703", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1704", "messages": "1705", "suppressedMessages": "1706", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1707", "messages": "1708", "suppressedMessages": "1709", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1710", "messages": "1711", "suppressedMessages": "1712", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1713", "messages": "1714", "suppressedMessages": "1715", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1716", "messages": "1717", "suppressedMessages": "1718", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1719", "messages": "1720", "suppressedMessages": "1721", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1722", "messages": "1723", "suppressedMessages": "1724", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1725", "messages": "1726", "suppressedMessages": "1727", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1728", "messages": "1729", "suppressedMessages": "1730", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1731", "messages": "1732", "suppressedMessages": "1733", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1734", "messages": "1735", "suppressedMessages": "1736", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1737", "messages": "1738", "suppressedMessages": "1739", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1740", "messages": "1741", "suppressedMessages": "1742", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1743", "messages": "1744", "suppressedMessages": "1745", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1746", "messages": "1747", "suppressedMessages": "1748", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1749", "messages": "1750", "suppressedMessages": "1751", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1752", "messages": "1753", "suppressedMessages": "1754", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1755", "messages": "1756", "suppressedMessages": "1757", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1758", "messages": "1759", "suppressedMessages": "1760", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1761", "messages": "1762", "suppressedMessages": "1763", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1764", "messages": "1765", "suppressedMessages": "1766", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1767", "messages": "1768", "suppressedMessages": "1769", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1770", "messages": "1771", "suppressedMessages": "1772", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1773", "messages": "1774", "suppressedMessages": "1775", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1776", "messages": "1777", "suppressedMessages": "1778", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1779", "messages": "1780", "suppressedMessages": "1781", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1782", "messages": "1783", "suppressedMessages": "1784", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1785", "messages": "1786", "suppressedMessages": "1787", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1788", "messages": "1789", "suppressedMessages": "1790", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1791", "messages": "1792", "suppressedMessages": "1793", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1794", "messages": "1795", "suppressedMessages": "1796", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1797", "messages": "1798", "suppressedMessages": "1799", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1800", "messages": "1801", "suppressedMessages": "1802", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1803", "messages": "1804", "suppressedMessages": "1805", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1806", "messages": "1807", "suppressedMessages": "1808", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1809", "messages": "1810", "suppressedMessages": "1811", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1812", "messages": "1813", "suppressedMessages": "1814", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1815", "messages": "1816", "suppressedMessages": "1817", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1818", "messages": "1819", "suppressedMessages": "1820", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1821", "messages": "1822", "suppressedMessages": "1823", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1824", "messages": "1825", "suppressedMessages": "1826", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1827", "messages": "1828", "suppressedMessages": "1829", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1830", "messages": "1831", "suppressedMessages": "1832", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1833", "messages": "1834", "suppressedMessages": "1835", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1836", "messages": "1837", "suppressedMessages": "1838", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1839", "messages": "1840", "suppressedMessages": "1841", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1842", "messages": "1843", "suppressedMessages": "1844", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1845", "messages": "1846", "suppressedMessages": "1847", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1848", "messages": "1849", "suppressedMessages": "1850", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1851", "messages": "1852", "suppressedMessages": "1853", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1854", "messages": "1855", "suppressedMessages": "1856", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1857", "messages": "1858", "suppressedMessages": "1859", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1860", "messages": "1861", "suppressedMessages": "1862", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1863", "messages": "1864", "suppressedMessages": "1865", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1866", "messages": "1867", "suppressedMessages": "1868", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1869", "messages": "1870", "suppressedMessages": "1871", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1872", "messages": "1873", "suppressedMessages": "1874", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1875", "messages": "1876", "suppressedMessages": "1877", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1878", "messages": "1879", "suppressedMessages": "1880", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1881", "messages": "1882", "suppressedMessages": "1883", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1884", "messages": "1885", "suppressedMessages": "1886", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1887", "messages": "1888", "suppressedMessages": "1889", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1890", "messages": "1891", "suppressedMessages": "1892", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1893", "messages": "1894", "suppressedMessages": "1895", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1896", "messages": "1897", "suppressedMessages": "1898", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1899", "messages": "1900", "suppressedMessages": "1901", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1902", "messages": "1903", "suppressedMessages": "1904", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1905", "messages": "1906", "suppressedMessages": "1907", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1908", "messages": "1909", "suppressedMessages": "1910", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1911", "messages": "1912", "suppressedMessages": "1913", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1914", "messages": "1915", "suppressedMessages": "1916", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1917", "messages": "1918", "suppressedMessages": "1919", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1920", "messages": "1921", "suppressedMessages": "1922", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1923", "messages": "1924", "suppressedMessages": "1925", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1926", "messages": "1927", "suppressedMessages": "1928", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1929", "messages": "1930", "suppressedMessages": "1931", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1932", "messages": "1933", "suppressedMessages": "1934", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1935", "messages": "1936", "suppressedMessages": "1937", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1938", "messages": "1939", "suppressedMessages": "1940", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1941", "messages": "1942", "suppressedMessages": "1943", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1944", "messages": "1945", "suppressedMessages": "1946", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1947", "messages": "1948", "suppressedMessages": "1949", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1950", "messages": "1951", "suppressedMessages": "1952", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1953", "messages": "1954", "suppressedMessages": "1955", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1956", "messages": "1957", "suppressedMessages": "1958", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1959", "messages": "1960", "suppressedMessages": "1961", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1962", "messages": "1963", "suppressedMessages": "1964", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1965", "messages": "1966", "suppressedMessages": "1967", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1968", "messages": "1969", "suppressedMessages": "1970", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1971", "messages": "1972", "suppressedMessages": "1973", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1974", "messages": "1975", "suppressedMessages": "1976", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1977", "messages": "1978", "suppressedMessages": "1979", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1980", "messages": "1981", "suppressedMessages": "1982", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1983", "messages": "1984", "suppressedMessages": "1985", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1986", "messages": "1987", "suppressedMessages": "1988", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1989", "messages": "1990", "suppressedMessages": "1991", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1992", "messages": "1993", "suppressedMessages": "1994", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1995", "messages": "1996", "suppressedMessages": "1997", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1998", "messages": "1999", "suppressedMessages": "2000", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2001", "messages": "2002", "suppressedMessages": "2003", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2004", "messages": "2005", "suppressedMessages": "2006", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2007", "messages": "2008", "suppressedMessages": "2009", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2010", "messages": "2011", "suppressedMessages": "2012", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2013", "messages": "2014", "suppressedMessages": "2015", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2016", "messages": "2017", "suppressedMessages": "2018", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2019", "messages": "2020", "suppressedMessages": "2021", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2022", "messages": "2023", "suppressedMessages": "2024", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2025", "messages": "2026", "suppressedMessages": "2027", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2028", "messages": "2029", "suppressedMessages": "2030", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2031", "messages": "2032", "suppressedMessages": "2033", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2034", "messages": "2035", "suppressedMessages": "2036", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2037", "messages": "2038", "suppressedMessages": "2039", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2040", "messages": "2041", "suppressedMessages": "2042", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2043", "messages": "2044", "suppressedMessages": "2045", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2046", "messages": "2047", "suppressedMessages": "2048", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2049", "messages": "2050", "suppressedMessages": "2051", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2052", "messages": "2053", "suppressedMessages": "2054", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2055", "messages": "2056", "suppressedMessages": "2057", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2058", "messages": "2059", "suppressedMessages": "2060", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2061", "messages": "2062", "suppressedMessages": "2063", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2064", "messages": "2065", "suppressedMessages": "2066", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2067", "messages": "2068", "suppressedMessages": "2069", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2070", "messages": "2071", "suppressedMessages": "2072", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2073", "messages": "2074", "suppressedMessages": "2075", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2076", "messages": "2077", "suppressedMessages": "2078", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2079", "messages": "2080", "suppressedMessages": "2081", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2082", "messages": "2083", "suppressedMessages": "2084", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2085", "messages": "2086", "suppressedMessages": "2087", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2088", "messages": "2089", "suppressedMessages": "2090", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2091", "messages": "2092", "suppressedMessages": "2093", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2094", "messages": "2095", "suppressedMessages": "2096", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2097", "messages": "2098", "suppressedMessages": "2099", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2100", "messages": "2101", "suppressedMessages": "2102", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2103", "messages": "2104", "suppressedMessages": "2105", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2106", "messages": "2107", "suppressedMessages": "2108", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2109", "messages": "2110", "suppressedMessages": "2111", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2112", "messages": "2113", "suppressedMessages": "2114", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2115", "messages": "2116", "suppressedMessages": "2117", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2118", "messages": "2119", "suppressedMessages": "2120", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2121", "messages": "2122", "suppressedMessages": "2123", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2124", "messages": "2125", "suppressedMessages": "2126", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2127", "messages": "2128", "suppressedMessages": "2129", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2130", "messages": "2131", "suppressedMessages": "2132", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2133", "messages": "2134", "suppressedMessages": "2135", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2136", "messages": "2137", "suppressedMessages": "2138", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2139", "messages": "2140", "suppressedMessages": "2141", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2142", "messages": "2143", "suppressedMessages": "2144", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2145", "messages": "2146", "suppressedMessages": "2147", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2148", "messages": "2149", "suppressedMessages": "2150", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2151", "messages": "2152", "suppressedMessages": "2153", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2154", "messages": "2155", "suppressedMessages": "2156", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2157", "messages": "2158", "suppressedMessages": "2159", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2160", "messages": "2161", "suppressedMessages": "2162", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2163", "messages": "2164", "suppressedMessages": "2165", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2166", "messages": "2167", "suppressedMessages": "2168", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2169", "messages": "2170", "suppressedMessages": "2171", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2172", "messages": "2173", "suppressedMessages": "2174", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2175", "messages": "2176", "suppressedMessages": "2177", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2178", "messages": "2179", "suppressedMessages": "2180", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2181", "messages": "2182", "suppressedMessages": "2183", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2184", "messages": "2185", "suppressedMessages": "2186", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2187", "messages": "2188", "suppressedMessages": "2189", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2190", "messages": "2191", "suppressedMessages": "2192", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2193", "messages": "2194", "suppressedMessages": "2195", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2196", "messages": "2197", "suppressedMessages": "2198", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2199", "messages": "2200", "suppressedMessages": "2201", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2202", "messages": "2203", "suppressedMessages": "2204", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2205", "messages": "2206", "suppressedMessages": "2207", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2208", "messages": "2209", "suppressedMessages": "2210", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2211", "messages": "2212", "suppressedMessages": "2213", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2214", "messages": "2215", "suppressedMessages": "2216", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2217", "messages": "2218", "suppressedMessages": "2219", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2220", "messages": "2221", "suppressedMessages": "2222", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2223", "messages": "2224", "suppressedMessages": "2225", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2226", "messages": "2227", "suppressedMessages": "2228", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2229", "messages": "2230", "suppressedMessages": "2231", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2232", "messages": "2233", "suppressedMessages": "2234", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2235", "messages": "2236", "suppressedMessages": "2237", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2238", "messages": "2239", "suppressedMessages": "2240", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2241", "messages": "2242", "suppressedMessages": "2243", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2244", "messages": "2245", "suppressedMessages": "2246", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2247", "messages": "2248", "suppressedMessages": "2249", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2250", "messages": "2251", "suppressedMessages": "2252", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2253", "messages": "2254", "suppressedMessages": "2255", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2256", "messages": "2257", "suppressedMessages": "2258", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2259", "messages": "2260", "suppressedMessages": "2261", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2262", "messages": "2263", "suppressedMessages": "2264", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2265", "messages": "2266", "suppressedMessages": "2267", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2268", "messages": "2269", "suppressedMessages": "2270", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2271", "messages": "2272", "suppressedMessages": "2273", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2274", "messages": "2275", "suppressedMessages": "2276", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2277", "messages": "2278", "suppressedMessages": "2279", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2280", "messages": "2281", "suppressedMessages": "2282", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2283", "messages": "2284", "suppressedMessages": "2285", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2286", "messages": "2287", "suppressedMessages": "2288", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2289", "messages": "2290", "suppressedMessages": "2291", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2292", "messages": "2293", "suppressedMessages": "2294", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2295", "messages": "2296", "suppressedMessages": "2297", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2298", "messages": "2299", "suppressedMessages": "2300", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2301", "messages": "2302", "suppressedMessages": "2303", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2304", "messages": "2305", "suppressedMessages": "2306", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2307", "messages": "2308", "suppressedMessages": "2309", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2310", "messages": "2311", "suppressedMessages": "2312", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2313", "messages": "2314", "suppressedMessages": "2315", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2316", "messages": "2317", "suppressedMessages": "2318", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2319", "messages": "2320", "suppressedMessages": "2321", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2322", "messages": "2323", "suppressedMessages": "2324", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2325", "messages": "2326", "suppressedMessages": "2327", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2328", "messages": "2329", "suppressedMessages": "2330", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2331", "messages": "2332", "suppressedMessages": "2333", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2334", "messages": "2335", "suppressedMessages": "2336", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2337", "messages": "2338", "suppressedMessages": "2339", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2340", "messages": "2341", "suppressedMessages": "2342", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2343", "messages": "2344", "suppressedMessages": "2345", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2346", "messages": "2347", "suppressedMessages": "2348", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2349", "messages": "2350", "suppressedMessages": "2351", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2352", "messages": "2353", "suppressedMessages": "2354", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2355", "messages": "2356", "suppressedMessages": "2357", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2358", "messages": "2359", "suppressedMessages": "2360", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2361", "messages": "2362", "suppressedMessages": "2363", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2364", "messages": "2365", "suppressedMessages": "2366", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2367", "messages": "2368", "suppressedMessages": "2369", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2370", "messages": "2371", "suppressedMessages": "2372", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2373", "messages": "2374", "suppressedMessages": "2375", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2376", "messages": "2377", "suppressedMessages": "2378", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2379", "messages": "2380", "suppressedMessages": "2381", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2382", "messages": "2383", "suppressedMessages": "2384", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2385", "messages": "2386", "suppressedMessages": "2387", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2388", "messages": "2389", "suppressedMessages": "2390", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2391", "messages": "2392", "suppressedMessages": "2393", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2394", "messages": "2395", "suppressedMessages": "2396", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2397", "messages": "2398", "suppressedMessages": "2399", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2400", "messages": "2401", "suppressedMessages": "2402", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2403", "messages": "2404", "suppressedMessages": "2405", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2406", "messages": "2407", "suppressedMessages": "2408", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2409", "messages": "2410", "suppressedMessages": "2411", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2412", "messages": "2413", "suppressedMessages": "2414", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2415", "messages": "2416", "suppressedMessages": "2417", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2418", "messages": "2419", "suppressedMessages": "2420", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2421", "messages": "2422", "suppressedMessages": "2423", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2424", "messages": "2425", "suppressedMessages": "2426", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2427", "messages": "2428", "suppressedMessages": "2429", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2430", "messages": "2431", "suppressedMessages": "2432", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2433", "messages": "2434", "suppressedMessages": "2435", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2436", "messages": "2437", "suppressedMessages": "2438", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2439", "messages": "2440", "suppressedMessages": "2441", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2442", "messages": "2443", "suppressedMessages": "2444", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2445", "messages": "2446", "suppressedMessages": "2447", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2448", "messages": "2449", "suppressedMessages": "2450", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2451", "messages": "2452", "suppressedMessages": "2453", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2454", "messages": "2455", "suppressedMessages": "2456", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2457", "messages": "2458", "suppressedMessages": "2459", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2460", "messages": "2461", "suppressedMessages": "2462", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2463", "messages": "2464", "suppressedMessages": "2465", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2466", "messages": "2467", "suppressedMessages": "2468", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2469", "messages": "2470", "suppressedMessages": "2471", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2472", "messages": "2473", "suppressedMessages": "2474", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2475", "messages": "2476", "suppressedMessages": "2477", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2478", "messages": "2479", "suppressedMessages": "2480", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2481", "messages": "2482", "suppressedMessages": "2483", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2484", "messages": "2485", "suppressedMessages": "2486", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2487", "messages": "2488", "suppressedMessages": "2489", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2490", "messages": "2491", "suppressedMessages": "2492", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2493", "messages": "2494", "suppressedMessages": "2495", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2496", "messages": "2497", "suppressedMessages": "2498", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2499", "messages": "2500", "suppressedMessages": "2501", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2502", "messages": "2503", "suppressedMessages": "2504", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2505", "messages": "2506", "suppressedMessages": "2507", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2508", "messages": "2509", "suppressedMessages": "2510", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2511", "messages": "2512", "suppressedMessages": "2513", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2514", "messages": "2515", "suppressedMessages": "2516", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2517", "messages": "2518", "suppressedMessages": "2519", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2520", "messages": "2521", "suppressedMessages": "2522", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2523", "messages": "2524", "suppressedMessages": "2525", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2526", "messages": "2527", "suppressedMessages": "2528", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2529", "messages": "2530", "suppressedMessages": "2531", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2532", "messages": "2533", "suppressedMessages": "2534", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2535", "messages": "2536", "suppressedMessages": "2537", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2538", "messages": "2539", "suppressedMessages": "2540", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2541", "messages": "2542", "suppressedMessages": "2543", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2544", "messages": "2545", "suppressedMessages": "2546", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2547", "messages": "2548", "suppressedMessages": "2549", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2550", "messages": "2551", "suppressedMessages": "2552", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2553", "messages": "2554", "suppressedMessages": "2555", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2556", "messages": "2557", "suppressedMessages": "2558", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2559", "messages": "2560", "suppressedMessages": "2561", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2562", "messages": "2563", "suppressedMessages": "2564", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2565", "messages": "2566", "suppressedMessages": "2567", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2568", "messages": "2569", "suppressedMessages": "2570", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2571", "messages": "2572", "suppressedMessages": "2573", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2574", "messages": "2575", "suppressedMessages": "2576", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2577", "messages": "2578", "suppressedMessages": "2579", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2580", "messages": "2581", "suppressedMessages": "2582", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2583", "messages": "2584", "suppressedMessages": "2585", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2586", "messages": "2587", "suppressedMessages": "2588", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2589", "messages": "2590", "suppressedMessages": "2591", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2592", "messages": "2593", "suppressedMessages": "2594", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2595", "messages": "2596", "suppressedMessages": "2597", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2598", "messages": "2599", "suppressedMessages": "2600", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2601", "messages": "2602", "suppressedMessages": "2603", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2604", "messages": "2605", "suppressedMessages": "2606", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2607", "messages": "2608", "suppressedMessages": "2609", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2610", "messages": "2611", "suppressedMessages": "2612", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2613", "messages": "2614", "suppressedMessages": "2615", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2616", "messages": "2617", "suppressedMessages": "2618", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2619", "messages": "2620", "suppressedMessages": "2621", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2622", "messages": "2623", "suppressedMessages": "2624", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2625", "messages": "2626", "suppressedMessages": "2627", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2628", "messages": "2629", "suppressedMessages": "2630", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2631", "messages": "2632", "suppressedMessages": "2633", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2634", "messages": "2635", "suppressedMessages": "2636", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2637", "messages": "2638", "suppressedMessages": "2639", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2640", "messages": "2641", "suppressedMessages": "2642", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2643", "messages": "2644", "suppressedMessages": "2645", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2646", "messages": "2647", "suppressedMessages": "2648", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2649", "messages": "2650", "suppressedMessages": "2651", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2652", "messages": "2653", "suppressedMessages": "2654", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2655", "messages": "2656", "suppressedMessages": "2657", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2658", "messages": "2659", "suppressedMessages": "2660", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2661", "messages": "2662", "suppressedMessages": "2663", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2664", "messages": "2665", "suppressedMessages": "2666", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2667", "messages": "2668", "suppressedMessages": "2669", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2670", "messages": "2671", "suppressedMessages": "2672", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2673", "messages": "2674", "suppressedMessages": "2675", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2676", "messages": "2677", "suppressedMessages": "2678", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2679", "messages": "2680", "suppressedMessages": "2681", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2682", "messages": "2683", "suppressedMessages": "2684", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2685", "messages": "2686", "suppressedMessages": "2687", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2688", "messages": "2689", "suppressedMessages": "2690", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2691", "messages": "2692", "suppressedMessages": "2693", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2694", "messages": "2695", "suppressedMessages": "2696", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2697", "messages": "2698", "suppressedMessages": "2699", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2700", "messages": "2701", "suppressedMessages": "2702", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2703", "messages": "2704", "suppressedMessages": "2705", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2706", "messages": "2707", "suppressedMessages": "2708", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2709", "messages": "2710", "suppressedMessages": "2711", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "2712", "messages": "2713", "suppressedMessages": "2714", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2715", "messages": "2716", "suppressedMessages": "2717", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2718", "messages": "2719", "suppressedMessages": "2720", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2721", "messages": "2722", "suppressedMessages": "2723", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2724", "messages": "2725", "suppressedMessages": "2726", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2727", "messages": "2728", "suppressedMessages": "2729", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2730", "messages": "2731", "suppressedMessages": "2732", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2733", "messages": "2734", "suppressedMessages": "2735", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2736", "messages": "2737", "suppressedMessages": "2738", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2739", "messages": "2740", "suppressedMessages": "2741", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2742", "messages": "2743", "suppressedMessages": "2744", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2745", "messages": "2746", "suppressedMessages": "2747", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2748", "messages": "2749", "suppressedMessages": "2750", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2751", "messages": "2752", "suppressedMessages": "2753", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2754", "messages": "2755", "suppressedMessages": "2756", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2757", "messages": "2758", "suppressedMessages": "2759", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2760", "messages": "2761", "suppressedMessages": "2762", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2763", "messages": "2764", "suppressedMessages": "2765", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2766", "messages": "2767", "suppressedMessages": "2768", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2769", "messages": "2770", "suppressedMessages": "2771", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2772", "messages": "2773", "suppressedMessages": "2774", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2775", "messages": "2776", "suppressedMessages": "2777", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2778", "messages": "2779", "suppressedMessages": "2780", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2781", "messages": "2782", "suppressedMessages": "2783", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2784", "messages": "2785", "suppressedMessages": "2786", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2787", "messages": "2788", "suppressedMessages": "2789", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2790", "messages": "2791", "suppressedMessages": "2792", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2793", "messages": "2794", "suppressedMessages": "2795", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2796", "messages": "2797", "suppressedMessages": "2798", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2799", "messages": "2800", "suppressedMessages": "2801", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2802", "messages": "2803", "suppressedMessages": "2804", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2805", "messages": "2806", "suppressedMessages": "2807", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2808", "messages": "2809", "suppressedMessages": "2810", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2811", "messages": "2812", "suppressedMessages": "2813", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2814", "messages": "2815", "suppressedMessages": "2816", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2817", "messages": "2818", "suppressedMessages": "2819", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2820", "messages": "2821", "suppressedMessages": "2822", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2823", "messages": "2824", "suppressedMessages": "2825", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2826", "messages": "2827", "suppressedMessages": "2828", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2829", "messages": "2830", "suppressedMessages": "2831", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2832", "messages": "2833", "suppressedMessages": "2834", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2835", "messages": "2836", "suppressedMessages": "2837", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2838", "messages": "2839", "suppressedMessages": "2840", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2841", "messages": "2842", "suppressedMessages": "2843", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2844", "messages": "2845", "suppressedMessages": "2846", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2847", "messages": "2848", "suppressedMessages": "2849", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2850", "messages": "2851", "suppressedMessages": "2852", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2853", "messages": "2854", "suppressedMessages": "2855", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2856", "messages": "2857", "suppressedMessages": "2858", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2859", "messages": "2860", "suppressedMessages": "2861", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2862", "messages": "2863", "suppressedMessages": "2864", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2865", "messages": "2866", "suppressedMessages": "2867", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2868", "messages": "2869", "suppressedMessages": "2870", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2871", "messages": "2872", "suppressedMessages": "2873", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2874", "messages": "2875", "suppressedMessages": "2876", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2877", "messages": "2878", "suppressedMessages": "2879", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2880", "messages": "2881", "suppressedMessages": "2882", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2883", "messages": "2884", "suppressedMessages": "2885", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2886", "messages": "2887", "suppressedMessages": "2888", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2889", "messages": "2890", "suppressedMessages": "2891", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2892", "messages": "2893", "suppressedMessages": "2894", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2895", "messages": "2896", "suppressedMessages": "2897", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2898", "messages": "2899", "suppressedMessages": "2900", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2901", "messages": "2902", "suppressedMessages": "2903", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2904", "messages": "2905", "suppressedMessages": "2906", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2907", "messages": "2908", "suppressedMessages": "2909", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2910", "messages": "2911", "suppressedMessages": "2912", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2913", "messages": "2914", "suppressedMessages": "2915", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2916", "messages": "2917", "suppressedMessages": "2918", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2919", "messages": "2920", "suppressedMessages": "2921", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2922", "messages": "2923", "suppressedMessages": "2924", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2925", "messages": "2926", "suppressedMessages": "2927", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2928", "messages": "2929", "suppressedMessages": "2930", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2931", "messages": "2932", "suppressedMessages": "2933", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2934", "messages": "2935", "suppressedMessages": "2936", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2937", "messages": "2938", "suppressedMessages": "2939", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2940", "messages": "2941", "suppressedMessages": "2942", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2943", "messages": "2944", "suppressedMessages": "2945", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2946", "messages": "2947", "suppressedMessages": "2948", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2949", "messages": "2950", "suppressedMessages": "2951", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2952", "messages": "2953", "suppressedMessages": "2954", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2955", "messages": "2956", "suppressedMessages": "2957", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2958", "messages": "2959", "suppressedMessages": "2960", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2961", "messages": "2962", "suppressedMessages": "2963", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2964", "messages": "2965", "suppressedMessages": "2966", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2967", "messages": "2968", "suppressedMessages": "2969", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2970", "messages": "2971", "suppressedMessages": "2972", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2973", "messages": "2974", "suppressedMessages": "2975", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2976", "messages": "2977", "suppressedMessages": "2978", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2979", "messages": "2980", "suppressedMessages": "2981", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2982", "messages": "2983", "suppressedMessages": "2984", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2985", "messages": "2986", "suppressedMessages": "2987", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2988", "messages": "2989", "suppressedMessages": "2990", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2991", "messages": "2992", "suppressedMessages": "2993", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2994", "messages": "2995", "suppressedMessages": "2996", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2997", "messages": "2998", "suppressedMessages": "2999", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3000", "messages": "3001", "suppressedMessages": "3002", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3003", "messages": "3004", "suppressedMessages": "3005", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3006", "messages": "3007", "suppressedMessages": "3008", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3009", "messages": "3010", "suppressedMessages": "3011", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3012", "messages": "3013", "suppressedMessages": "3014", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3015", "messages": "3016", "suppressedMessages": "3017", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3018", "messages": "3019", "suppressedMessages": "3020", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3021", "messages": "3022", "suppressedMessages": "3023", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3024", "messages": "3025", "suppressedMessages": "3026", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3027", "messages": "3028", "suppressedMessages": "3029", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3030", "messages": "3031", "suppressedMessages": "3032", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3033", "messages": "3034", "suppressedMessages": "3035", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3036", "messages": "3037", "suppressedMessages": "3038", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3039", "messages": "3040", "suppressedMessages": "3041", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3042", "messages": "3043", "suppressedMessages": "3044", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3045", "messages": "3046", "suppressedMessages": "3047", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3048", "messages": "3049", "suppressedMessages": "3050", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3051", "messages": "3052", "suppressedMessages": "3053", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3054", "messages": "3055", "suppressedMessages": "3056", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3057", "messages": "3058", "suppressedMessages": "3059", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3060", "messages": "3061", "suppressedMessages": "3062", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3063", "messages": "3064", "suppressedMessages": "3065", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3066", "messages": "3067", "suppressedMessages": "3068", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3069", "messages": "3070", "suppressedMessages": "3071", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3072", "messages": "3073", "suppressedMessages": "3074", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3075", "messages": "3076", "suppressedMessages": "3077", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3078", "messages": "3079", "suppressedMessages": "3080", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3081", "messages": "3082", "suppressedMessages": "3083", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3084", "messages": "3085", "suppressedMessages": "3086", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3087", "messages": "3088", "suppressedMessages": "3089", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3090", "messages": "3091", "suppressedMessages": "3092", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3093", "messages": "3094", "suppressedMessages": "3095", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3096", "messages": "3097", "suppressedMessages": "3098", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3099", "messages": "3100", "suppressedMessages": "3101", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3102", "messages": "3103", "suppressedMessages": "3104", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3105", "messages": "3106", "suppressedMessages": "3107", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3108", "messages": "3109", "suppressedMessages": "3110", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3111", "messages": "3112", "suppressedMessages": "3113", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3114", "messages": "3115", "suppressedMessages": "3116", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3117", "messages": "3118", "suppressedMessages": "3119", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3120", "messages": "3121", "suppressedMessages": "3122", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3123", "messages": "3124", "suppressedMessages": "3125", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3126", "messages": "3127", "suppressedMessages": "3128", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3129", "messages": "3130", "suppressedMessages": "3131", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3132", "messages": "3133", "suppressedMessages": "3134", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3135", "messages": "3136", "suppressedMessages": "3137", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3138", "messages": "3139", "suppressedMessages": "3140", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3141", "messages": "3142", "suppressedMessages": "3143", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3144", "messages": "3145", "suppressedMessages": "3146", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3147", "messages": "3148", "suppressedMessages": "3149", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3150", "messages": "3151", "suppressedMessages": "3152", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3153", "messages": "3154", "suppressedMessages": "3155", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3156", "messages": "3157", "suppressedMessages": "3158", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3159", "messages": "3160", "suppressedMessages": "3161", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3162", "messages": "3163", "suppressedMessages": "3164", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3165", "messages": "3166", "suppressedMessages": "3167", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3168", "messages": "3169", "suppressedMessages": "3170", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3171", "messages": "3172", "suppressedMessages": "3173", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3174", "messages": "3175", "suppressedMessages": "3176", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3177", "messages": "3178", "suppressedMessages": "3179", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3180", "messages": "3181", "suppressedMessages": "3182", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3183", "messages": "3184", "suppressedMessages": "3185", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3186", "messages": "3187", "suppressedMessages": "3188", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3189", "messages": "3190", "suppressedMessages": "3191", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3192", "messages": "3193", "suppressedMessages": "3194", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3195", "messages": "3196", "suppressedMessages": "3197", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3198", "messages": "3199", "suppressedMessages": "3200", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3201", "messages": "3202", "suppressedMessages": "3203", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3204", "messages": "3205", "suppressedMessages": "3206", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3207", "messages": "3208", "suppressedMessages": "3209", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3210", "messages": "3211", "suppressedMessages": "3212", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3213", "messages": "3214", "suppressedMessages": "3215", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3216", "messages": "3217", "suppressedMessages": "3218", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3219", "messages": "3220", "suppressedMessages": "3221", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3222", "messages": "3223", "suppressedMessages": "3224", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3225", "messages": "3226", "suppressedMessages": "3227", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3228", "messages": "3229", "suppressedMessages": "3230", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3231", "messages": "3232", "suppressedMessages": "3233", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3234", "messages": "3235", "suppressedMessages": "3236", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3237", "messages": "3238", "suppressedMessages": "3239", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3240", "messages": "3241", "suppressedMessages": "3242", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3243", "messages": "3244", "suppressedMessages": "3245", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3246", "messages": "3247", "suppressedMessages": "3248", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3249", "messages": "3250", "suppressedMessages": "3251", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3252", "messages": "3253", "suppressedMessages": "3254", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3255", "messages": "3256", "suppressedMessages": "3257", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3258", "messages": "3259", "suppressedMessages": "3260", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3261", "messages": "3262", "suppressedMessages": "3263", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3264", "messages": "3265", "suppressedMessages": "3266", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3267", "messages": "3268", "suppressedMessages": "3269", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3270", "messages": "3271", "suppressedMessages": "3272", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3273", "messages": "3274", "suppressedMessages": "3275", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3276", "messages": "3277", "suppressedMessages": "3278", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3279", "messages": "3280", "suppressedMessages": "3281", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3282", "messages": "3283", "suppressedMessages": "3284", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3285", "messages": "3286", "suppressedMessages": "3287", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3288", "messages": "3289", "suppressedMessages": "3290", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3291", "messages": "3292", "suppressedMessages": "3293", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3294", "messages": "3295", "suppressedMessages": "3296", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3297", "messages": "3298", "suppressedMessages": "3299", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3300", "messages": "3301", "suppressedMessages": "3302", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3303", "messages": "3304", "suppressedMessages": "3305", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3306", "messages": "3307", "suppressedMessages": "3308", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3309", "messages": "3310", "suppressedMessages": "3311", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3312", "messages": "3313", "suppressedMessages": "3314", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3315", "messages": "3316", "suppressedMessages": "3317", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3318", "messages": "3319", "suppressedMessages": "3320", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3321", "messages": "3322", "suppressedMessages": "3323", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3324", "messages": "3325", "suppressedMessages": "3326", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3327", "messages": "3328", "suppressedMessages": "3329", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3330", "messages": "3331", "suppressedMessages": "3332", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3333", "messages": "3334", "suppressedMessages": "3335", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3336", "messages": "3337", "suppressedMessages": "3338", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3339", "messages": "3340", "suppressedMessages": "3341", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3342", "messages": "3343", "suppressedMessages": "3344", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3345", "messages": "3346", "suppressedMessages": "3347", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3348", "messages": "3349", "suppressedMessages": "3350", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3351", "messages": "3352", "suppressedMessages": "3353", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3354", "messages": "3355", "suppressedMessages": "3356", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3357", "messages": "3358", "suppressedMessages": "3359", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3360", "messages": "3361", "suppressedMessages": "3362", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3363", "messages": "3364", "suppressedMessages": "3365", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3366", "messages": "3367", "suppressedMessages": "3368", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3369", "messages": "3370", "suppressedMessages": "3371", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3372", "messages": "3373", "suppressedMessages": "3374", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3375", "messages": "3376", "suppressedMessages": "3377", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3378", "messages": "3379", "suppressedMessages": "3380", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3381", "messages": "3382", "suppressedMessages": "3383", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3384", "messages": "3385", "suppressedMessages": "3386", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3387", "messages": "3388", "suppressedMessages": "3389", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3390", "messages": "3391", "suppressedMessages": "3392", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "3393", "messages": "3394", "suppressedMessages": "3395", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3396", "messages": "3397", "suppressedMessages": "3398", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3399", "messages": "3400", "suppressedMessages": "3401", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3402", "messages": "3403", "suppressedMessages": "3404", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3405", "messages": "3406", "suppressedMessages": "3407", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3408", "messages": "3409", "suppressedMessages": "3410", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3411", "messages": "3412", "suppressedMessages": "3413", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3414", "messages": "3415", "suppressedMessages": "3416", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3417", "messages": "3418", "suppressedMessages": "3419", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3420", "messages": "3421", "suppressedMessages": "3422", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3423", "messages": "3424", "suppressedMessages": "3425", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3426", "messages": "3427", "suppressedMessages": "3428", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3429", "messages": "3430", "suppressedMessages": "3431", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3432", "messages": "3433", "suppressedMessages": "3434", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3435", "messages": "3436", "suppressedMessages": "3437", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3438", "messages": "3439", "suppressedMessages": "3440", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3441", "messages": "3442", "suppressedMessages": "3443", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3444", "messages": "3445", "suppressedMessages": "3446", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3447", "messages": "3448", "suppressedMessages": "3449", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "3450", "messages": "3451", "suppressedMessages": "3452", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3453", "messages": "3454", "suppressedMessages": "3455", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3456", "messages": "3457", "suppressedMessages": "3458", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3459", "messages": "3460", "suppressedMessages": "3461", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3462", "messages": "3463", "suppressedMessages": "3464", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3465", "messages": "3466", "suppressedMessages": "3467", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3468", "messages": "3469", "suppressedMessages": "3470", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3471", "messages": "3472", "suppressedMessages": "3473", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3474", "messages": "3475", "suppressedMessages": "3476", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3477", "messages": "3478", "suppressedMessages": "3479", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3480", "messages": "3481", "suppressedMessages": "3482", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3483", "messages": "3484", "suppressedMessages": "3485", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3486", "messages": "3487", "suppressedMessages": "3488", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3489", "messages": "3490", "suppressedMessages": "3491", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3492", "messages": "3493", "suppressedMessages": "3494", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3495", "messages": "3496", "suppressedMessages": "3497", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3498", "messages": "3499", "suppressedMessages": "3500", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3501", "messages": "3502", "suppressedMessages": "3503", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3504", "messages": "3505", "suppressedMessages": "3506", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3507", "messages": "3508", "suppressedMessages": "3509", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3510", "messages": "3511", "suppressedMessages": "3512", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3513", "messages": "3514", "suppressedMessages": "3515", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3516", "messages": "3517", "suppressedMessages": "3518", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3519", "messages": "3520", "suppressedMessages": "3521", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3522", "messages": "3523", "suppressedMessages": "3524", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3525", "messages": "3526", "suppressedMessages": "3527", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3528", "messages": "3529", "suppressedMessages": "3530", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3531", "messages": "3532", "suppressedMessages": "3533", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3534", "messages": "3535", "suppressedMessages": "3536", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3537", "messages": "3538", "suppressedMessages": "3539", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3540", "messages": "3541", "suppressedMessages": "3542", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3543", "messages": "3544", "suppressedMessages": "3545", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3546", "messages": "3547", "suppressedMessages": "3548", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3549", "messages": "3550", "suppressedMessages": "3551", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3552", "messages": "3553", "suppressedMessages": "3554", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3555", "messages": "3556", "suppressedMessages": "3557", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3558", "messages": "3559", "suppressedMessages": "3560", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3561", "messages": "3562", "suppressedMessages": "3563", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3564", "messages": "3565", "suppressedMessages": "3566", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3567", "messages": "3568", "suppressedMessages": "3569", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3570", "messages": "3571", "suppressedMessages": "3572", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3573", "messages": "3574", "suppressedMessages": "3575", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3576", "messages": "3577", "suppressedMessages": "3578", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3579", "messages": "3580", "suppressedMessages": "3581", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3582", "messages": "3583", "suppressedMessages": "3584", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3585", "messages": "3586", "suppressedMessages": "3587", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "3588", "messages": "3589", "suppressedMessages": "3590", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3591", "messages": "3592", "suppressedMessages": "3593", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "3594", "messages": "3595", "suppressedMessages": "3596", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3597", "messages": "3598", "suppressedMessages": "3599", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3600", "messages": "3601", "suppressedMessages": "3602", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3603", "messages": "3604", "suppressedMessages": "3605", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "3606", "messages": "3607", "suppressedMessages": "3608", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3609", "messages": "3610", "suppressedMessages": "3611", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3612", "messages": "3613", "suppressedMessages": "3614", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3615", "messages": "3616", "suppressedMessages": "3617", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "3618", "messages": "3619", "suppressedMessages": "3620", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "3621", "messages": "3622", "suppressedMessages": "3623", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "3624", "messages": "3625", "suppressedMessages": "3626", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3627", "messages": "3628", "suppressedMessages": "3629", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3630", "messages": "3631", "suppressedMessages": "3632", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3633", "messages": "3634", "suppressedMessages": "3635", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3636", "messages": "3637", "suppressedMessages": "3638", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3639", "messages": "3640", "suppressedMessages": "3641", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "3642", "messages": "3643", "suppressedMessages": "3644", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "3645", "messages": "3646", "suppressedMessages": "3647", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "3648", "messages": "3649", "suppressedMessages": "3650", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "3651", "messages": "3652", "suppressedMessages": "3653", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3654", "messages": "3655", "suppressedMessages": "3656", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3657", "messages": "3658", "suppressedMessages": "3659", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3660", "messages": "3661", "suppressedMessages": "3662", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3663", "messages": "3664", "suppressedMessages": "3665", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "3666", "messages": "3667", "suppressedMessages": "3668", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "3669", "messages": "3670", "suppressedMessages": "3671", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "3672", "messages": "3673", "suppressedMessages": "3674", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "3675", "messages": "3676", "suppressedMessages": "3677", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "3678", "messages": "3679", "suppressedMessages": "3680", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3681", "messages": "3682", "suppressedMessages": "3683", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3684", "messages": "3685", "suppressedMessages": "3686", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3687", "messages": "3688", "suppressedMessages": "3689", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3690", "messages": "3691", "suppressedMessages": "3692", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3693", "messages": "3694", "suppressedMessages": "3695", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3696", "messages": "3697", "suppressedMessages": "3698", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3699", "messages": "3700", "suppressedMessages": "3701", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3702", "messages": "3703", "suppressedMessages": "3704", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 18, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "3705", "messages": "3706", "suppressedMessages": "3707", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3708", "messages": "3709", "suppressedMessages": "3710", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3711", "messages": "3712", "suppressedMessages": "3713", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3714", "messages": "3715", "suppressedMessages": "3716", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "3717", "messages": "3718", "suppressedMessages": "3719", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "3720", "messages": "3721", "suppressedMessages": "3722", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 1, "fixableWarningCount": 0, "source": null}, {"filePath": "3723", "messages": "3724", "suppressedMessages": "3725", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3726", "messages": "3727", "suppressedMessages": "3728", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "3729", "messages": "3730", "suppressedMessages": "3731", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "3732", "messages": "3733", "suppressedMessages": "3734", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3735", "messages": "3736", "suppressedMessages": "3737", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3738", "messages": "3739", "suppressedMessages": "3740", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3741", "messages": "3742", "suppressedMessages": "3743", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3744", "messages": "3745", "suppressedMessages": "3746", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "3747", "messages": "3748", "suppressedMessages": "3749", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3750", "messages": "3751", "suppressedMessages": "3752", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "3753", "messages": "3754", "suppressedMessages": "3755", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3756", "messages": "3757", "suppressedMessages": "3758", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "3759", "messages": "3760", "suppressedMessages": "3761", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 10, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "3762", "messages": "3763", "suppressedMessages": "3764", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "3765", "messages": "3766", "suppressedMessages": "3767", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3768", "messages": "3769", "suppressedMessages": "3770", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3771", "messages": "3772", "suppressedMessages": "3773", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "3774", "messages": "3775", "suppressedMessages": "3776", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3777", "messages": "3778", "suppressedMessages": "3779", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3780", "messages": "3781", "suppressedMessages": "3782", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3783", "messages": "3784", "suppressedMessages": "3785", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3786", "messages": "3787", "suppressedMessages": "3788", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3789", "messages": "3790", "suppressedMessages": "3791", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3792", "messages": "3793", "suppressedMessages": "3794", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3795", "messages": "3796", "suppressedMessages": "3797", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "3798", "messages": "3799", "suppressedMessages": "3800", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "3801", "messages": "3802", "suppressedMessages": "3803", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "3804", "messages": "3805", "suppressedMessages": "3806", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "C:\\web-app\\dukancard\\app\\(auth)\\choose-role\\actions.ts", [], [], "C:\\web-app\\dukancard\\app\\(auth)\\choose-role\\ChooseRoleClient.tsx", [], [], "C:\\web-app\\dukancard\\app\\(auth)\\choose-role\\page.tsx", [], [], "C:\\web-app\\dukancard\\app\\(auth)\\layout.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\actions\\themeHeaderActions.ts", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\actions.ts", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\business-card\\getBusinessCardData.ts", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\business-card\\updateBusinessCard.ts", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\CardEditorClient.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\components\\BusinessCardPreview.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\components\\CardBackgroundEffects.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\components\\CardBusinessInfo.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\components\\CardCornerDecorations.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\components\\CardDivider.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\components\\CardEditForm\\AppearanceSection.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\components\\CardEditForm\\BasicInfoSection.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\components\\CardEditForm\\BusinessDetailsSection.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\components\\CardEditForm\\BusinessHoursEditor.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\components\\CardEditForm\\CardEditFormContent.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\components\\CardEditForm\\ContactLocationSection.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\components\\CardEditForm\\formStyles.ts", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\components\\CardEditForm\\FormSubmitButton.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\components\\CardEditForm\\LinksSection.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\components\\CardEditForm\\StatusSlugSection.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\components\\CardGlowEffects.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\components\\CardHeader.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\components\\CardPreviewSection\\DownloadButton.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\components\\CardPreviewSection\\index.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\components\\CardPreviewSection\\ShareButtons.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\components\\CardProfile.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\components\\CardTextures.ts", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\components\\CustomAdCropDialog.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\components\\EnhancedInteractionButtons.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\components\\hooks\\useLogoUpload.ts", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\components\\hooks\\usePincodeDetails.ts", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\components\\hooks\\useSlugCheck.ts", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\components\\ImageCropDialog.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\components\\LogoDeleteDialog.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\components\\UnsavedChangesReminder.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\components\\utils\\cardUtils.ts", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\data\\businessCardMapper.ts", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\data\\subscriptionChecker.ts", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\logo\\logoActions.ts", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\page.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\public\\publicCardActions.ts", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\schema.ts", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\slug\\slugUtils.ts", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\utils\\businessHoursProcessor.ts", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\utils\\constants.ts", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\utils\\scrollToError.ts", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\utils\\slugGenerator.ts", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\validation\\businessCardValidation.ts", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\components\\AnimatedMetricCard.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\components\\BusinessDashboardClient.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\components\\BusinessDashboardClientLayout.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\components\\BusinessStatusAlert.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\components\\DashboardOverviewClient.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\components\\EnhancedQuickActions.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\components\\FlipTimer.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\gallery\\actions.ts", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\gallery\\components\\DeleteDialog.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\gallery\\components\\EmptyGallery.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\gallery\\components\\GalleryGrid.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\gallery\\components\\GalleryHeader.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\gallery\\components\\Lightbox.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\gallery\\components\\ReorderControls.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\gallery\\components\\SortableImageItem.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\gallery\\components\\StaticImageItem.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\gallery\\components\\UploadBox.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\gallery\\components\\UploadDialog.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\gallery\\GalleryPageClient.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\gallery\\hooks\\useDragAndDrop.ts", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\gallery\\hooks\\useGalleryState.ts", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\gallery\\hooks\\useReordering.ts", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\gallery\\page.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\gallery\\types\\galleryTypes.ts", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\gallery\\types.ts", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\gallery\\utils\\fileValidation.ts", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\gallery\\utils.ts", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\layout.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\likes\\actions.ts", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\likes\\components\\BusinessLikesPageClient.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\likes\\components\\BusinessLikesReceivedList.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\likes\\components\\BusinessMyLikesList.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\likes\\LikeCardClient.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\likes\\LikeListClient.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\likes\\page.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\overview\\page.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\page.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\actions\\addProduct.ts", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\actions\\addVariant.ts", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\actions\\bulkVariantOperations.ts", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\actions\\deleteProduct.ts", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\actions\\deleteVariant.ts", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\actions\\getProducts.ts", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\actions\\getProductWithVariants.ts", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\actions\\image-library.ts", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\actions\\imageHandlers.ts", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\actions\\index.ts", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\actions\\schemas.ts", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\actions\\types.ts", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\actions\\updateProduct.ts", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\actions\\updateVariant.ts", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\actions.ts", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\add\\AddProductClient.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\add\\page.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\components\\BulkVariantOperations.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\components\\hooks\\useProductImageUpload.ts", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\components\\hooks\\useProductMultiImageUpload.ts", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\components\\ImageLibraryDialog.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\components\\product-ui\\index.ts", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\components\\product-ui\\ProductDeleteDialog.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\components\\product-ui\\ProductEmptyState.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\components\\product-ui\\ProductFilters.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\components\\product-ui\\ProductGrid.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\components\\product-ui\\ProductHeader.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\components\\product-ui\\ProductItemsPerPageSelector.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\components\\product-ui\\ProductLoadingState.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\components\\product-ui\\ProductPagination.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\components\\product-ui\\ProductStats.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\components\\product-ui\\ProductTable.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\components\\product-ui\\ProductViewToggle.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\components\\ProductImageCropDialog.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\components\\ProductMultiImageUpload.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\components\\ProductsPageClient.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\components\\StandaloneProductForm.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\components\\VariantCombinationGenerator.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\components\\VariantForm.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\components\\VariantTable.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\components\\VariantTypeSelector.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\context\\ProductsContext.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\edit\\[productId]\\EditProductClient.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\edit\\[productId]\\page.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\page.tsx", ["3807"], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\ProductsClient.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\types.ts", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\reviews\\actions.ts", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\reviews\\components\\BusinessMyReviewListClient.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\reviews\\components\\BusinessReviewListClient.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\reviews\\components\\BusinessReviewsPageClient.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\reviews\\page.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\settings\\actions.ts", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\settings\\components\\AccountDeletionSection.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\settings\\components\\CardEditorLinkSection.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\settings\\components\\EmailUpdateDialog.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\settings\\components\\LinkEmailSection.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\settings\\components\\LinkPhoneSection.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\settings\\components\\SettingsPageClient.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\settings\\page.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\settings\\schema.ts", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\subscriptions\\actions.ts", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\subscriptions\\components\\BusinessSubscriptionsPageClient.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\subscriptions\\page.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\components\\CustomerAnimatedMetricCard.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\components\\CustomerDashboardClient.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\components\\CustomerMetricsOverview.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\CustomerDashboardClientLayout.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\layout.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\likes\\actions.ts", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\likes\\components\\LikesPageClient.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\likes\\LikeListClient.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\likes\\page.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\overview\\page.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\page.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\profile\\actions.ts", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\profile\\avatar-actions.ts", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\profile\\components\\AddressForm.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\profile\\components\\AvatarDeleteDialog.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\profile\\components\\AvatarUpload.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\profile\\components\\EmailForm.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\profile\\components\\hooks\\usePincodeDetails.ts", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\profile\\components\\MobileForm.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\profile\\components\\PhoneForm.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\profile\\components\\ProfilePageClient.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\profile\\components\\ProfileRequirementDialog.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\profile\\hooks\\useAvatarUpload.ts", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\profile\\page.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\profile\\ProfileForm.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\reviews\\actions.ts", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\reviews\\components\\EnhancedReviewListClient.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\reviews\\components\\ReviewsBackground.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\reviews\\components\\ReviewsPageClient.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\reviews\\page.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\reviews\\ReviewListClient.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\settings\\actions.ts", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\settings\\components\\EmailUpdateDialog.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\settings\\components\\LinkEmailSection.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\settings\\components\\LinkPhoneSection.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\settings\\components\\SettingsPageClient.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\settings\\CustomerSettingsForm.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\settings\\DeleteAccountSection.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\settings\\page.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\settings\\schema.ts", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\settings\\UpdateEmailForm.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\subscriptions\\actions.ts", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\subscriptions\\components\\SubscriptionCardSkeleton.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\subscriptions\\components\\SubscriptionPagination.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\subscriptions\\components\\SubscriptionsBackground.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\subscriptions\\components\\SubscriptionSearch.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\subscriptions\\components\\SubscriptionsPageClient.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\subscriptions\\page.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\subscriptions\\SubscriptionCardClient.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\subscriptions\\SubscriptionListClient.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\about\\AboutUsClient.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\about\\components\\AboutCTASection.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\about\\components\\AboutHeroSection.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\about\\components\\animations\\AboutAnimatedBackground.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\about\\components\\CoreValuesSection.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\about\\components\\MilestonesSection.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\about\\components\\MissionVisionSection.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\about\\components\\StorySection.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\about\\page.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\actions\\getHomepageBusinessCard.ts", [], [], "C:\\web-app\\dukancard\\app\\(main)\\advertise\\AdvertisePageClient.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\advertise\\components\\BenefitsSection.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\advertise\\components\\ContactSection.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\advertise\\components\\FAQSection.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\advertise\\components\\HeroSection.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\advertise\\components\\HubSpotForm.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\advertise\\page.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\auth\\callback\\AuthCallbackClient.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\auth\\callback\\AuthCallbackWrapper.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\auth\\callback\\metadata.ts", [], [], "C:\\web-app\\dukancard\\app\\(main)\\auth\\callback\\page.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\blog\\components\\BlogListingClient.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\blog\\page.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\blog\\sitemap.ts", [], [], "C:\\web-app\\dukancard\\app\\(main)\\blog\\[blogSlug]\\components\\BlogPostClient.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\blog\\[blogSlug]\\page.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\components\\AnimatedBackground.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\components\\auth\\AnimatedAuthCard.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\components\\auth\\AnimatedButton.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\components\\auth\\AnimatedIcon.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\components\\auth\\AuthPageBackground.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\components\\CardShowcase.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\components\\DeviceFrame.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\components\\FeatureElements.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\components\\FuturisticScanner.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\components\\landing\\ActionButtonsSection.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\components\\landing\\animations.ts", [], [], "C:\\web-app\\dukancard\\app\\(main)\\components\\landing\\CardBackground.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\components\\landing\\CTASection.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\components\\landing\\DigitalCardFeature.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\components\\landing\\EnhancedCardShowcase.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\components\\landing\\EnhancedFeatureElements.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\components\\landing\\EnhancedMetricsContainer.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\components\\landing\\EnhancedMetricsDisplay.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\components\\landing\\ErrorDialog.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\components\\landing\\exampleCardData.ts", [], [], "C:\\web-app\\dukancard\\app\\(main)\\components\\landing\\FeatureCard.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\components\\landing\\FeaturesSection.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\components\\landing\\FloatingParticlesBackground.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\components\\landing\\FloatingPricingElements.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\components\\landing\\HeroActionButtons.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\components\\landing\\HeroSearchSection.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\components\\landing\\HomeCategoriesSection.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\components\\landing\\MobileFeatureCarousel.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\components\\landing\\ModernSearchSection.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\components\\landing\\NewArrivalsSection.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\components\\landing\\PopularBusinessesSection.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\components\\landing\\SectionDivider.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\components\\landing\\StickyHeroSectionClient.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\components\\landing\\TestimonialCard.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\components\\landing\\TestimonialCarousel.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\components\\landing\\TestimonialsSection.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\components\\metrics\\index.ts", [], [], "C:\\web-app\\dukancard\\app\\(main)\\components\\metrics\\MetricsContainer.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\components\\metrics\\MetricsParticles.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\components\\MetricsDisplay.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\components\\policy\\PolicyBackground.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\components\\policy\\PolicyCTASection.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\components\\policy\\PolicyHeroSection.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\components\\policy\\PolicyNavigation.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\components\\policy\\PolicySection.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\components\\SectionBackground.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\components\\shared\\PopularCategoriesSection.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\contact\\components\\animations\\ContactAnimatedBackground.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\contact\\components\\ContactCTASection.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\contact\\components\\ContactHeroSection.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\contact\\components\\ContactInfoSection.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\contact\\components\\ContactMapSection.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\contact\\ModernContactClient.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\contact\\page.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\cookies\\ModernCookiePolicyClient.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\cookies\\page.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\discover\\actions\\businessActions.ts", [], [], "C:\\web-app\\dukancard\\app\\(main)\\discover\\actions\\combinedActions.ts", [], [], "C:\\web-app\\dukancard\\app\\(main)\\discover\\actions\\locationActions.ts", [], [], "C:\\web-app\\dukancard\\app\\(main)\\discover\\actions\\productActions.ts", [], [], "C:\\web-app\\dukancard\\app\\(main)\\discover\\actions\\types.ts", [], [], "C:\\web-app\\dukancard\\app\\(main)\\discover\\actions.ts", [], [], "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\AnimatedBadge.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\AnimatedBusinessCard.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\AnimatedBusinessGrid.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\AnimatedBusinessGridSkeleton.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\AnimatedButton.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\AnimatedDivider.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\BusinessCardTable.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\BusinessCardTableSkeleton.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\BusinessResultsGrid.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\BusinessSortControls.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\CategoryCarousel.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\CategoryFilter.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\CitySearchSkeleton.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\ErrorSection.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\FloatingCard.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\ImprovedSearchSection.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\LoadingSpinner.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\LocationIndicator.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\ModernBusinessFilterGrid.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\ModernBusinessResults.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\ModernResultsSection.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\ModernSearchSection.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\NoResultsMessage.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\ProductGrid.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\ProductGridSkeleton.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\ProductResults.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\ProductResultsGrid.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\ProductSortControls.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\ResultsSkeleton.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\RevealTitle.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\SearchResultsHeader.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\SectionTitle.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\SortingControls.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\ViewToggle.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\discover\\constants\\paginationConstants.ts", [], [], "C:\\web-app\\dukancard\\app\\(main)\\discover\\constants\\urlParamConstants.ts", [], [], "C:\\web-app\\dukancard\\app\\(main)\\discover\\context\\businessContextFunctions.ts", [], [], "C:\\web-app\\dukancard\\app\\(main)\\discover\\context\\commonContextFunctions.ts", [], [], "C:\\web-app\\dukancard\\app\\(main)\\discover\\context\\DiscoverContext.tsx", [], ["3808"], "C:\\web-app\\dukancard\\app\\(main)\\discover\\context\\productContextFunctions.ts", [], [], "C:\\web-app\\dukancard\\app\\(main)\\discover\\context\\types.ts", [], [], "C:\\web-app\\dukancard\\app\\(main)\\discover\\ModernDiscoverClient.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\discover\\ModernResultsSkeleton.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\discover\\page.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\discover\\utils\\sortMappings.ts", [], [], "C:\\web-app\\dukancard\\app\\(main)\\features\\components\\BusinessUseCasesSection.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\features\\components\\FeatureAnimation.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\features\\components\\FeatureBackground.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\features\\components\\FeatureCard.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\features\\components\\FeaturesCTASection.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\features\\components\\HeroFeatureSection.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\features\\ModernFeaturesClient.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\features\\page.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\hooks\\useIntersectionAnimation.ts", [], [], "C:\\web-app\\dukancard\\app\\(main)\\LandingPageClient.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\layout.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\login\\actions.ts", [], [], "C:\\web-app\\dukancard\\app\\(main)\\login\\components\\AuthMethodToggle.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\login\\components\\EmailOTPForm.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\login\\components\\MobilePasswordForm.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\login\\components\\SocialLoginButton.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\login\\LoginForm.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\login\\page.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\page.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\privacy\\ModernPrivacyPolicyClient.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\privacy\\page.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\refund\\ModernRefundPolicyClient.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\refund\\page.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\support\\account-billing\\AccountBillingClient.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\support\\account-billing\\page.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\support\\business-card-setup\\BusinessCardSetupClient.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\support\\business-card-setup\\page.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\support\\components\\AnimatedTitle.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\support\\components\\EnhancedFAQSection.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\support\\components\\EnhancedSupportFAQ.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\support\\components\\EnhancedSupportSubPage.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\support\\page.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\support\\product-management\\page.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\support\\product-management\\ProductManagementClient.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\support\\settings\\page.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\support\\settings\\SettingsClient.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\support\\SupportPageClient.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\support\\technical-issues\\page.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\support\\technical-issues\\TechnicalIssuesClient.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\terms\\ModernTermsOfServiceClient.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\terms\\page.tsx", [], [], "C:\\web-app\\dukancard\\app\\(onboarding)\\layout.tsx", [], [], "C:\\web-app\\dukancard\\app\\(onboarding)\\onboarding\\actions.ts", [], [], "C:\\web-app\\dukancard\\app\\(onboarding)\\onboarding\\components\\LoadingOverlay.tsx", [], [], "C:\\web-app\\dukancard\\app\\(onboarding)\\onboarding\\components\\NavigationButtons.tsx", [], [], "C:\\web-app\\dukancard\\app\\(onboarding)\\onboarding\\components\\StepProgress.tsx", [], [], "C:\\web-app\\dukancard\\app\\(onboarding)\\onboarding\\components\\steps\\AddressStep.tsx", [], [], "C:\\web-app\\dukancard\\app\\(onboarding)\\onboarding\\components\\steps\\BusinessDetailsStep.tsx", [], [], "C:\\web-app\\dukancard\\app\\(onboarding)\\onboarding\\components\\steps\\CardInformationStep.tsx", [], [], "C:\\web-app\\dukancard\\app\\(onboarding)\\onboarding\\constants\\onboardingSteps.ts", [], [], "C:\\web-app\\dukancard\\app\\(onboarding)\\onboarding\\hooks\\useExistingData.ts", [], [], "C:\\web-app\\dukancard\\app\\(onboarding)\\onboarding\\hooks\\useOnboardingForm.ts", [], [], "C:\\web-app\\dukancard\\app\\(onboarding)\\onboarding\\hooks\\usePincodeDetails.ts", [], [], "C:\\web-app\\dukancard\\app\\(onboarding)\\onboarding\\hooks\\useSlugAvailability.ts", [], [], "C:\\web-app\\dukancard\\app\\(onboarding)\\onboarding\\hooks\\useUserData.ts", [], [], "C:\\web-app\\dukancard\\app\\(onboarding)\\onboarding\\layout.tsx", [], [], "C:\\web-app\\dukancard\\app\\(onboarding)\\onboarding\\OnboardingClient.tsx", [], [], "C:\\web-app\\dukancard\\app\\(onboarding)\\onboarding\\page.tsx", [], [], "C:\\web-app\\dukancard\\app\\(onboarding)\\onboarding\\types\\onboarding.ts", [], [], "C:\\web-app\\dukancard\\app\\api\\check-user-type\\route.ts", ["3809", "3810", "3811", "3812", "3813", "3814"], [], "C:\\web-app\\dukancard\\app\\auth\\actions.ts", [], [], "C:\\web-app\\dukancard\\app\\cards\\sitemap.ts", [], [], "C:\\web-app\\dukancard\\app\\components\\AdSlot.tsx", [], [], "C:\\web-app\\dukancard\\app\\components\\AdvertiseButton.tsx", [], [], "C:\\web-app\\dukancard\\app\\components\\BottomNav.tsx", [], [], "C:\\web-app\\dukancard\\app\\components\\EnhancedPublicCardActions.tsx", [], [], "C:\\web-app\\dukancard\\app\\components\\FloatingAIButton.tsx", [], [], "C:\\web-app\\dukancard\\app\\components\\FloatingInteractionButtons.tsx", [], [], "C:\\web-app\\dukancard\\app\\components\\Footer.tsx", [], [], "C:\\web-app\\dukancard\\app\\components\\GoogleAnalytics.tsx", [], [], "C:\\web-app\\dukancard\\app\\components\\Header.tsx", [], [], "C:\\web-app\\dukancard\\app\\components\\icons\\FacebookIcon.tsx", [], [], "C:\\web-app\\dukancard\\app\\components\\icons\\InstagramIcon.tsx", [], [], "C:\\web-app\\dukancard\\app\\components\\icons\\LinkedInIcon.tsx", [], [], "C:\\web-app\\dukancard\\app\\components\\icons\\PinterestIcon.tsx", [], [], "C:\\web-app\\dukancard\\app\\components\\icons\\TelegramIcon.tsx", [], [], "C:\\web-app\\dukancard\\app\\components\\icons\\TwitterIcon.tsx", [], [], "C:\\web-app\\dukancard\\app\\components\\icons\\WhatsAppIcon.tsx", [], [], "C:\\web-app\\dukancard\\app\\components\\icons\\YouTubeIcon.tsx", [], [], "C:\\web-app\\dukancard\\app\\components\\LoadingOverlay.tsx", [], [], "C:\\web-app\\dukancard\\app\\components\\MetaPixel.tsx", [], ["3815"], "C:\\web-app\\dukancard\\app\\components\\MinimalFooter.tsx", [], [], "C:\\web-app\\dukancard\\app\\components\\MinimalHeader.tsx", [], [], "C:\\web-app\\dukancard\\app\\components\\MobileFooter.tsx", [], [], "C:\\web-app\\dukancard\\app\\components\\ProductListItem.tsx", [], [], "C:\\web-app\\dukancard\\app\\components\\PublicCardView.tsx", [], [], "C:\\web-app\\dukancard\\app\\components\\reviews\\AuthMessage.tsx", [], [], "C:\\web-app\\dukancard\\app\\components\\reviews\\ReviewForm.tsx", [], [], "C:\\web-app\\dukancard\\app\\components\\reviews\\ReviewSignInPrompt.tsx", [], [], "C:\\web-app\\dukancard\\app\\components\\reviews\\ReviewsList.tsx", [], [], "C:\\web-app\\dukancard\\app\\components\\reviews\\ReviewsSummary.tsx", [], [], "C:\\web-app\\dukancard\\app\\components\\reviews\\ReviewsTab.tsx", [], ["3816"], "C:\\web-app\\dukancard\\app\\components\\ReviewsTab.tsx", [], [], "C:\\web-app\\dukancard\\app\\components\\shared\\EnhancedCardActions.tsx", [], [], "C:\\web-app\\dukancard\\app\\components\\shared\\likes\\index.ts", [], [], "C:\\web-app\\dukancard\\app\\components\\shared\\likes\\LikeCard.tsx", [], [], "C:\\web-app\\dukancard\\app\\components\\shared\\likes\\LikeCardSkeleton.tsx", [], [], "C:\\web-app\\dukancard\\app\\components\\shared\\likes\\LikeList.tsx", [], [], "C:\\web-app\\dukancard\\app\\components\\shared\\likes\\LikePagination.tsx", [], [], "C:\\web-app\\dukancard\\app\\components\\shared\\likes\\LikeSearch.tsx", [], [], "C:\\web-app\\dukancard\\app\\components\\shared\\reviews\\ReviewCard.tsx", [], [], "C:\\web-app\\dukancard\\app\\components\\shared\\reviews\\ReviewCardSkeleton.tsx", [], [], "C:\\web-app\\dukancard\\app\\components\\shared\\reviews\\ReviewSortDropdown.tsx", [], [], "C:\\web-app\\dukancard\\app\\components\\shared\\subscriptions\\index.ts", [], [], "C:\\web-app\\dukancard\\app\\components\\shared\\subscriptions\\SubscriptionCard.tsx", [], [], "C:\\web-app\\dukancard\\app\\components\\shared\\subscriptions\\SubscriptionCardSkeleton.tsx", [], [], "C:\\web-app\\dukancard\\app\\components\\shared\\subscriptions\\SubscriptionList.tsx", [], [], "C:\\web-app\\dukancard\\app\\components\\shared\\subscriptions\\SubscriptionPagination.tsx", [], [], "C:\\web-app\\dukancard\\app\\components\\shared\\subscriptions\\SubscriptionSearch.tsx", [], [], "C:\\web-app\\dukancard\\app\\components\\ThemeToggle.tsx", [], [], "C:\\web-app\\dukancard\\app\\components\\ui\\container.tsx", [], [], "C:\\web-app\\dukancard\\app\\layout.tsx", [], [], "C:\\web-app\\dukancard\\app\\locality\\actions\\businessActions.ts", [], [], "C:\\web-app\\dukancard\\app\\locality\\actions\\combinedActions.ts", [], [], "C:\\web-app\\dukancard\\app\\locality\\actions\\locationActions.ts", [], [], "C:\\web-app\\dukancard\\app\\locality\\actions\\productActions.ts", [], ["3817"], "C:\\web-app\\dukancard\\app\\locality\\actions.ts", [], [], "C:\\web-app\\dukancard\\app\\locality\\components\\BreadcrumbNav.tsx", [], [], "C:\\web-app\\dukancard\\app\\locality\\components\\ErrorSection.tsx", [], [], "C:\\web-app\\dukancard\\app\\locality\\components\\ImprovedSearchSection.tsx", [], [], "C:\\web-app\\dukancard\\app\\locality\\components\\LocationIndicator.tsx", [], [], "C:\\web-app\\dukancard\\app\\locality\\components\\ModernResultsSection.tsx", [], [], "C:\\web-app\\dukancard\\app\\locality\\constants\\paginationConstants.ts", [], [], "C:\\web-app\\dukancard\\app\\locality\\context\\businessContextFunctions.ts", [], [], "C:\\web-app\\dukancard\\app\\locality\\context\\commonContextFunctions.ts", [], [], "C:\\web-app\\dukancard\\app\\locality\\context\\LocalityContext.tsx", [], ["3818"], "C:\\web-app\\dukancard\\app\\locality\\context\\productContextFunctions.ts", [], [], "C:\\web-app\\dukancard\\app\\locality\\context\\types.ts", [], [], "C:\\web-app\\dukancard\\app\\locality\\layout.tsx", [], [], "C:\\web-app\\dukancard\\app\\locality\\[localSlug]\\LocalityPageClient.tsx", [], [], "C:\\web-app\\dukancard\\app\\locality\\[localSlug]\\page.tsx", [], [], "C:\\web-app\\dukancard\\app\\post\\[postId]\\error.tsx", [], [], "C:\\web-app\\dukancard\\app\\post\\[postId]\\loading.tsx", [], [], "C:\\web-app\\dukancard\\app\\post\\[postId]\\not-found.tsx", [], [], "C:\\web-app\\dukancard\\app\\post\\[postId]\\page.tsx", [], [], "C:\\web-app\\dukancard\\app\\products\\sitemap.ts", [], [], "C:\\web-app\\dukancard\\app\\robots.ts", [], [], "C:\\web-app\\dukancard\\app\\sitemap.ts", [], [], "C:\\web-app\\dukancard\\app\\[cardSlug]\\actions.ts", [], [], "C:\\web-app\\dukancard\\app\\[cardSlug]\\components\\animations.ts", [], [], "C:\\web-app\\dukancard\\app\\[cardSlug]\\components\\BusinessDetails\\EnhancedMetricsCards.tsx", [], [], "C:\\web-app\\dukancard\\app\\[cardSlug]\\components\\BusinessDetails\\ProfessionalBusinessTable.tsx", [], [], "C:\\web-app\\dukancard\\app\\[cardSlug]\\components\\EnhancedAdSection.tsx", [], [], "C:\\web-app\\dukancard\\app\\[cardSlug]\\components\\EnhancedBusinessCardSection.tsx", [], [], "C:\\web-app\\dukancard\\app\\[cardSlug]\\components\\EnhancedBusinessDetails.tsx", [], [], "C:\\web-app\\dukancard\\app\\[cardSlug]\\components\\EnhancedBusinessGallery.tsx", [], [], "C:\\web-app\\dukancard\\app\\[cardSlug]\\components\\EnhancedPublicCardPageWrapper.tsx", [], [], "C:\\web-app\\dukancard\\app\\[cardSlug]\\components\\EnhancedTabsToggle.tsx", [], [], "C:\\web-app\\dukancard\\app\\[cardSlug]\\components\\GalleryTab.tsx", [], [], "C:\\web-app\\dukancard\\app\\[cardSlug]\\components\\OfflineBusinessMessage.tsx", [], [], "C:\\web-app\\dukancard\\app\\[cardSlug]\\components\\ProductGridSkeleton.tsx", [], [], "C:\\web-app\\dukancard\\app\\[cardSlug]\\components\\ProductsTab.tsx", [], [], "C:\\web-app\\dukancard\\app\\[cardSlug]\\gallery\\GalleryPageClient.tsx", [], [], "C:\\web-app\\dukancard\\app\\[cardSlug]\\gallery\\page.tsx", [], [], "C:\\web-app\\dukancard\\app\\[cardSlug]\\layout.tsx", [], [], "C:\\web-app\\dukancard\\app\\[cardSlug]\\loading.tsx", [], [], "C:\\web-app\\dukancard\\app\\[cardSlug]\\page.tsx", [], [], "C:\\web-app\\dukancard\\app\\[cardSlug]\\product\\actions.ts", [], [], "C:\\web-app\\dukancard\\app\\[cardSlug]\\product\\components\\BuyNowButton.tsx", [], [], "C:\\web-app\\dukancard\\app\\[cardSlug]\\product\\components\\ImageZoomModal.tsx", [], [], "C:\\web-app\\dukancard\\app\\[cardSlug]\\product\\components\\OfflineProductMessage.tsx", [], [], "C:\\web-app\\dukancard\\app\\[cardSlug]\\product\\components\\PhoneButton.tsx", [], [], "C:\\web-app\\dukancard\\app\\[cardSlug]\\product\\components\\ProductAdSection.tsx", [], [], "C:\\web-app\\dukancard\\app\\[cardSlug]\\product\\components\\ProductDetail.tsx", [], [], "C:\\web-app\\dukancard\\app\\[cardSlug]\\product\\components\\ProductRecommendations.tsx", [], [], "C:\\web-app\\dukancard\\app\\[cardSlug]\\product\\components\\VariantSelector.tsx", [], [], "C:\\web-app\\dukancard\\app\\[cardSlug]\\product\\components\\WhatsAppButton.tsx", [], [], "C:\\web-app\\dukancard\\app\\[cardSlug]\\product\\hooks\\usePinchZoom.ts", [], [], "C:\\web-app\\dukancard\\app\\[cardSlug]\\product\\[productSlug]\\page.tsx", [], [], "C:\\web-app\\dukancard\\app\\[cardSlug]\\product\\[productSlug]\\ProductDetailClient.tsx", [], [], "C:\\web-app\\dukancard\\app\\[cardSlug]\\PublicCardPageClient.tsx", [], [], "C:\\web-app\\dukancard\\components\\blog\\BlogCard.tsx", [], [], "C:\\web-app\\dukancard\\components\\blog\\BlogContent.tsx", [], [], "C:\\web-app\\dukancard\\components\\blog\\BlogListingSkeleton.tsx", [], [], "C:\\web-app\\dukancard\\components\\blog\\BlogNavigation.tsx", [], [], "C:\\web-app\\dukancard\\components\\blog\\BlogPagination.tsx", [], [], "C:\\web-app\\dukancard\\components\\blog\\BlogPostSkeleton.tsx", [], [], "C:\\web-app\\dukancard\\components\\blog\\BlogSearch.tsx", [], [], "C:\\web-app\\dukancard\\components\\feed\\ModernBusinessFeedList.tsx", [], [], "C:\\web-app\\dukancard\\components\\feed\\ModernCustomerFeedList.tsx", [], [], "C:\\web-app\\dukancard\\components\\feed\\shared\\dialogs\\PostDeleteDialog.tsx", [], [], "C:\\web-app\\dukancard\\components\\feed\\shared\\editors\\InlinePostAndProductEditor.tsx", [], [], "C:\\web-app\\dukancard\\components\\feed\\shared\\editors\\InlinePostEditor.tsx", [], [], "C:\\web-app\\dukancard\\components\\feed\\shared\\FilterPills.tsx", [], [], "C:\\web-app\\dukancard\\components\\feed\\shared\\forms\\ImageCropper.tsx", [], [], "C:\\web-app\\dukancard\\components\\feed\\shared\\forms\\LocationDisplay.tsx", [], [], "C:\\web-app\\dukancard\\components\\feed\\shared\\forms\\ProductSelector.tsx", [], [], "C:\\web-app\\dukancard\\components\\feed\\shared\\hooks\\useCustomerPostMediaUpload.ts", [], [], "C:\\web-app\\dukancard\\components\\feed\\shared\\hooks\\usePostMediaUpload.ts", [], [], "C:\\web-app\\dukancard\\components\\feed\\shared\\hooks\\usePostOwnership.ts", [], [], "C:\\web-app\\dukancard\\components\\feed\\shared\\ModernCustomerPostCard.tsx", [], [], "C:\\web-app\\dukancard\\components\\feed\\shared\\ModernFeedContainer.tsx", [], [], "C:\\web-app\\dukancard\\components\\feed\\shared\\ModernFeedHeader.tsx", [], [], "C:\\web-app\\dukancard\\components\\feed\\shared\\ModernPostCard.tsx", [], [], "C:\\web-app\\dukancard\\components\\feed\\shared\\PostActions.tsx", [], [], "C:\\web-app\\dukancard\\components\\feed\\shared\\PostCardSkeleton.tsx", [], [], "C:\\web-app\\dukancard\\components\\feed\\shared\\SocialMediaBusinessPostCreator.tsx", [], [], "C:\\web-app\\dukancard\\components\\feed\\shared\\SocialMediaPostCreator.tsx", [], [], "C:\\web-app\\dukancard\\components\\feed\\shared\\UnifiedPostCard.tsx", [], [], "C:\\web-app\\dukancard\\components\\post\\BackNavigation.tsx", [], [], "C:\\web-app\\dukancard\\components\\post\\ConditionalPostLayout.tsx", [], [], "C:\\web-app\\dukancard\\components\\post\\PostShareButton.tsx", [], [], "C:\\web-app\\dukancard\\components\\post\\SinglePostView.tsx", [], [], "C:\\web-app\\dukancard\\components\\qr\\QRScanner.tsx", [], [], "C:\\web-app\\dukancard\\components\\qr\\QRScannerModal.tsx", [], [], "C:\\web-app\\dukancard\\components\\sidebar\\BusinessAppSidebar.tsx", [], [], "C:\\web-app\\dukancard\\components\\sidebar\\CustomerAppSidebar.tsx", [], [], "C:\\web-app\\dukancard\\components\\sidebar\\NavBusinessMain.tsx", [], [], "C:\\web-app\\dukancard\\components\\sidebar\\NavBusinessUser.tsx", [], [], "C:\\web-app\\dukancard\\components\\sidebar\\NavCustomerMain.tsx", [], [], "C:\\web-app\\dukancard\\components\\sidebar\\NavCustomerUser.tsx", [], [], "C:\\web-app\\dukancard\\components\\sidebar\\SidebarLink.tsx", [], [], "C:\\web-app\\dukancard\\components\\ui\\accordion.tsx", [], [], "C:\\web-app\\dukancard\\components\\ui\\alert-dialog.tsx", [], [], "C:\\web-app\\dukancard\\components\\ui\\alert.tsx", [], [], "C:\\web-app\\dukancard\\components\\ui\\avatar.tsx", [], [], "C:\\web-app\\dukancard\\components\\ui\\badge.tsx", [], [], "C:\\web-app\\dukancard\\components\\ui\\breadcrumb.tsx", [], [], "C:\\web-app\\dukancard\\components\\ui\\button.tsx", [], [], "C:\\web-app\\dukancard\\components\\ui\\calendar.tsx", [], [], "C:\\web-app\\dukancard\\components\\ui\\card.tsx", [], [], "C:\\web-app\\dukancard\\components\\ui\\carousel.tsx", [], [], "C:\\web-app\\dukancard\\components\\ui\\category-combobox.tsx", [], [], "C:\\web-app\\dukancard\\components\\ui\\chart.tsx", [], [], "C:\\web-app\\dukancard\\components\\ui\\checkbox.tsx", [], [], "C:\\web-app\\dukancard\\components\\ui\\collapsible.tsx", [], [], "C:\\web-app\\dukancard\\components\\ui\\command.tsx", [], [], "C:\\web-app\\dukancard\\components\\ui\\dialog.tsx", [], [], "C:\\web-app\\dukancard\\components\\ui\\dropdown-menu.tsx", [], [], "C:\\web-app\\dukancard\\components\\ui\\form.tsx", [], [], "C:\\web-app\\dukancard\\components\\ui\\input-otp.tsx", [], [], "C:\\web-app\\dukancard\\components\\ui\\input.tsx", [], [], "C:\\web-app\\dukancard\\components\\ui\\label.tsx", [], [], "C:\\web-app\\dukancard\\components\\ui\\multi-select-combobox.tsx", [], [], "C:\\web-app\\dukancard\\components\\ui\\pagination.tsx", [], [], "C:\\web-app\\dukancard\\components\\ui\\popover.tsx", [], [], "C:\\web-app\\dukancard\\components\\ui\\progress.tsx", [], [], "C:\\web-app\\dukancard\\components\\ui\\radio-group.tsx", [], [], "C:\\web-app\\dukancard\\components\\ui\\resizable-navbar.tsx", [], [], "C:\\web-app\\dukancard\\components\\ui\\scroll-area.tsx", [], [], "C:\\web-app\\dukancard\\components\\ui\\scroll-to-top-button.tsx", [], [], "C:\\web-app\\dukancard\\components\\ui\\select.tsx", [], [], "C:\\web-app\\dukancard\\components\\ui\\separator.tsx", [], [], "C:\\web-app\\dukancard\\components\\ui\\sheet.tsx", [], [], "C:\\web-app\\dukancard\\components\\ui\\sidebar.tsx", [], [], "C:\\web-app\\dukancard\\components\\ui\\skeleton.tsx", [], [], "C:\\web-app\\dukancard\\components\\ui\\slider.tsx", [], [], "C:\\web-app\\dukancard\\components\\ui\\sonner.tsx", [], [], "C:\\web-app\\dukancard\\components\\ui\\switch.tsx", [], [], "C:\\web-app\\dukancard\\components\\ui\\table.tsx", [], [], "C:\\web-app\\dukancard\\components\\ui\\tabs.tsx", [], [], "C:\\web-app\\dukancard\\components\\ui\\textarea.tsx", [], [], "C:\\web-app\\dukancard\\components\\ui\\toast.tsx", [], [], "C:\\web-app\\dukancard\\components\\ui\\tooltip.tsx", [], [], "C:\\web-app\\dukancard\\components\\ui\\use-toast.ts", [], [], "C:\\web-app\\dukancard\\components\\ui\\visually-hidden.tsx", [], [], "C:\\web-app\\dukancard\\lib\\actions\\blogs.ts", [], [], "C:\\web-app\\dukancard\\lib\\actions\\businessProfiles\\access.ts", [], [], "C:\\web-app\\dukancard\\lib\\actions\\businessProfiles\\discovery.ts", [], [], "C:\\web-app\\dukancard\\lib\\actions\\businessProfiles\\index.ts", [], [], "C:\\web-app\\dukancard\\lib\\actions\\businessProfiles\\location.ts", [], [], "C:\\web-app\\dukancard\\lib\\actions\\businessProfiles\\profileRetrieval.ts", [], [], "C:\\web-app\\dukancard\\lib\\actions\\businessProfiles\\search.ts", [], [], "C:\\web-app\\dukancard\\lib\\actions\\businessProfiles\\sitemap.ts", [], [], "C:\\web-app\\dukancard\\lib\\actions\\businessProfiles\\types.ts", [], [], "C:\\web-app\\dukancard\\lib\\actions\\businessProfiles\\utils.ts", [], ["3819", "3820"], "C:\\web-app\\dukancard\\lib\\actions\\categories\\locationBasedFetching.ts", [], [], "C:\\web-app\\dukancard\\lib\\actions\\categories\\pincodeTypes.ts", [], [], "C:\\web-app\\dukancard\\lib\\actions\\customerPosts\\crud.ts", [], [], "C:\\web-app\\dukancard\\lib\\actions\\customerPosts\\index.ts", [], [], "C:\\web-app\\dukancard\\lib\\actions\\customerProfiles\\addressValidation.ts", [], [], "C:\\web-app\\dukancard\\lib\\actions\\gallery.ts", [], [], "C:\\web-app\\dukancard\\lib\\actions\\interactions.ts", [], [], "C:\\web-app\\dukancard\\lib\\actions\\location\\locationBySlug.ts", [], [], "C:\\web-app\\dukancard\\lib\\actions\\location.ts", [], [], "C:\\web-app\\dukancard\\lib\\actions\\posts\\crud.ts", [], [], "C:\\web-app\\dukancard\\lib\\actions\\posts\\fetchSinglePost.ts", [], [], "C:\\web-app\\dukancard\\lib\\actions\\posts\\index.ts", [], [], "C:\\web-app\\dukancard\\lib\\actions\\posts\\types.ts", [], [], "C:\\web-app\\dukancard\\lib\\actions\\posts\\unifiedFeed.ts", [], [], "C:\\web-app\\dukancard\\lib\\actions\\posts\\utils.ts", [], [], "C:\\web-app\\dukancard\\lib\\actions\\posts.ts", [], [], "C:\\web-app\\dukancard\\lib\\actions\\products\\fetchProductsByIds.ts", [], [], "C:\\web-app\\dukancard\\lib\\actions\\products\\sitemapHelpers.ts", [], [], "C:\\web-app\\dukancard\\lib\\actions\\redirectAfterLogin.ts", [], [], "C:\\web-app\\dukancard\\lib\\actions\\reviews.ts", [], [], "C:\\web-app\\dukancard\\lib\\actions\\secureCustomerProfiles.ts", ["3821", "3822"], [], "C:\\web-app\\dukancard\\lib\\actions\\shared\\delete-customer-post-media.ts", [], [], "C:\\web-app\\dukancard\\lib\\actions\\shared\\productActions.ts", [], [], "C:\\web-app\\dukancard\\lib\\actions\\shared\\upload-business-post-media.ts", [], [], "C:\\web-app\\dukancard\\lib\\actions\\shared\\upload-customer-post-media.ts", [], [], "C:\\web-app\\dukancard\\lib\\actions\\shared\\upload-post-media.ts", [], [], "C:\\web-app\\dukancard\\lib\\api\\response.ts", [], [], "C:\\web-app\\dukancard\\lib\\cardDownloader.ts", [], [], "C:\\web-app\\dukancard\\lib\\client\\locationUtils.ts", [], [], "C:\\web-app\\dukancard\\lib\\config\\categories.ts", [], [], "C:\\web-app\\dukancard\\lib\\config\\states.ts", [], [], "C:\\web-app\\dukancard\\lib\\constants\\predefinedVariants.ts", [], [], "C:\\web-app\\dukancard\\lib\\csrf.ts", [], [], "C:\\web-app\\dukancard\\lib\\errorHandling.ts", [], ["3823", "3824"], "C:\\web-app\\dukancard\\lib\\qrCodeGenerator.ts", [], [], "C:\\web-app\\dukancard\\lib\\rateLimiter.ts", [], [], "C:\\web-app\\dukancard\\lib\\schemas\\authSchemas.ts", [], [], "C:\\web-app\\dukancard\\lib\\schemas\\locationSchemas.ts", [], [], "C:\\web-app\\dukancard\\lib\\services\\realtimeService.ts", [], ["3825"], "C:\\web-app\\dukancard\\lib\\services\\socialService.ts", ["3826", "3827", "3828", "3829", "3830", "3831"], [], "C:\\web-app\\dukancard\\lib\\site-config.ts", [], [], "C:\\web-app\\dukancard\\lib\\siteContent.ts", [], [], "C:\\web-app\\dukancard\\lib\\supabase\\constants.ts", [], [], "C:\\web-app\\dukancard\\lib\\types\\activities.ts", [], [], "C:\\web-app\\dukancard\\lib\\types\\api.ts", [], [], "C:\\web-app\\dukancard\\lib\\types\\blog.ts", [], [], "C:\\web-app\\dukancard\\lib\\types\\posts.ts", [], [], "C:\\web-app\\dukancard\\lib\\utils\\addressUtils.ts", [], [], "C:\\web-app\\dukancard\\lib\\utils\\addressValidation.ts", [], [], "C:\\web-app\\dukancard\\lib\\utils\\cameraUtils.ts", [], [], "C:\\web-app\\dukancard\\lib\\utils\\client-image-compression.ts", [], [], "C:\\web-app\\dukancard\\lib\\utils\\customBranding.ts", [], [], "C:\\web-app\\dukancard\\lib\\utils\\debounce.ts", [], [], "C:\\web-app\\dukancard\\lib\\utils\\feed\\diversityEngine.ts", [], [], "C:\\web-app\\dukancard\\lib\\utils\\feed\\feedMerger.ts", [], [], "C:\\web-app\\dukancard\\lib\\utils\\feed\\hybridTimeAndPlanAlgorithm.ts", [], [], "C:\\web-app\\dukancard\\lib\\utils\\feed\\optimizedHybridAlgorithm.ts", [], [], "C:\\web-app\\dukancard\\lib\\utils\\feed\\planPrioritizer.ts", [], [], "C:\\web-app\\dukancard\\lib\\utils\\feed\\postCreationHandler.ts", [], [], "C:\\web-app\\dukancard\\lib\\utils\\feed\\smartFeedAlgorithm.ts", [], [], "C:\\web-app\\dukancard\\lib\\utils\\image-compression.ts", [], [], "C:\\web-app\\dukancard\\lib\\utils\\markdown.ts", [], [], "C:\\web-app\\dukancard\\lib\\utils\\pagination.ts", [], [], "C:\\web-app\\dukancard\\lib\\utils\\postUrl.ts", [], [], "C:\\web-app\\dukancard\\lib\\utils\\qrCodeUtils.ts", [], [], "C:\\web-app\\dukancard\\lib\\utils\\seo.ts", [], [], "C:\\web-app\\dukancard\\lib\\utils\\slugUtils.ts", [], [], "C:\\web-app\\dukancard\\lib\\utils\\storage-paths.ts", [], [], "C:\\web-app\\dukancard\\lib\\utils\\supabaseErrorHandler.ts", [], [], "C:\\web-app\\dukancard\\lib\\utils\\variantHelpers.ts", [], [], "C:\\web-app\\dukancard\\lib\\utils.ts", [], [], "C:\\web-app\\dukancard\\components\\ui\\CommentDisplay.tsx", [], [], "C:\\web-app\\dukancard\\components\\ui\\CommentInput.tsx", [], [], "C:\\web-app\\dukancard\\components\\ui\\CommentSection.tsx", [], [], "C:\\web-app\\dukancard\\components\\ui\\LikeButton.tsx", [], [], "C:\\web-app\\dukancard\\lib\\actions\\comments\\postComments.ts", [], [], "C:\\web-app\\dukancard\\lib\\actions\\likes\\commentLikes.ts", [], [], "C:\\web-app\\dukancard\\lib\\actions\\likes\\postLikes.ts", [], [], "C:\\web-app\\dukancard\\lib\\stores\\likeCommentStore.ts", [], [], "C:\\web-app\\dukancard\\lib\\utils\\formatNumber.ts", [], [], "C:\\web-app\\dukancard\\components\\post\\PostLikesList.tsx", [], [], "C:\\web-app\\dukancard\\lib\\services\\pagination\\paginationService.ts", [], ["3832", "3833", "3834"], "C:\\web-app\\dukancard\\app\\(onboarding)\\onboarding\\components\\steps\\WelcomeStep.tsx", [], [], "C:\\web-app\\dukancard\\app\\api\\auth\\login\\route.ts", [], [], "C:\\web-app\\dukancard\\app\\api\\auth\\logout\\route.ts", [], [], "C:\\web-app\\dukancard\\app\\api\\auth\\refresh\\route.ts", ["3835", "3836", "3837", "3838"], [], "C:\\web-app\\dukancard\\app\\api\\devices\\register\\route.ts", [], [], "C:\\web-app\\dukancard\\lib\\auth\\jwt.ts", ["3839", "3840"], [], "C:\\web-app\\dukancard\\lib\\auth\\utils.ts", [], [], "C:\\web-app\\dukancard\\lib\\middleware\\hmac.ts", [], [], "C:\\web-app\\dukancard\\lib\\security\\hashing.ts", [], [], "C:\\web-app\\dukancard\\lib\\security\\hmac.ts", ["3841", "3842", "3843"], [], "C:\\web-app\\dukancard\\lib\\middleware\\bruteForceProtection.ts", [], [], "C:\\web-app\\dukancard\\lib\\actions\\auth.ts", [], [], "C:\\web-app\\dukancard\\lib\\stores\\authStore.ts", [], [], "C:\\web-app\\dukancard\\lib\\utils\\authenticatedFetch.ts", ["3844"], [], "C:\\web-app\\dukancard\\app\\api\\auth\\send-otp\\route.ts", ["3845", "3846", "3847", "3848"], [], "C:\\web-app\\dukancard\\app\\api\\auth\\verify-otp\\route.ts", ["3849", "3850", "3851", "3852"], [], "C:\\web-app\\dukancard\\lib\\auth\\middleware-jwt.ts", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\overview\\BusinessOverviewClient.tsx", [], [], "C:\\web-app\\dukancard\\app\\api\\business\\access\\route.ts", [], [], "C:\\web-app\\dukancard\\app\\api\\business\\me\\route.ts", [], [], "C:\\web-app\\dukancard\\app\\api\\business\\profile\\exists\\route.ts", [], [], "C:\\web-app\\dukancard\\app\\api\\business\\route.ts", ["3853"], [], "C:\\web-app\\dukancard\\app\\api\\business\\search\\route.ts", ["3854"], [], "C:\\web-app\\dukancard\\app\\api\\business\\sitemap\\route.ts", ["3855", "3856", "3857", "3858", "3859"], [], "C:\\web-app\\dukancard\\app\\api\\business\\slug\\[slug]\\route.ts", ["3860", "3861"], [], "C:\\web-app\\dukancard\\app\\api\\business\\[id]\\route.ts", [], [], "C:\\web-app\\dukancard\\app\\api\\customer\\profile\\exists\\route.ts", [], [], "C:\\web-app\\dukancard\\app\\api\\customer\\profile\\route.ts", [], [], "C:\\web-app\\dukancard\\app\\api\\likes\\route.ts", [], [], "C:\\web-app\\dukancard\\app\\api\\location\\city\\[city]\\route.ts", ["3862"], [], "C:\\web-app\\dukancard\\app\\api\\location\\pincode\\[pincode]\\route.ts", ["3863", "3864", "3865", "3866", "3867"], [], "C:\\web-app\\dukancard\\app\\api\\posts\\route.ts", ["3868"], [], "C:\\web-app\\dukancard\\app\\api\\products\\route.ts", ["3869"], [], "C:\\web-app\\dukancard\\app\\api\\products\\[id]\\route.ts", ["3870", "3871", "3872", "3873", "3874", "3875", "3876", "3877"], [], "C:\\web-app\\dukancard\\app\\api\\storage\\upload\\route.ts", [], [], "C:\\web-app\\dukancard\\lib\\stores\\businessProfileStore.ts", [], [], "C:\\web-app\\dukancard\\lib\\stores\\postsStore.ts", [], [], "C:\\web-app\\dukancard\\lib\\stores\\productsStore.ts", [], [], "C:\\web-app\\dukancard\\lib\\stores\\storageStore.ts", [], [], "C:\\web-app\\dukancard\\app\\api\\comments\\route.ts", [], [], "C:\\web-app\\dukancard\\lib\\stores\\customerProfileStore.ts", [], [], "C:\\web-app\\dukancard\\app\\api\\comments\\[id]\\route.ts", [], [], "C:\\web-app\\dukancard\\lib\\utils\\internalApiClient.ts", ["3878", "3879", "3880", "3881", "3882", "3883", "3884", "3885", "3886", "3887", "3888", "3889", "3890", "3891", "3892", "3893", "3894", "3895"], [], "C:\\web-app\\dukancard\\app\\api\\subscriptions\\route.ts", [], [], "C:\\web-app\\dukancard\\app\\api\\reviews\\route.ts", [], [], "C:\\web-app\\dukancard\\app\\api\\gallery\\public\\[businessSlug]\\route.ts", [], [], "C:\\web-app\\dukancard\\app\\api\\gallery\\route.ts", ["3896", "3897"], [], "C:\\web-app\\dukancard\\app\\api\\gallery\\[imageId]\\route.ts", ["3898", "3899", "3900", "3901"], [], "C:\\web-app\\dukancard\\app\\api\\posts\\[postId]\\route.ts", ["3902"], [], "C:\\web-app\\dukancard\\app\\api\\user\\profile\\route.ts", [], [], "C:\\web-app\\dukancard\\lib\\auth\\getAuthContext.ts", ["3903"], ["3904", "3905"], "C:\\web-app\\dukancard\\lib\\middleware\\routeProtection.ts", ["3906", "3907", "3908", "3909"], [], "C:\\web-app\\dukancard\\lib\\security\\csrf.ts", [], [], "C:\\web-app\\dukancard\\lib\\utils\\jwt-error-handler.ts", [], [], "C:\\web-app\\dukancard\\lib\\middleware\\rateLimiter.ts", [], [], "C:\\web-app\\dukancard\\lib\\services\\deviceService.ts", [], [], "C:\\web-app\\dukancard\\lib\\services\\securityService.ts", ["3910", "3911", "3912", "3913", "3914", "3915"], [], "C:\\web-app\\dukancard\\lib\\utils\\redis.ts", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\admin\\components\\AdminDashboardClient.tsx", ["3916", "3917", "3918", "3919", "3920", "3921"], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\admin\\components\\AuditLogsTab.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\admin\\components\\DeviceDetailModal.tsx", ["3922", "3923", "3924", "3925", "3926"], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\admin\\components\\DeviceManagementTab.tsx", ["3927", "3928", "3929", "3930", "3931", "3932", "3933", "3934", "3935", "3936"], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\admin\\components\\SecurityAlertsTab.tsx", ["3937", "3938", "3939", "3940"], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\admin\\page.tsx", [], [], "C:\\web-app\\dukancard\\app\\api\\admin\\audit\\logs\\route.ts", [], [], "C:\\web-app\\dukancard\\app\\api\\admin\\devices\\list\\route.ts", ["3941", "3942"], [], "C:\\web-app\\dukancard\\app\\api\\admin\\devices\\quarantine\\route.ts", [], [], "C:\\web-app\\dukancard\\app\\api\\admin\\devices\\revoke\\route.ts", [], [], "C:\\web-app\\dukancard\\app\\api\\admin\\devices\\[deviceId]\\route.ts", [], [], "C:\\web-app\\dukancard\\app\\api\\admin\\security\\alerts\\route.ts", [], [], "C:\\web-app\\dukancard\\lib\\hooks\\admin\\useAdminDevices.ts", [], [], "C:\\web-app\\dukancard\\lib\\hooks\\admin\\useAuditLogs.ts", [], [], "C:\\web-app\\dukancard\\lib\\hooks\\admin\\useSecurityAlerts.ts", [], [], "C:\\web-app\\dukancard\\lib\\services\\deviceAnalytics.ts", ["3943", "3944"], [], "C:\\web-app\\dukancard\\lib\\services\\securityAnalytics.ts", ["3945", "3946", "3947"], [], "C:\\web-app\\dukancard\\lib\\services\\tokenService.ts", ["3948", "3949"], [], "C:\\web-app\\dukancard\\lib\\utils\\adminAuth.ts", ["3950", "3951"], [], {"ruleId": "3952", "severity": 1, "message": "3953", "line": 27, "column": 20, "nodeType": "3954", "messageId": "3955", "endLine": 27, "endColumn": 23, "suggestions": "3956"}, {"ruleId": "3957", "severity": 1, "message": "3958", "line": 282, "column": 6, "nodeType": "3959", "endLine": 282, "endColumn": 8, "suggestions": "3960", "suppressions": "3961"}, {"ruleId": "3962", "severity": 1, "message": "3963", "line": 1, "column": 10, "nodeType": "3964", "messageId": "3965", "endLine": 1, "endColumn": 21, "suggestions": "3966"}, {"ruleId": "3967", "severity": 1, "message": "3963", "line": 1, "column": 10, "nodeType": null, "messageId": "3965", "endLine": 1, "endColumn": 21}, {"ruleId": "3962", "severity": 1, "message": "3968", "line": 2, "column": 10, "nodeType": "3964", "messageId": "3965", "endLine": 2, "endColumn": 28, "suggestions": "3969"}, {"ruleId": "3967", "severity": 1, "message": "3968", "line": 2, "column": 10, "nodeType": null, "messageId": "3965", "endLine": 2, "endColumn": 28}, {"ruleId": "3962", "severity": 1, "message": "3970", "line": 3, "column": 10, "nodeType": "3964", "messageId": "3965", "endLine": 3, "endColumn": 21, "suggestions": "3971"}, {"ruleId": "3967", "severity": 1, "message": "3970", "line": 3, "column": 10, "nodeType": null, "messageId": "3965", "endLine": 3, "endColumn": 21}, {"ruleId": "3972", "severity": 1, "message": "3973", "line": 25, "column": 9, "nodeType": "3974", "endLine": 31, "endColumn": 11, "suppressions": "3975"}, {"ruleId": "3957", "severity": 1, "message": "3976", "line": 53, "column": 6, "nodeType": "3959", "endLine": 53, "endColumn": 46, "suggestions": "3977", "suppressions": "3978"}, {"ruleId": "3952", "severity": 1, "message": "3953", "line": 159, "column": 62, "nodeType": "3954", "messageId": "3955", "endLine": 159, "endColumn": 65, "suggestions": "3979", "suppressions": "3980"}, {"ruleId": "3957", "severity": 1, "message": "3981", "line": 172, "column": 6, "nodeType": "3959", "endLine": 172, "endColumn": 8, "suggestions": "3982", "suppressions": "3983"}, {"ruleId": "3952", "severity": 1, "message": "3953", "line": 9, "column": 37, "nodeType": "3954", "messageId": "3955", "endLine": 9, "endColumn": 40, "suggestions": "3984", "suppressions": "3985"}, {"ruleId": "3952", "severity": 1, "message": "3953", "line": 9, "column": 67, "nodeType": "3954", "messageId": "3955", "endLine": 9, "endColumn": 70, "suggestions": "3986", "suppressions": "3987"}, {"ruleId": "3962", "severity": 1, "message": "3988", "line": 66, "column": 11, "nodeType": "3964", "messageId": "3965", "endLine": 66, "endColumn": 35, "suggestions": "3989"}, {"ruleId": "3967", "severity": 1, "message": "3988", "line": 66, "column": 11, "nodeType": null, "messageId": "3965", "endLine": 66, "endColumn": 35}, {"ruleId": "3952", "severity": 1, "message": "3953", "line": 6, "column": 18, "nodeType": "3954", "messageId": "3955", "endLine": 6, "endColumn": 21, "suggestions": "3990", "suppressions": "3991"}, {"ruleId": "3952", "severity": 1, "message": "3953", "line": 13, "column": 10, "nodeType": "3954", "messageId": "3955", "endLine": 13, "endColumn": 13, "suggestions": "3992", "suppressions": "3993"}, {"ruleId": "3952", "severity": 1, "message": "3953", "line": 55, "column": 31, "nodeType": "3954", "messageId": "3955", "endLine": 55, "endColumn": 34, "suggestions": "3994", "suppressions": "3995"}, {"ruleId": "3952", "severity": 1, "message": "3953", "line": 162, "column": 15, "nodeType": "3954", "messageId": "3955", "endLine": 162, "endColumn": 18, "suggestions": "3996"}, {"ruleId": "3952", "severity": 1, "message": "3953", "line": 251, "column": 15, "nodeType": "3954", "messageId": "3955", "endLine": 251, "endColumn": 18, "suggestions": "3997"}, {"ruleId": "3952", "severity": 1, "message": "3953", "line": 314, "column": 16, "nodeType": "3954", "messageId": "3955", "endLine": 314, "endColumn": 19, "suggestions": "3998"}, {"ruleId": "3952", "severity": 1, "message": "3953", "line": 405, "column": 16, "nodeType": "3954", "messageId": "3955", "endLine": 405, "endColumn": 19, "suggestions": "3999"}, {"ruleId": "3952", "severity": 1, "message": "3953", "line": 478, "column": 18, "nodeType": "3954", "messageId": "3955", "endLine": 478, "endColumn": 21, "suggestions": "4000"}, {"ruleId": "3952", "severity": 1, "message": "3953", "line": 659, "column": 18, "nodeType": "3954", "messageId": "3955", "endLine": 659, "endColumn": 21, "suggestions": "4001"}, {"ruleId": "3952", "severity": 1, "message": "3953", "line": 104, "column": 47, "nodeType": "3954", "messageId": "3955", "endLine": 104, "endColumn": 50, "suggestions": "4002", "suppressions": "4003"}, {"ruleId": "3952", "severity": 1, "message": "3953", "line": 233, "column": 53, "nodeType": "3954", "messageId": "3955", "endLine": 233, "endColumn": 56, "suggestions": "4004", "suppressions": "4005"}, {"ruleId": "3952", "severity": 1, "message": "3953", "line": 391, "column": 50, "nodeType": "3954", "messageId": "3955", "endLine": 391, "endColumn": 53, "suggestions": "4006", "suppressions": "4007"}, {"ruleId": "3962", "severity": 1, "message": "4008", "line": 6, "column": 10, "nodeType": "3964", "messageId": "3965", "endLine": 6, "endColumn": 25, "suggestions": "4009"}, {"ruleId": "3967", "severity": 1, "message": "4008", "line": 6, "column": 10, "nodeType": null, "messageId": "3965", "endLine": 6, "endColumn": 25}, {"ruleId": "3962", "severity": 1, "message": "3970", "line": 7, "column": 10, "nodeType": "3964", "messageId": "3965", "endLine": 7, "endColumn": 21, "suggestions": "4010"}, {"ruleId": "3967", "severity": 1, "message": "3970", "line": 7, "column": 10, "nodeType": null, "messageId": "3965", "endLine": 7, "endColumn": 21}, {"ruleId": "3962", "severity": 1, "message": "4011", "line": 66, "column": 72, "nodeType": "3964", "messageId": "3965", "endLine": 66, "endColumn": 84, "suggestions": "4012"}, {"ruleId": "3962", "severity": 1, "message": "4011", "line": 106, "column": 53, "nodeType": "3964", "messageId": "3965", "endLine": 106, "endColumn": 65, "suggestions": "4013"}, {"ruleId": "3952", "severity": 1, "message": "3953", "line": 96, "column": 19, "nodeType": "3954", "messageId": "3955", "endLine": 96, "endColumn": 22, "suggestions": "4014"}, {"ruleId": "3952", "severity": 1, "message": "3953", "line": 99, "column": 19, "nodeType": "3954", "messageId": "3955", "endLine": 99, "endColumn": 22, "suggestions": "4015"}, {"ruleId": "3952", "severity": 1, "message": "3953", "line": 102, "column": 19, "nodeType": "3954", "messageId": "3955", "endLine": 102, "endColumn": 22, "suggestions": "4016"}, {"ruleId": "3952", "severity": 1, "message": "3953", "line": 18, "column": 48, "nodeType": "3954", "messageId": "3955", "endLine": 18, "endColumn": 51, "suggestions": "4017"}, {"ruleId": "3962", "severity": 1, "message": "4008", "line": 4, "column": 10, "nodeType": "3964", "messageId": "3965", "endLine": 4, "endColumn": 25, "suggestions": "4018"}, {"ruleId": "3967", "severity": 1, "message": "4008", "line": 4, "column": 10, "nodeType": null, "messageId": "3965", "endLine": 4, "endColumn": 25}, {"ruleId": "3962", "severity": 1, "message": "3970", "line": 5, "column": 10, "nodeType": "3964", "messageId": "3965", "endLine": 5, "endColumn": 21, "suggestions": "4019"}, {"ruleId": "3967", "severity": 1, "message": "3970", "line": 5, "column": 10, "nodeType": null, "messageId": "3965", "endLine": 5, "endColumn": 21}, {"ruleId": "3962", "severity": 1, "message": "4008", "line": 7, "column": 10, "nodeType": "3964", "messageId": "3965", "endLine": 7, "endColumn": 25, "suggestions": "4020"}, {"ruleId": "3967", "severity": 1, "message": "4008", "line": 7, "column": 10, "nodeType": null, "messageId": "3965", "endLine": 7, "endColumn": 25}, {"ruleId": "3962", "severity": 1, "message": "3970", "line": 8, "column": 10, "nodeType": "3964", "messageId": "3965", "endLine": 8, "endColumn": 21, "suggestions": "4021"}, {"ruleId": "3967", "severity": 1, "message": "3970", "line": 8, "column": 10, "nodeType": null, "messageId": "3965", "endLine": 8, "endColumn": 21}, {"ruleId": "3952", "severity": 1, "message": "3953", "line": 246, "column": 39, "nodeType": "3954", "messageId": "3955", "endLine": 246, "endColumn": 42, "suggestions": "4022"}, {"ruleId": "3952", "severity": 1, "message": "3953", "line": 145, "column": 47, "nodeType": "3954", "messageId": "3955", "endLine": 145, "endColumn": 50, "suggestions": "4023"}, {"ruleId": "3962", "severity": 1, "message": "4008", "line": 2, "column": 10, "nodeType": "3964", "messageId": "3965", "endLine": 2, "endColumn": 25, "suggestions": "4024"}, {"ruleId": "3967", "severity": 1, "message": "4008", "line": 2, "column": 10, "nodeType": null, "messageId": "3965", "endLine": 2, "endColumn": 25}, {"ruleId": "3962", "severity": 1, "message": "3970", "line": 3, "column": 10, "nodeType": "3964", "messageId": "3965", "endLine": 3, "endColumn": 21, "suggestions": "4025"}, {"ruleId": "3967", "severity": 1, "message": "3970", "line": 3, "column": 10, "nodeType": null, "messageId": "3965", "endLine": 3, "endColumn": 21}, {"ruleId": "3952", "severity": 1, "message": "3953", "line": 74, "column": 32, "nodeType": "3954", "messageId": "3955", "endLine": 74, "endColumn": 35, "suggestions": "4026"}, {"ruleId": "3962", "severity": 1, "message": "4027", "line": 87, "column": 9, "nodeType": "3964", "messageId": "3965", "endLine": 87, "endColumn": 16, "suggestions": "4028"}, {"ruleId": "3967", "severity": 1, "message": "4027", "line": 87, "column": 9, "nodeType": null, "messageId": "3965", "endLine": 87, "endColumn": 16}, {"ruleId": "3952", "severity": 1, "message": "3953", "line": 59, "column": 27, "nodeType": "3954", "messageId": "3955", "endLine": 59, "endColumn": 30, "suggestions": "4029"}, {"ruleId": "3962", "severity": 1, "message": "4008", "line": 2, "column": 10, "nodeType": "3964", "messageId": "3965", "endLine": 2, "endColumn": 25, "suggestions": "4030"}, {"ruleId": "3967", "severity": 1, "message": "4008", "line": 2, "column": 10, "nodeType": null, "messageId": "3965", "endLine": 2, "endColumn": 25}, {"ruleId": "3962", "severity": 1, "message": "3970", "line": 3, "column": 10, "nodeType": "3964", "messageId": "3965", "endLine": 3, "endColumn": 21, "suggestions": "4031"}, {"ruleId": "3967", "severity": 1, "message": "3970", "line": 3, "column": 10, "nodeType": null, "messageId": "3965", "endLine": 3, "endColumn": 21}, {"ruleId": "3952", "severity": 1, "message": "3953", "line": 86, "column": 42, "nodeType": "3954", "messageId": "3955", "endLine": 86, "endColumn": 45, "suggestions": "4032"}, {"ruleId": "3952", "severity": 1, "message": "3953", "line": 118, "column": 30, "nodeType": "3954", "messageId": "3955", "endLine": 118, "endColumn": 33, "suggestions": "4033"}, {"ruleId": "3952", "severity": 1, "message": "3953", "line": 246, "column": 20, "nodeType": "3954", "messageId": "3955", "endLine": 246, "endColumn": 23, "suggestions": "4034"}, {"ruleId": "3962", "severity": 1, "message": "3968", "line": 4, "column": 3, "nodeType": "3964", "messageId": "3965", "endLine": 4, "endColumn": 21, "suggestions": "4035"}, {"ruleId": "3967", "severity": 1, "message": "3968", "line": 4, "column": 3, "nodeType": null, "messageId": "3965", "endLine": 4, "endColumn": 21}, {"ruleId": "3962", "severity": 1, "message": "4036", "line": 88, "column": 7, "nodeType": "3964", "messageId": "3965", "endLine": 88, "endColumn": 26}, {"ruleId": "3967", "severity": 1, "message": "4036", "line": 88, "column": 7, "nodeType": null, "messageId": "3965", "endLine": 88, "endColumn": 26}, {"ruleId": "3962", "severity": 1, "message": "4037", "line": 109, "column": 13, "nodeType": "3964", "messageId": "3965", "endLine": 109, "endColumn": 15, "suggestions": "4038"}, {"ruleId": "3967", "severity": 1, "message": "4037", "line": 109, "column": 13, "nodeType": null, "messageId": "3965", "endLine": 109, "endColumn": 15}, {"ruleId": "3962", "severity": 1, "message": "4037", "line": 140, "column": 13, "nodeType": "3964", "messageId": "3965", "endLine": 140, "endColumn": 15, "suggestions": "4039"}, {"ruleId": "3967", "severity": 1, "message": "4037", "line": 140, "column": 13, "nodeType": null, "messageId": "3965", "endLine": 140, "endColumn": 15}, {"ruleId": "3952", "severity": 1, "message": "3953", "line": 15, "column": 35, "nodeType": "3954", "messageId": "3955", "endLine": 15, "endColumn": 38, "suggestions": "4040"}, {"ruleId": "3952", "severity": 1, "message": "3953", "line": 35, "column": 18, "nodeType": "3954", "messageId": "3955", "endLine": 35, "endColumn": 21, "suggestions": "4041"}, {"ruleId": "3952", "severity": 1, "message": "3953", "line": 107, "column": 17, "nodeType": "3954", "messageId": "3955", "endLine": 107, "endColumn": 20, "suggestions": "4042"}, {"ruleId": "3952", "severity": 1, "message": "3953", "line": 114, "column": 18, "nodeType": "3954", "messageId": "3955", "endLine": 114, "endColumn": 21, "suggestions": "4043"}, {"ruleId": "3952", "severity": 1, "message": "3953", "line": 114, "column": 48, "nodeType": "3954", "messageId": "3955", "endLine": 114, "endColumn": 51, "suggestions": "4044"}, {"ruleId": "3952", "severity": 1, "message": "3953", "line": 125, "column": 19, "nodeType": "3954", "messageId": "3955", "endLine": 125, "endColumn": 22, "suggestions": "4045"}, {"ruleId": "3952", "severity": 1, "message": "3953", "line": 125, "column": 49, "nodeType": "3954", "messageId": "3955", "endLine": 125, "endColumn": 52, "suggestions": "4046"}, {"ruleId": "3952", "severity": 1, "message": "3953", "line": 136, "column": 17, "nodeType": "3954", "messageId": "3955", "endLine": 136, "endColumn": 20, "suggestions": "4047"}, {"ruleId": "3952", "severity": 1, "message": "3953", "line": 136, "column": 47, "nodeType": "3954", "messageId": "3955", "endLine": 136, "endColumn": 50, "suggestions": "4048"}, {"ruleId": "3952", "severity": 1, "message": "3953", "line": 147, "column": 20, "nodeType": "3954", "messageId": "3955", "endLine": 147, "endColumn": 23, "suggestions": "4049"}, {"ruleId": "3952", "severity": 1, "message": "3953", "line": 187, "column": 43, "nodeType": "3954", "messageId": "3955", "endLine": 187, "endColumn": 46, "suggestions": "4050"}, {"ruleId": "3952", "severity": 1, "message": "3953", "line": 198, "column": 50, "nodeType": "3954", "messageId": "3955", "endLine": 198, "endColumn": 53, "suggestions": "4051"}, {"ruleId": "3952", "severity": 1, "message": "3953", "line": 210, "column": 39, "nodeType": "3954", "messageId": "3955", "endLine": 210, "endColumn": 42, "suggestions": "4052"}, {"ruleId": "3952", "severity": 1, "message": "3953", "line": 221, "column": 40, "nodeType": "3954", "messageId": "3955", "endLine": 221, "endColumn": 43, "suggestions": "4053"}, {"ruleId": "3952", "severity": 1, "message": "3953", "line": 223, "column": 10, "nodeType": "3954", "messageId": "3955", "endLine": 223, "endColumn": 13, "suggestions": "4054"}, {"ruleId": "3952", "severity": 1, "message": "3953", "line": 233, "column": 41, "nodeType": "3954", "messageId": "3955", "endLine": 233, "endColumn": 44, "suggestions": "4055"}, {"ruleId": "3952", "severity": 1, "message": "3953", "line": 235, "column": 10, "nodeType": "3954", "messageId": "3955", "endLine": 235, "endColumn": 13, "suggestions": "4056"}, {"ruleId": "3952", "severity": 1, "message": "3953", "line": 245, "column": 42, "nodeType": "3954", "messageId": "3955", "endLine": 245, "endColumn": 45, "suggestions": "4057"}, {"ruleId": "3962", "severity": 1, "message": "4008", "line": 4, "column": 3, "nodeType": "3964", "messageId": "3965", "endLine": 4, "endColumn": 18, "suggestions": "4058"}, {"ruleId": "3967", "severity": 1, "message": "4008", "line": 4, "column": 3, "nodeType": null, "messageId": "3965", "endLine": 4, "endColumn": 18}, {"ruleId": "3962", "severity": 1, "message": "4059", "line": 100, "column": 5, "nodeType": "3964", "messageId": "3965", "endLine": 100, "endColumn": 17}, {"ruleId": "3967", "severity": 1, "message": "4059", "line": 100, "column": 5, "nodeType": null, "messageId": "3965", "endLine": 100, "endColumn": 17}, {"ruleId": "3952", "severity": 1, "message": "3953", "line": 126, "column": 19, "nodeType": "3954", "messageId": "3955", "endLine": 126, "endColumn": 22, "suggestions": "4060"}, {"ruleId": "3952", "severity": 1, "message": "3953", "line": 146, "column": 57, "nodeType": "3954", "messageId": "3955", "endLine": 146, "endColumn": 60, "suggestions": "4061"}, {"ruleId": "4062", "severity": 2, "message": "4063", "line": 262, "column": 7, "nodeType": "3964", "messageId": "4064", "endLine": 262, "endColumn": 21, "fix": "4065"}, {"ruleId": "3952", "severity": 1, "message": "3953", "line": 13, "column": 13, "nodeType": "3954", "messageId": "3955", "endLine": 13, "endColumn": 16, "suggestions": "4066"}, {"ruleId": "3952", "severity": 1, "message": "3953", "line": 138, "column": 16, "nodeType": "3954", "messageId": "3955", "endLine": 138, "endColumn": 19, "suggestions": "4067", "suppressions": "4068"}, {"ruleId": "3952", "severity": 1, "message": "3953", "line": 150, "column": 14, "nodeType": "3954", "messageId": "3955", "endLine": 150, "endColumn": 17, "suggestions": "4069", "suppressions": "4070"}, {"ruleId": "3952", "severity": 1, "message": "3953", "line": 19, "column": 15, "nodeType": "3954", "messageId": "3955", "endLine": 19, "endColumn": 18, "suggestions": "4071"}, {"ruleId": "3952", "severity": 1, "message": "3953", "line": 42, "column": 44, "nodeType": "3954", "messageId": "3955", "endLine": 42, "endColumn": 47, "suggestions": "4072"}, {"ruleId": "3952", "severity": 1, "message": "3953", "line": 157, "column": 44, "nodeType": "3954", "messageId": "3955", "endLine": 157, "endColumn": 47, "suggestions": "4073"}, {"ruleId": "3952", "severity": 1, "message": "3953", "line": 251, "column": 9, "nodeType": "3954", "messageId": "3955", "endLine": 251, "endColumn": 12, "suggestions": "4074"}, {"ruleId": "3952", "severity": 1, "message": "3953", "line": 35, "column": 27, "nodeType": "3954", "messageId": "3955", "endLine": 35, "endColumn": 30, "suggestions": "4075"}, {"ruleId": "3952", "severity": 1, "message": "3953", "line": 112, "column": 29, "nodeType": "3954", "messageId": "3955", "endLine": 112, "endColumn": 32, "suggestions": "4076"}, {"ruleId": "3952", "severity": 1, "message": "3953", "line": 192, "column": 83, "nodeType": "3954", "messageId": "3955", "endLine": 192, "endColumn": 86, "suggestions": "4077"}, {"ruleId": "3952", "severity": 1, "message": "3953", "line": 238, "column": 74, "nodeType": "3954", "messageId": "3955", "endLine": 238, "endColumn": 77, "suggestions": "4078"}, {"ruleId": "4079", "severity": 1, "message": "4080", "line": 261, "column": 9, "nodeType": "4081", "messageId": "4082", "endLine": 261, "endColumn": 22, "suggestions": "4083"}, {"ruleId": "4079", "severity": 1, "message": "4080", "line": 263, "column": 9, "nodeType": "4081", "messageId": "4082", "endLine": 263, "endColumn": 22, "suggestions": "4084"}, {"ruleId": "3962", "severity": 1, "message": "4085", "line": 5, "column": 29, "nodeType": "3964", "messageId": "3965", "endLine": 5, "endColumn": 44, "suggestions": "4086"}, {"ruleId": "3967", "severity": 1, "message": "4085", "line": 5, "column": 29, "nodeType": null, "messageId": "3965", "endLine": 5, "endColumn": 44}, {"ruleId": "3962", "severity": 1, "message": "4087", "line": 6, "column": 10, "nodeType": "3964", "messageId": "3965", "endLine": 6, "endColumn": 15, "suggestions": "4088"}, {"ruleId": "3967", "severity": 1, "message": "4087", "line": 6, "column": 10, "nodeType": null, "messageId": "3965", "endLine": 6, "endColumn": 15}, {"ruleId": "3962", "severity": 1, "message": "4089", "line": 7, "column": 10, "nodeType": "3964", "messageId": "3965", "endLine": 7, "endColumn": 16, "suggestions": "4090"}, {"ruleId": "3967", "severity": 1, "message": "4089", "line": 7, "column": 10, "nodeType": null, "messageId": "3965", "endLine": 7, "endColumn": 16}, {"ruleId": "3962", "severity": 1, "message": "4091", "line": 3, "column": 20, "nodeType": "3964", "messageId": "3965", "endLine": 3, "endColumn": 29, "suggestions": "4092"}, {"ruleId": "3967", "severity": 1, "message": "4091", "line": 3, "column": 20, "nodeType": null, "messageId": "3965", "endLine": 3, "endColumn": 29}, {"ruleId": "3952", "severity": 1, "message": "3953", "line": 29, "column": 14, "nodeType": "3954", "messageId": "3955", "endLine": 29, "endColumn": 17, "suggestions": "4093"}, {"ruleId": "3962", "severity": 1, "message": "4094", "line": 59, "column": 15, "nodeType": "3964", "messageId": "3965", "endLine": 59, "endColumn": 31, "suggestions": "4095"}, {"ruleId": "3962", "severity": 1, "message": "4094", "line": 60, "column": 19, "nodeType": "3964", "messageId": "3965", "endLine": 60, "endColumn": 35, "suggestions": "4096"}, {"ruleId": "3962", "severity": 1, "message": "4097", "line": 28, "column": 3, "nodeType": "3964", "messageId": "3965", "endLine": 28, "endColumn": 9, "suggestions": "4098"}, {"ruleId": "3967", "severity": 1, "message": "4097", "line": 28, "column": 3, "nodeType": null, "messageId": "3965", "endLine": 28, "endColumn": 9}, {"ruleId": "3962", "severity": 1, "message": "4099", "line": 33, "column": 3, "nodeType": "3964", "messageId": "3965", "endLine": 33, "endColumn": 11, "suggestions": "4100"}, {"ruleId": "3967", "severity": 1, "message": "4099", "line": 33, "column": 3, "nodeType": null, "messageId": "3965", "endLine": 33, "endColumn": 11}, {"ruleId": "3952", "severity": 1, "message": "3953", "line": 47, "column": 56, "nodeType": "3954", "messageId": "3955", "endLine": 47, "endColumn": 59, "suggestions": "4101"}, {"ruleId": "3962", "severity": 1, "message": "4102", "line": 54, "column": 5, "nodeType": "3964", "messageId": "3965", "endLine": 54, "endColumn": 15, "suggestions": "4103"}, {"ruleId": "3967", "severity": 1, "message": "4102", "line": 54, "column": 5, "nodeType": null, "messageId": "3965", "endLine": 54, "endColumn": 15}, {"ruleId": "3952", "severity": 1, "message": "3953", "line": 97, "column": 43, "nodeType": "3954", "messageId": "3955", "endLine": 97, "endColumn": 46, "suggestions": "4104"}, {"ruleId": "3962", "severity": 1, "message": "4105", "line": 108, "column": 14, "nodeType": "3964", "messageId": "3965", "endLine": 108, "endColumn": 19}, {"ruleId": "3967", "severity": 1, "message": "4105", "line": 108, "column": 14, "nodeType": null, "messageId": "3965", "endLine": 108, "endColumn": 19}, {"ruleId": "3962", "severity": 1, "message": "4106", "line": 3, "column": 10, "nodeType": "3964", "messageId": "3965", "endLine": 3, "endColumn": 18, "suggestions": "4107"}, {"ruleId": "3967", "severity": 1, "message": "4106", "line": 3, "column": 10, "nodeType": null, "messageId": "3965", "endLine": 3, "endColumn": 18}, {"ruleId": "3962", "severity": 1, "message": "4108", "line": 16, "column": 3, "nodeType": "3964", "messageId": "3965", "endLine": 16, "endColumn": 10, "suggestions": "4109"}, {"ruleId": "3967", "severity": 1, "message": "4108", "line": 16, "column": 3, "nodeType": null, "messageId": "3965", "endLine": 16, "endColumn": 10}, {"ruleId": "3962", "severity": 1, "message": "4110", "line": 68, "column": 11, "nodeType": "3964", "messageId": "3965", "endLine": 68, "endColumn": 18}, {"ruleId": "3967", "severity": 1, "message": "4110", "line": 68, "column": 11, "nodeType": null, "messageId": "3965", "endLine": 68, "endColumn": 18}, {"ruleId": "3952", "severity": 1, "message": "3953", "line": 26, "column": 14, "nodeType": "3954", "messageId": "3955", "endLine": 26, "endColumn": 17, "suggestions": "4111"}, {"ruleId": "3952", "severity": 1, "message": "3953", "line": 370, "column": 19, "nodeType": "3954", "messageId": "3955", "endLine": 370, "endColumn": 22, "suggestions": "4112"}, {"ruleId": "3952", "severity": 1, "message": "3953", "line": 23, "column": 12, "nodeType": "3954", "messageId": "3955", "endLine": 23, "endColumn": 15, "suggestions": "4113"}, {"ruleId": "3962", "severity": 1, "message": "4114", "line": 407, "column": 5, "nodeType": "3964", "messageId": "3965", "endLine": 407, "endColumn": 25, "suggestions": "4115"}, {"ruleId": "3967", "severity": 1, "message": "4114", "line": 407, "column": 5, "nodeType": null, "messageId": "3965", "endLine": 407, "endColumn": 12}, {"ruleId": "3962", "severity": 1, "message": "4094", "line": 125, "column": 56, "nodeType": "3964", "messageId": "3965", "endLine": 125, "endColumn": 73, "suggestions": "4116"}, {"ruleId": "3967", "severity": 1, "message": "4094", "line": 125, "column": 56, "nodeType": null, "messageId": "3965", "endLine": 125, "endColumn": 64}, {"ruleId": "3962", "severity": 1, "message": "4117", "line": 28, "column": 36, "nodeType": "3964", "messageId": "3965", "endLine": 28, "endColumn": 52, "suggestions": "4118"}, {"ruleId": "3967", "severity": 1, "message": "4117", "line": 28, "column": 36, "nodeType": null, "messageId": "3965", "endLine": 28, "endColumn": 43}, "@typescript-eslint/no-explicit-any", "Unexpected any. Specify a different type.", "TSAnyKeyword", "unexpectedAny", ["4119", "4120"], "react-hooks/exhaustive-deps", "React Hook useEffect has missing dependencies: 'initialBusinessName', 'initialCategory', 'initialCity', 'initialLocality', 'initialPincode', 'performSearch', 'productFilterBy', and 'productSortBy'. Either include them or remove the dependency array.", "ArrayExpression", ["4121"], ["4122"], "no-unused-vars", "'NextRequest' is defined but never used. Allowed unused vars must match /^_/u.", "Identifier", "unusedVar", ["4123"], "@typescript-eslint/no-unused-vars", "'withProtectedRoute' is defined but never used. Allowed unused vars must match /^_/u.", ["4124"], "'AuthContext' is defined but never used. Allowed unused vars must match /^_/u.", ["4125"], "@next/next/no-img-element", "Using `<img>` could result in slower LCP and higher bandwidth. Consider using `<Image />` from `next/image` or a custom image loader to automatically optimize images. This may incur additional usage or cost from your provider. See: https://nextjs.org/docs/messages/no-img-element", "JSXOpeningElement", ["4126"], "React Hook useEffect has a missing dependency: 'loadReviews'. Either include it or remove the dependency array.", ["4127"], ["4128"], ["4129", "4130"], ["4131"], "React Hook useEffect has missing dependencies: 'isSearching', 'performSearch', 'products.length', and 'viewType'. Either include them or remove the dependency array.", ["4132"], ["4133"], ["4134", "4135"], ["4136"], ["4137", "4138"], ["4139"], "'CustomerProfileForReview' is defined but never used. Allowed unused vars must match /^_/u.", ["4140"], ["4141", "4142"], ["4143"], ["4144", "4145"], ["4146"], ["4147", "4148"], ["4149"], ["4150", "4151"], ["4152", "4153"], ["4154", "4155"], ["4156", "4157"], ["4158", "4159"], ["4160", "4161"], ["4162", "4163"], ["4164"], ["4165", "4166"], ["4167"], ["4168", "4169"], ["4170"], "'withPublicRoute' is defined but never used. Allowed unused vars must match /^_/u.", ["4171"], ["4172"], "'name' is defined but never used. Allowed unused args must match /^_/u.", ["4173"], ["4174"], ["4175", "4176"], ["4177", "4178"], ["4179", "4180"], ["4181", "4182"], ["4183"], ["4184"], ["4185"], ["4186"], ["4187", "4188"], ["4189", "4190"], ["4191"], ["4192"], ["4193", "4194"], "'isOwner' is assigned a value but never used. Allowed unused vars must match /^_/u.", ["4195"], ["4196", "4197"], ["4198"], ["4199"], ["4200", "4201"], ["4202", "4203"], ["4204", "4205"], ["4206"], "'updateProductSchema' is assigned a value but never used. Allowed unused vars must match /^_/u.", "'id' is assigned a value but never used. Allowed unused vars must match /^_/u.", ["4207"], ["4208"], ["4209", "4210"], ["4211", "4212"], ["4213", "4214"], ["4215", "4216"], ["4217", "4218"], ["4219", "4220"], ["4221", "4222"], ["4223", "4224"], ["4225", "4226"], ["4227", "4228"], ["4229", "4230"], ["4231", "4232"], ["4233", "4234"], ["4235", "4236"], ["4237", "4238"], ["4239", "4240"], ["4241", "4242"], ["4243", "4244"], ["4245"], "'storageError' is assigned a value but never used. Allowed unused vars must match /^_/u.", ["4246", "4247"], ["4248", "4249"], "prefer-const", "'isBusinessPost' is never reassigned. Use 'const' instead.", "useConst", {"range": "4250", "text": "4251"}, ["4252", "4253"], ["4254", "4255"], ["4256"], ["4257", "4258"], ["4259"], ["4260", "4261"], ["4262", "4263"], ["4264", "4265"], ["4266", "4267"], ["4268", "4269"], ["4270", "4271"], ["4272", "4273"], ["4274", "4275"], "@typescript-eslint/ban-ts-comment", "Use \"@ts-expect-error\" instead of \"@ts-ignore\", as \"@ts-ignore\" will do nothing if the following line is error-free.", "Line", "tsIgnoreInsteadOfExpectError", ["4276"], ["4277"], "'CardDescription' is defined but never used. Allowed unused vars must match /^_/u.", ["4278"], "'Badge' is defined but never used. Allowed unused vars must match /^_/u.", ["4279"], "'Button' is defined but never used. Allowed unused vars must match /^_/u.", ["4280"], "'useEffect' is defined but never used. Allowed unused vars must match /^_/u.", ["4281"], ["4282", "4283"], "'deviceId' is defined but never used. Allowed unused args must match /^_/u.", ["4284"], ["4285"], "'Filter' is defined but never used. Allowed unused vars must match /^_/u.", ["4286"], "'Calendar' is defined but never used. Allowed unused vars must match /^_/u.", ["4287"], ["4288", "4289"], "'pagination' is assigned a value but never used. Allowed unused vars must match /^_/u.", ["4290"], ["4291", "4292"], "'error' is defined but never used. Allowed unused caught errors must match /^_/u.", "'useState' is defined but never used. Allowed unused vars must match /^_/u.", ["4293"], "'XCircle' is defined but never used. Allowed unused vars must match /^_/u.", ["4294"], "'userIds' is assigned a value but never used. Allowed unused vars must match /^_/u.", ["4295", "4296"], ["4297", "4298"], ["4299", "4300"], "'profile' is defined but never used. Allowed unused args must match /^_/u.", ["4301"], ["4302"], "'request' is defined but never used. Allowed unused args must match /^_/u.", ["4303"], {"messageId": "4304", "fix": "4305", "desc": "4306"}, {"messageId": "4307", "fix": "4308", "desc": "4309"}, {"desc": "4310", "fix": "4311"}, {"kind": "4312", "justification": "4313"}, {"messageId": "4314", "data": "4315", "fix": "4316", "desc": "4317"}, {"messageId": "4314", "data": "4318", "fix": "4319", "desc": "4320"}, {"messageId": "4314", "data": "4321", "fix": "4322", "desc": "4323"}, {"kind": "4312", "justification": "4313"}, {"desc": "4324", "fix": "4325"}, {"kind": "4312", "justification": "4313"}, {"messageId": "4304", "fix": "4326", "desc": "4306"}, {"messageId": "4307", "fix": "4327", "desc": "4309"}, {"kind": "4312", "justification": "4313"}, {"desc": "4328", "fix": "4329"}, {"kind": "4312", "justification": "4313"}, {"messageId": "4304", "fix": "4330", "desc": "4306"}, {"messageId": "4307", "fix": "4331", "desc": "4309"}, {"kind": "4312", "justification": "4313"}, {"messageId": "4304", "fix": "4332", "desc": "4306"}, {"messageId": "4307", "fix": "4333", "desc": "4309"}, {"kind": "4312", "justification": "4313"}, {"messageId": "4314", "data": "4334", "fix": "4335", "desc": "4336"}, {"messageId": "4304", "fix": "4337", "desc": "4306"}, {"messageId": "4307", "fix": "4338", "desc": "4309"}, {"kind": "4312", "justification": "4313"}, {"messageId": "4304", "fix": "4339", "desc": "4306"}, {"messageId": "4307", "fix": "4340", "desc": "4309"}, {"kind": "4312", "justification": "4313"}, {"messageId": "4304", "fix": "4341", "desc": "4306"}, {"messageId": "4307", "fix": "4342", "desc": "4309"}, {"kind": "4312", "justification": "4313"}, {"messageId": "4304", "fix": "4343", "desc": "4306"}, {"messageId": "4307", "fix": "4344", "desc": "4309"}, {"messageId": "4304", "fix": "4345", "desc": "4306"}, {"messageId": "4307", "fix": "4346", "desc": "4309"}, {"messageId": "4304", "fix": "4347", "desc": "4306"}, {"messageId": "4307", "fix": "4348", "desc": "4309"}, {"messageId": "4304", "fix": "4349", "desc": "4306"}, {"messageId": "4307", "fix": "4350", "desc": "4309"}, {"messageId": "4304", "fix": "4351", "desc": "4306"}, {"messageId": "4307", "fix": "4352", "desc": "4309"}, {"messageId": "4304", "fix": "4353", "desc": "4306"}, {"messageId": "4307", "fix": "4354", "desc": "4309"}, {"messageId": "4304", "fix": "4355", "desc": "4306"}, {"messageId": "4307", "fix": "4356", "desc": "4309"}, {"kind": "4312", "justification": "4313"}, {"messageId": "4304", "fix": "4357", "desc": "4306"}, {"messageId": "4307", "fix": "4358", "desc": "4309"}, {"kind": "4312", "justification": "4313"}, {"messageId": "4304", "fix": "4359", "desc": "4306"}, {"messageId": "4307", "fix": "4360", "desc": "4309"}, {"kind": "4312", "justification": "4313"}, {"messageId": "4314", "data": "4361", "fix": "4362", "desc": "4363"}, {"messageId": "4314", "data": "4364", "fix": "4365", "desc": "4323"}, {"messageId": "4314", "data": "4366", "fix": "4367", "desc": "4368"}, {"messageId": "4314", "data": "4369", "fix": "4370", "desc": "4368"}, {"messageId": "4304", "fix": "4371", "desc": "4306"}, {"messageId": "4307", "fix": "4372", "desc": "4309"}, {"messageId": "4304", "fix": "4373", "desc": "4306"}, {"messageId": "4307", "fix": "4374", "desc": "4309"}, {"messageId": "4304", "fix": "4375", "desc": "4306"}, {"messageId": "4307", "fix": "4376", "desc": "4309"}, {"messageId": "4304", "fix": "4377", "desc": "4306"}, {"messageId": "4307", "fix": "4378", "desc": "4309"}, {"messageId": "4314", "data": "4379", "fix": "4380", "desc": "4363"}, {"messageId": "4314", "data": "4381", "fix": "4382", "desc": "4323"}, {"messageId": "4314", "data": "4383", "fix": "4384", "desc": "4363"}, {"messageId": "4314", "data": "4385", "fix": "4386", "desc": "4323"}, {"messageId": "4304", "fix": "4387", "desc": "4306"}, {"messageId": "4307", "fix": "4388", "desc": "4309"}, {"messageId": "4304", "fix": "4389", "desc": "4306"}, {"messageId": "4307", "fix": "4390", "desc": "4309"}, {"messageId": "4314", "data": "4391", "fix": "4392", "desc": "4363"}, {"messageId": "4314", "data": "4393", "fix": "4394", "desc": "4323"}, {"messageId": "4304", "fix": "4395", "desc": "4306"}, {"messageId": "4307", "fix": "4396", "desc": "4309"}, {"messageId": "4314", "data": "4397", "fix": "4398", "desc": "4399"}, {"messageId": "4304", "fix": "4400", "desc": "4306"}, {"messageId": "4307", "fix": "4401", "desc": "4309"}, {"messageId": "4314", "data": "4402", "fix": "4403", "desc": "4363"}, {"messageId": "4314", "data": "4404", "fix": "4405", "desc": "4323"}, {"messageId": "4304", "fix": "4406", "desc": "4306"}, {"messageId": "4307", "fix": "4407", "desc": "4309"}, {"messageId": "4304", "fix": "4408", "desc": "4306"}, {"messageId": "4307", "fix": "4409", "desc": "4309"}, {"messageId": "4304", "fix": "4410", "desc": "4306"}, {"messageId": "4307", "fix": "4411", "desc": "4309"}, {"messageId": "4314", "data": "4412", "fix": "4413", "desc": "4320"}, {"messageId": "4314", "data": "4414", "fix": "4415", "desc": "4416"}, {"messageId": "4314", "data": "4417", "fix": "4418", "desc": "4416"}, {"messageId": "4304", "fix": "4419", "desc": "4306"}, {"messageId": "4307", "fix": "4420", "desc": "4309"}, {"messageId": "4304", "fix": "4421", "desc": "4306"}, {"messageId": "4307", "fix": "4422", "desc": "4309"}, {"messageId": "4304", "fix": "4423", "desc": "4306"}, {"messageId": "4307", "fix": "4424", "desc": "4309"}, {"messageId": "4304", "fix": "4425", "desc": "4306"}, {"messageId": "4307", "fix": "4426", "desc": "4309"}, {"messageId": "4304", "fix": "4427", "desc": "4306"}, {"messageId": "4307", "fix": "4428", "desc": "4309"}, {"messageId": "4304", "fix": "4429", "desc": "4306"}, {"messageId": "4307", "fix": "4430", "desc": "4309"}, {"messageId": "4304", "fix": "4431", "desc": "4306"}, {"messageId": "4307", "fix": "4432", "desc": "4309"}, {"messageId": "4304", "fix": "4433", "desc": "4306"}, {"messageId": "4307", "fix": "4434", "desc": "4309"}, {"messageId": "4304", "fix": "4435", "desc": "4306"}, {"messageId": "4307", "fix": "4436", "desc": "4309"}, {"messageId": "4304", "fix": "4437", "desc": "4306"}, {"messageId": "4307", "fix": "4438", "desc": "4309"}, {"messageId": "4304", "fix": "4439", "desc": "4306"}, {"messageId": "4307", "fix": "4440", "desc": "4309"}, {"messageId": "4304", "fix": "4441", "desc": "4306"}, {"messageId": "4307", "fix": "4442", "desc": "4309"}, {"messageId": "4304", "fix": "4443", "desc": "4306"}, {"messageId": "4307", "fix": "4444", "desc": "4309"}, {"messageId": "4304", "fix": "4445", "desc": "4306"}, {"messageId": "4307", "fix": "4446", "desc": "4309"}, {"messageId": "4304", "fix": "4447", "desc": "4306"}, {"messageId": "4307", "fix": "4448", "desc": "4309"}, {"messageId": "4304", "fix": "4449", "desc": "4306"}, {"messageId": "4307", "fix": "4450", "desc": "4309"}, {"messageId": "4304", "fix": "4451", "desc": "4306"}, {"messageId": "4307", "fix": "4452", "desc": "4309"}, {"messageId": "4304", "fix": "4453", "desc": "4306"}, {"messageId": "4307", "fix": "4454", "desc": "4309"}, {"messageId": "4314", "data": "4455", "fix": "4456", "desc": "4363"}, {"messageId": "4304", "fix": "4457", "desc": "4306"}, {"messageId": "4307", "fix": "4458", "desc": "4309"}, {"messageId": "4304", "fix": "4459", "desc": "4306"}, {"messageId": "4307", "fix": "4460", "desc": "4309"}, [6794, 6850], "const isBusinessPost = !businessPostError && businessPost;", {"messageId": "4304", "fix": "4461", "desc": "4306"}, {"messageId": "4307", "fix": "4462", "desc": "4309"}, {"messageId": "4304", "fix": "4463", "desc": "4306"}, {"messageId": "4307", "fix": "4464", "desc": "4309"}, {"kind": "4312", "justification": "4313"}, {"messageId": "4304", "fix": "4465", "desc": "4306"}, {"messageId": "4307", "fix": "4466", "desc": "4309"}, {"kind": "4312", "justification": "4313"}, {"messageId": "4304", "fix": "4467", "desc": "4306"}, {"messageId": "4307", "fix": "4468", "desc": "4309"}, {"messageId": "4304", "fix": "4469", "desc": "4306"}, {"messageId": "4307", "fix": "4470", "desc": "4309"}, {"messageId": "4304", "fix": "4471", "desc": "4306"}, {"messageId": "4307", "fix": "4472", "desc": "4309"}, {"messageId": "4304", "fix": "4473", "desc": "4306"}, {"messageId": "4307", "fix": "4474", "desc": "4309"}, {"messageId": "4304", "fix": "4475", "desc": "4306"}, {"messageId": "4307", "fix": "4476", "desc": "4309"}, {"messageId": "4304", "fix": "4477", "desc": "4306"}, {"messageId": "4307", "fix": "4478", "desc": "4309"}, {"messageId": "4304", "fix": "4479", "desc": "4306"}, {"messageId": "4307", "fix": "4480", "desc": "4309"}, {"messageId": "4304", "fix": "4481", "desc": "4306"}, {"messageId": "4307", "fix": "4482", "desc": "4309"}, {"messageId": "4483", "fix": "4484", "desc": "4485"}, {"messageId": "4483", "fix": "4486", "desc": "4485"}, {"messageId": "4314", "data": "4487", "fix": "4488", "desc": "4489"}, {"messageId": "4314", "data": "4490", "fix": "4491", "desc": "4492"}, {"messageId": "4314", "data": "4493", "fix": "4494", "desc": "4495"}, {"messageId": "4314", "data": "4496", "fix": "4497", "desc": "4498"}, {"messageId": "4304", "fix": "4499", "desc": "4306"}, {"messageId": "4307", "fix": "4500", "desc": "4309"}, {"messageId": "4314", "data": "4501", "fix": "4502", "desc": "4503"}, {"messageId": "4314", "data": "4504", "fix": "4505", "desc": "4503"}, {"messageId": "4314", "data": "4506", "fix": "4507", "desc": "4508"}, {"messageId": "4314", "data": "4509", "fix": "4510", "desc": "4511"}, {"messageId": "4304", "fix": "4512", "desc": "4306"}, {"messageId": "4307", "fix": "4513", "desc": "4309"}, {"messageId": "4314", "data": "4514", "fix": "4515", "desc": "4516"}, {"messageId": "4304", "fix": "4517", "desc": "4306"}, {"messageId": "4307", "fix": "4518", "desc": "4309"}, {"messageId": "4314", "data": "4519", "fix": "4520", "desc": "4521"}, {"messageId": "4314", "data": "4522", "fix": "4523", "desc": "4524"}, {"messageId": "4304", "fix": "4525", "desc": "4306"}, {"messageId": "4307", "fix": "4526", "desc": "4309"}, {"messageId": "4304", "fix": "4527", "desc": "4306"}, {"messageId": "4307", "fix": "4528", "desc": "4309"}, {"messageId": "4304", "fix": "4529", "desc": "4306"}, {"messageId": "4307", "fix": "4530", "desc": "4309"}, {"messageId": "4314", "data": "4531", "fix": "4532", "desc": "4533"}, {"messageId": "4314", "data": "4534", "fix": "4535", "desc": "4503"}, {"messageId": "4314", "data": "4536", "fix": "4537", "desc": "4538"}, "suggestUnknown", {"range": "4539", "text": "4540"}, "Use `unknown` instead, this will force you to explicitly, and safely assert the type is correct.", "suggestNever", {"range": "4541", "text": "4542"}, "Use `never` instead, this is useful when instantiating generic type parameters that you don't need to know the type of.", "Update the dependencies array to be: [initialBusinessName, initialCategory, initialCity, initialLocality, initialPincode, performSearch, productFilterBy, productSortBy]", {"range": "4543", "text": "4544"}, "directive", "", "removeVar", {"varName": "4545"}, {"range": "4546", "text": "4313"}, "Remove unused variable 'NextRequest'.", {"varName": "4547"}, {"range": "4548", "text": "4313"}, "Remove unused variable 'withProtectedRoute'.", {"varName": "4549"}, {"range": "4550", "text": "4313"}, "Remove unused variable 'AuthContext'.", "Update the dependencies array to be: [currentPage, sortBy, businessProfileId, loadReviews]", {"range": "4551", "text": "4552"}, {"range": "4553", "text": "4540"}, {"range": "4554", "text": "4542"}, "Update the dependencies array to be: [isSearching, performSearch, products.length, viewType]", {"range": "4555", "text": "4556"}, {"range": "4557", "text": "4540"}, {"range": "4558", "text": "4542"}, {"range": "4559", "text": "4540"}, {"range": "4560", "text": "4542"}, {"varName": "4561"}, {"range": "4562", "text": "4313"}, "Remove unused variable 'CustomerProfileForReview'.", {"range": "4563", "text": "4540"}, {"range": "4564", "text": "4542"}, {"range": "4565", "text": "4540"}, {"range": "4566", "text": "4542"}, {"range": "4567", "text": "4540"}, {"range": "4568", "text": "4542"}, {"range": "4569", "text": "4540"}, {"range": "4570", "text": "4542"}, {"range": "4571", "text": "4540"}, {"range": "4572", "text": "4542"}, {"range": "4573", "text": "4540"}, {"range": "4574", "text": "4542"}, {"range": "4575", "text": "4540"}, {"range": "4576", "text": "4542"}, {"range": "4577", "text": "4540"}, {"range": "4578", "text": "4542"}, {"range": "4579", "text": "4540"}, {"range": "4580", "text": "4542"}, {"range": "4581", "text": "4540"}, {"range": "4582", "text": "4542"}, {"range": "4583", "text": "4540"}, {"range": "4584", "text": "4542"}, {"range": "4585", "text": "4540"}, {"range": "4586", "text": "4542"}, {"varName": "4587"}, {"range": "4588", "text": "4313"}, "Remove unused variable 'withPublicRoute'.", {"varName": "4549"}, {"range": "4589", "text": "4313"}, {"varName": "4590"}, {"range": "4591", "text": "4313"}, "Remove unused variable 'name'.", {"varName": "4590"}, {"range": "4592", "text": "4313"}, {"range": "4593", "text": "4540"}, {"range": "4594", "text": "4542"}, {"range": "4595", "text": "4540"}, {"range": "4596", "text": "4542"}, {"range": "4597", "text": "4540"}, {"range": "4598", "text": "4542"}, {"range": "4599", "text": "4540"}, {"range": "4600", "text": "4542"}, {"varName": "4587"}, {"range": "4601", "text": "4313"}, {"varName": "4549"}, {"range": "4602", "text": "4313"}, {"varName": "4587"}, {"range": "4603", "text": "4313"}, {"varName": "4549"}, {"range": "4604", "text": "4313"}, {"range": "4605", "text": "4540"}, {"range": "4606", "text": "4542"}, {"range": "4607", "text": "4540"}, {"range": "4608", "text": "4542"}, {"varName": "4587"}, {"range": "4609", "text": "4313"}, {"varName": "4549"}, {"range": "4610", "text": "4313"}, {"range": "4611", "text": "4540"}, {"range": "4612", "text": "4542"}, {"varName": "4613"}, {"range": "4614", "text": "4313"}, "Remove unused variable 'isOwner'.", {"range": "4615", "text": "4540"}, {"range": "4616", "text": "4542"}, {"varName": "4587"}, {"range": "4617", "text": "4313"}, {"varName": "4549"}, {"range": "4618", "text": "4313"}, {"range": "4619", "text": "4540"}, {"range": "4620", "text": "4542"}, {"range": "4621", "text": "4540"}, {"range": "4622", "text": "4542"}, {"range": "4623", "text": "4540"}, {"range": "4624", "text": "4542"}, {"varName": "4547"}, {"range": "4625", "text": "4313"}, {"varName": "4626"}, {"range": "4627", "text": "4313"}, "Remove unused variable 'id'.", {"varName": "4626"}, {"range": "4628", "text": "4313"}, {"range": "4629", "text": "4540"}, {"range": "4630", "text": "4542"}, {"range": "4631", "text": "4540"}, {"range": "4632", "text": "4542"}, {"range": "4633", "text": "4540"}, {"range": "4634", "text": "4542"}, {"range": "4635", "text": "4540"}, {"range": "4636", "text": "4542"}, {"range": "4637", "text": "4540"}, {"range": "4638", "text": "4542"}, {"range": "4639", "text": "4540"}, {"range": "4640", "text": "4542"}, {"range": "4641", "text": "4540"}, {"range": "4642", "text": "4542"}, {"range": "4643", "text": "4540"}, {"range": "4644", "text": "4542"}, {"range": "4645", "text": "4540"}, {"range": "4646", "text": "4542"}, {"range": "4647", "text": "4540"}, {"range": "4648", "text": "4542"}, {"range": "4649", "text": "4540"}, {"range": "4650", "text": "4542"}, {"range": "4651", "text": "4540"}, {"range": "4652", "text": "4542"}, {"range": "4653", "text": "4540"}, {"range": "4654", "text": "4542"}, {"range": "4655", "text": "4540"}, {"range": "4656", "text": "4542"}, {"range": "4657", "text": "4540"}, {"range": "4658", "text": "4542"}, {"range": "4659", "text": "4540"}, {"range": "4660", "text": "4542"}, {"range": "4661", "text": "4540"}, {"range": "4662", "text": "4542"}, {"range": "4663", "text": "4540"}, {"range": "4664", "text": "4542"}, {"varName": "4587"}, {"range": "4665", "text": "4313"}, {"range": "4666", "text": "4540"}, {"range": "4667", "text": "4542"}, {"range": "4668", "text": "4540"}, {"range": "4669", "text": "4542"}, {"range": "4670", "text": "4540"}, {"range": "4671", "text": "4542"}, {"range": "4672", "text": "4540"}, {"range": "4673", "text": "4542"}, {"range": "4674", "text": "4540"}, {"range": "4675", "text": "4542"}, {"range": "4676", "text": "4540"}, {"range": "4677", "text": "4542"}, {"range": "4678", "text": "4540"}, {"range": "4679", "text": "4542"}, {"range": "4680", "text": "4540"}, {"range": "4681", "text": "4542"}, {"range": "4682", "text": "4540"}, {"range": "4683", "text": "4542"}, {"range": "4684", "text": "4540"}, {"range": "4685", "text": "4542"}, {"range": "4686", "text": "4540"}, {"range": "4687", "text": "4542"}, {"range": "4688", "text": "4540"}, {"range": "4689", "text": "4542"}, {"range": "4690", "text": "4540"}, {"range": "4691", "text": "4542"}, "replaceTsIgnoreWithTsExpectError", {"range": "4692", "text": "4693"}, "Replace \"@ts-ignore\" with \"@ts-expect-error\".", {"range": "4694", "text": "4693"}, {"varName": "4695"}, {"range": "4696", "text": "4313"}, "Remove unused variable 'CardDescription'.", {"varName": "4697"}, {"range": "4698", "text": "4313"}, "Remove unused variable 'Badge'.", {"varName": "4699"}, {"range": "4700", "text": "4313"}, "Remove unused variable 'Button'.", {"varName": "4701"}, {"range": "4702", "text": "4313"}, "Remove unused variable 'useEffect'.", {"range": "4703", "text": "4540"}, {"range": "4704", "text": "4542"}, {"varName": "4705"}, {"range": "4706", "text": "4313"}, "Remove unused variable 'deviceId'.", {"varName": "4705"}, {"range": "4707", "text": "4313"}, {"varName": "4708"}, {"range": "4709", "text": "4313"}, "Remove unused variable 'Filter'.", {"varName": "4710"}, {"range": "4711", "text": "4313"}, "Remove unused variable 'Calendar'.", {"range": "4712", "text": "4540"}, {"range": "4713", "text": "4542"}, {"varName": "4714"}, {"range": "4715", "text": "4313"}, "Remove unused variable 'pagination'.", {"range": "4716", "text": "4540"}, {"range": "4717", "text": "4542"}, {"varName": "4718"}, {"range": "4719", "text": "4313"}, "Remove unused variable 'useState'.", {"varName": "4720"}, {"range": "4721", "text": "4313"}, "Remove unused variable 'XCircle'.", {"range": "4722", "text": "4540"}, {"range": "4723", "text": "4542"}, {"range": "4724", "text": "4540"}, {"range": "4725", "text": "4542"}, {"range": "4726", "text": "4540"}, {"range": "4727", "text": "4542"}, {"varName": "4728"}, {"range": "4729", "text": "4313"}, "Remove unused variable 'profile'.", {"varName": "4705"}, {"range": "4730", "text": "4313"}, {"varName": "4731"}, {"range": "4732", "text": "4313"}, "Remove unused variable 'request'.", [821, 824], "unknown", [821, 824], "never", [9509, 9511], "[initialBusinessName, initialCategory, initialCity, initialLocality, initialPincode, performSearch, productFilterBy, productSortBy]", "NextRequest", [9, 21], "withProtectedRoute", [58, 128], "AuthContext", [130, 186], [1800, 1840], "[currentPage, sortBy, businessProfileId, loadReviews]", [4444, 4447], [4444, 4447], [5544, 5546], "[isSearching, performSearch, products.length, viewType]", [394, 397], [394, 397], [424, 427], [424, 427], "CustomerProfileForReview", [1689, 1713], [170, 173], [170, 173], [362, 365], [362, 365], [1622, 1625], [1622, 1625], [4205, 4208], [4205, 4208], [6931, 6934], [6931, 6934], [8815, 8818], [8815, 8818], [11466, 11469], [11466, 11469], [13934, 13937], [13934, 13937], [19338, 19341], [19338, 19341], [2707, 2710], [2707, 2710], [6809, 6812], [6809, 6812], [12115, 12118], [12115, 12118], "withPublicRoute", [304, 371], [373, 429], "name", [1889, 1901], [3127, 3139], [3053, 3056], [3053, 3056], [3179, 3182], [3179, 3182], [3305, 3308], [3305, 3308], [626, 629], [626, 629], [155, 222], [223, 279], [313, 380], [381, 437], [7191, 7194], [7191, 7194], [4152, 4155], [4152, 4155], [57, 124], [125, 181], [2299, 2302], [2299, 2302], "isOwner", [2790, 2896], [1737, 1740], [1737, 1740], [57, 124], [125, 181], [2524, 2527], [2524, 2527], [3400, 3403], [3400, 3403], [6951, 6954], [6951, 6954], [93, 112], "id", [3080, 3108], [3894, 3922], [473, 476], [473, 476], [899, 902], [899, 902], [2936, 2939], [2936, 2939], [3146, 3149], [3146, 3149], [3176, 3179], [3176, 3179], [3441, 3444], [3441, 3444], [3471, 3474], [3471, 3474], [3733, 3736], [3733, 3736], [3763, 3766], [3763, 3766], [4029, 4032], [4029, 4032], [5175, 5178], [5175, 5178], [5467, 5470], [5467, 5470], [5764, 5767], [5764, 5767], [6029, 6032], [6029, 6032], [6064, 6067], [6064, 6067], [6317, 6320], [6317, 6320], [6352, 6355], [6352, 6355], [6608, 6611], [6608, 6611], [86, 105], [3226, 3229], [3226, 3229], [3787, 3790], [3787, 3790], [397, 400], [397, 400], [4231, 4234], [4231, 4234], [4487, 4490], [4487, 4490], [516, 519], [516, 519], [1255, 1258], [1255, 1258], [4663, 4666], [4663, 4666], [7298, 7301], [7298, 7301], [928, 931], [928, 931], [3504, 3507], [3504, 3507], [5746, 5749], [5746, 5749], [7263, 7266], [7263, 7266], [7932, 7945], "// @ts-expect-error", [8042, 8055], "CardDescription", [156, 173], "Badge", [228, 274], "<PERSON><PERSON>", [275, 323], "useEffect", [32, 43], [908, 911], [908, 911], "deviceId", [1611, 1627], [1656, 1672], "Filter", [700, 710], "Calendar", [765, 777], [1238, 1241], [1238, 1241], "pagination", [1361, 1377], [2782, 2785], [2782, 2785], "useState", [15, 48], "XCircle", [424, 435], [881, 884], [881, 884], [11656, 11659], [11656, 11659], [618, 621], [618, 621], "profile", [13331, 13358], [3759, 3778], "request", [753, 769]]