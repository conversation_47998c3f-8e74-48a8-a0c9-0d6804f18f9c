# API Layer Implementation

## 3.1 Enhanced Route Protection Middleware

```typescript
// lib/middleware/enhancedSecurity.ts
export interface SecurityConfig {
  authentication: {
    requireHMAC: boolean;      // Mobile HMAC required
    requireCSRF: boolean;      // Web CSRF required
    requireJWT: boolean;       // JWT token required
    adminOnly: boolean;        // Service key required
  };
  authorization: {
    allowedRoles: UserRole[];
    requiredPermissions: string[];
    resourceOwnership: boolean; // Check resource ownership
  };
  rateLimiting: {
    tier: "low" | "medium" | "high";
    customLimit?: RateLimitConfig;
  };
  validation: {
    strictMode: boolean;       // Strict input validation
    sanitization: boolean;     // Auto-sanitize inputs
  };
}

export function withEnhancedSecurity(
  handler: APIHandler,
  config: SecurityConfig
) {
  return async (req: NextRequest, context: RequestContext) => {
    // 1. Rate Limiting Check
    await enforceRateLimit(req, config.rateLimiting);
    
    // 2. Platform Detection & Authentication
    const authContext = await authenticateRequest(req, config.authentication);
    
    // 3. Authorization Checks
    await authorizeRequest(authContext, config.authorization);
    
    // 4. Input Validation & Sanitization
    const validatedInput = await validateRequest(req, config.validation);
    
    // 5. Execute Business Logic
    return handler(req, authContext, validatedInput);
  };
}
```

## 3.2 Unified Authentication Context

```typescript
// lib/auth/context.ts
export interface AuthContext {
  // User Information
  user: {
    id: string;
    email: string;
    role: "business" | "customer";
    verified: boolean;
  };
  
  // Platform Context
  platform: {
    type: "web" | "mobile";
    device_id?: string;
    user_agent: string;
    ip_address: string;
  };
  
  // Authorization
  permissions: string[];
  planId: string;
  isAdmin: boolean;
  
  // Database Access
  supabase: SupabaseClient;
  adminSupabase?: SupabaseClient; // Service role for admin operations
  
  // Security Metadata
  security: {
    hmacVerified: boolean;
    csrfVerified: boolean;
    rateLimitInfo: RateLimitInfo;
    sessionId: string;
  };
}

export async function getAuthContext(req: NextRequest): Promise<AuthContext> {
  const platform = detectPlatform(req);
  
  if (platform.type === "mobile") {
    return await getMobileAuthContext(req);
  } else {
    return await getWebAuthContext(req);
  }
}
```

## 3.3 Business Logic Centralization

```typescript
// lib/services/businessLogic.ts
export class BusinessLogicService {
  constructor(private context: AuthContext) {}

  // User Profile Management
  async updateUserProfile(data: UserProfileUpdate): Promise<UserProfile> {
    // 1. Validate input data
    const validatedData = UserProfileSchema.parse(data);
    
    // 2. Check permissions
    await this.checkProfileUpdatePermissions(validatedData);
    
    // 3. Apply business rules
    const processedData = await this.applyProfileBusinessRules(validatedData);
    
    // 4. Update database
    const result = await this.updateProfileInDatabase(processedData);
    
    // 5. Invalidate relevant caches
    await this.invalidateProfileCaches(this.context.user.id);
    
    // 6. Trigger side effects
    await this.triggerProfileUpdateSideEffects(result);
    
    return result;
  }

  // Product Management
  async createProduct(productData: ProductCreate): Promise<Product> {
    // Business rule validation
    await this.validateProductLimits();
    await this.validatePlanFeatures("product_creation");
    
    // Data processing
    const processedProduct = await this.processProductData(productData);
    
    // Database operations
    const product = await this.createProductInDatabase(processedProduct);
    
    // Cache management
    await this.invalidateBusinessCaches();
    
    return product;
  }

  // Social Interactions
  async toggleLike(targetId: string, targetType: "post" | "comment"): Promise<LikeResult> {
    // Check existing like
    const existingLike = await this.findExistingLike(targetId, targetType);
    
    if (existingLike) {
      await this.removeLike(existingLike.id);
      await this.decrementLikeCount(targetId, targetType);
      return { action: "removed", count: await this.getLikeCount(targetId, targetType) };
    } else {
      await this.createLike(targetId, targetType);
      await this.incrementLikeCount(targetId, targetType);
      return { action: "added", count: await this.getLikeCount(targetId, targetType) };
    }
  }
}
```
