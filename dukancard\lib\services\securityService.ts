import { createServiceRoleClient } from '@/utils/supabase/service-role';
import { incrementSecurityCounter, storeSecurityEvent } from '@/lib/utils/redis';
import { TABLES } from '@/lib/supabase/constants';

/**
 * Security event types for monitoring and analysis
 */
export type SecurityEventType = 
  | 'hmac_validation_failed'
  | 'timestamp_expired' 
  | 'replay_attack'
  | 'invalid_signature'
  | 'device_not_found'
  | 'device_revoked'
  | 'missing_headers'
  | 'malformed_request';

/**
 * Security event severity levels
 */
export type SecuritySeverity = 'low' | 'medium' | 'high' | 'critical';

/**
 * Security event data structure
 */
export interface SecurityEvent {
  event_type: SecurityEventType;
  device_id?: string;
  user_id?: string;
  ip_address: string;
  user_agent: string;
  request_path: string;
  request_method: string;
  timestamp: string;
  details: Record<string, any>;
  severity: SecuritySeverity;
}

/**
 * Security monitoring and logging service
 */
export class SecurityService {
  private supabase = createServiceRoleClient();

  /**
   * Log a security event for monitoring and analysis
   * @param eventData Security event details
   */
  async logSecurityEvent(eventData: SecurityEvent): Promise<void> {
    try {
      // Store in database for persistent logging
      const { error: dbError } = await this.supabase
        .from(TABLES.SECURITY_EVENTS)
        .insert({
          event_type: eventData.event_type,
          device_id: eventData.device_id,
          user_id: eventData.user_id,
          ip_address: eventData.ip_address,
          user_agent: eventData.user_agent,
          request_path: eventData.request_path,
          request_method: eventData.request_method,
          details: eventData.details,
          severity: eventData.severity,
          created_at: new Date().toISOString()
        });

      if (dbError) {
        console.error('Failed to log security event to database:', dbError);
      }

      // Store in Redis for real-time monitoring (24 hours)
      const eventKey = `${Date.now()}_${eventData.event_type}_${eventData.ip_address}`;
      await storeSecurityEvent(eventKey, eventData, 86400);

      // Increment counter for monitoring dashboards
      await incrementSecurityCounter(eventData.event_type);

      // Console log for immediate visibility (structured format)
      console.warn('SECURITY_EVENT', {
        type: eventData.event_type,
        severity: eventData.severity,
        deviceId: eventData.device_id,
        ipAddress: eventData.ip_address,
        path: eventData.request_path,
        method: eventData.request_method,
        details: eventData.details,
        timestamp: eventData.timestamp
      });

      // Trigger alerts for high-severity events
      if (eventData.severity === 'high' || eventData.severity === 'critical') {
        await this.triggerSecurityAlert(eventData);
      }

    } catch (error) {
      // Fallback logging to console if all storage methods fail
      console.error('CRITICAL: Failed to log security event:', error);
      console.warn('SECURITY_EVENT_FALLBACK', eventData);
    }
  }

  /**
   * Log HMAC validation failure
   */
  async logHMACValidationFailure(
    deviceId: string | undefined,
    ipAddress: string,
    userAgent: string,
    requestPath: string,
    requestMethod: string,
    reason: string,
    details: Record<string, any> = {}
  ): Promise<void> {
    await this.logSecurityEvent({
      event_type: 'hmac_validation_failed',
      device_id: deviceId,
      ip_address: ipAddress,
      user_agent: userAgent,
      request_path: requestPath,
      request_method: requestMethod,
      timestamp: new Date().toISOString(),
      severity: this.determineSeverity('hmac_validation_failed', details),
      details: {
        reason,
        ...details
      }
    });
  }

  /**
   * Log timestamp validation failure
   */
  async logTimestampFailure(
    deviceId: string | undefined,
    ipAddress: string,
    userAgent: string,
    requestPath: string,
    requestMethod: string,
    timestamp: string,
    currentTime: number
  ): Promise<void> {
    const timeDiff = Math.abs(currentTime - parseInt(timestamp, 10));
    
    await this.logSecurityEvent({
      event_type: 'timestamp_expired',
      device_id: deviceId,
      ip_address: ipAddress,
      user_agent: userAgent,
      request_path: requestPath,
      request_method: requestMethod,
      timestamp: new Date().toISOString(),
      severity: timeDiff > 300000 ? 'high' : 'medium', // >5 minutes = high
      details: {
        provided_timestamp: timestamp,
        current_timestamp: currentTime,
        time_diff_ms: timeDiff,
        time_diff_minutes: Math.round(timeDiff / 60000)
      }
    });
  }

  /**
   * Log replay attack attempt
   */
  async logReplayAttack(
    deviceId: string,
    ipAddress: string,
    userAgent: string,
    requestPath: string,
    requestMethod: string,
    nonce: string
  ): Promise<void> {
    await this.logSecurityEvent({
      event_type: 'replay_attack',
      device_id: deviceId,
      ip_address: ipAddress,
      user_agent: userAgent,
      request_path: requestPath,
      request_method: requestMethod,
      timestamp: new Date().toISOString(),
      severity: 'high',
      details: {
        nonce,
        message: 'Request nonce has been used before - potential replay attack'
      }
    });
  }

  /**
   * Determine event severity based on type and context
   */
  private determineSeverity(eventType: SecurityEventType, details: Record<string, any>): SecuritySeverity {
    switch (eventType) {
      case 'replay_attack':
        return 'high';
      case 'device_revoked':
      case 'invalid_signature':
        return 'medium';
      case 'timestamp_expired':
        // High severity if timestamp is way off (possible attack)
        const timeDiff = details.time_diff_ms || 0;
        return timeDiff > 300000 ? 'high' : 'medium';
      case 'device_not_found':
        return 'medium';
      case 'missing_headers':
      case 'malformed_request':
        return 'low';
      default:
        return 'medium';
    }
  }

  /**
   * Trigger security alert for high-severity events
   */
  private async triggerSecurityAlert(eventData: SecurityEvent): Promise<void> {
    try {
      // For now, just increment a high-priority counter
      // In production, this could send notifications, webhooks, etc.
      await incrementSecurityCounter(`alert_${eventData.event_type}`, 86400);
      
      console.error('SECURITY_ALERT', {
        message: `High-severity security event detected: ${eventData.event_type}`,
        deviceId: eventData.device_id,
        ipAddress: eventData.ip_address,
        severity: eventData.severity,
        details: eventData.details
      });

    } catch (error) {
      console.error('Failed to trigger security alert:', error);
    }
  }

  /**
   * Get security event statistics for monitoring
   */
  async getSecurityStats(hoursBack: number = 24): Promise<Record<string, any>> {
    try {
      const since = new Date(Date.now() - (hoursBack * 60 * 60 * 1000)).toISOString();
      
      const { data: events, error } = await this.supabase
        .from(TABLES.SECURITY_EVENTS)
        .select('event_type, severity, created_at')
        .gte('created_at', since);

      if (error) {
        console.error('Failed to get security stats:', error);
        return {};
      }

      // Aggregate statistics
      const stats = {
        total_events: events?.length || 0,
        by_type: {},
        by_severity: {},
        recent_events: events?.slice(-10) || []
      };

      events?.forEach(event => {
        // @ts-ignore
        stats.by_type[event.event_type] = (stats.by_type[event.event_type] || 0) + 1;
        // @ts-ignore
        stats.by_severity[event.severity] = (stats.by_severity[event.severity] || 0) + 1;
      });

      return stats;

    } catch (error) {
      console.error('Failed to get security statistics:', error);
      return {};
    }
  }
}

// Export singleton instance
export const securityService = new SecurityService();