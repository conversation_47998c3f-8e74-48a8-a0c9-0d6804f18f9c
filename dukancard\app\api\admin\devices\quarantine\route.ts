import { NextRequest } from 'next/server';
import { createServiceRoleClient } from '@/utils/supabase/service-role';
import { requireAdmin } from '@/lib/utils/adminAuth';

interface QuarantineRequest {
  device_id: string;
  reason?: string;
}

export async function POST(request: NextRequest) {
  // Verify admin access
  const adminUser = await requireAdmin(request);
  if (adminUser instanceof Response) {
    return adminUser; // Return error response if not admin
  }

  try {
    const body: QuarantineRequest = await request.json();
    const { device_id, reason } = body;

    if (!device_id) {
      return Response.json({ error: 'device_id is required' }, { status: 400 });
    }

    const supabase = createServiceRoleClient();
    const timestamp = new Date().toISOString();

    // 1. Get current device info
    const { data: deviceInfo, error: fetchError } = await supabase
      .from('devices')
      .select(`
        device_id,
        user_id,
        device_name,
        platform,
        is_quarantined,
        users!inner(email)
      `)
      .eq('device_id', device_id)
      .single();

    if (fetchError || !deviceInfo) {
      return Response.json({ error: 'Device not found' }, { status: 404 });
    }

    if (deviceInfo.is_quarantined) {
      return Response.json({ error: 'Device is already quarantined' }, { status: 400 });
    }

    // 2. Quarantine the device
    const { error: quarantineError } = await supabase
      .from('devices')
      .update({
        is_quarantined: true,
        quarantined_at: timestamp,
        quarantine_reason: reason || 'Admin quarantine'
      })
      .eq('device_id', device_id);

    if (quarantineError) {
      console.error('Quarantine error:', quarantineError);
      return Response.json({ error: 'Failed to quarantine device' }, { status: 500 });
    }

    // 3. Create security alert
    const { error: alertError } = await supabase
      .from('suspicious_activity_alerts')
      .insert({
        device_id,
        user_id: deviceInfo.user_id,
        alert_type: 'admin_quarantine',
        severity: 'high',
        details: {
          description: 'Device quarantined by administrator',
          reason: reason || 'Admin quarantine',
          quarantined_by: adminUser.email || adminUser.id
        },
        status: 'resolved' // Mark as resolved since admin took action
      });

    if (alertError) {
      console.error('Alert creation error:', alertError);
      // Continue even if alert creation fails
    }

    // 4. Create audit log entry
    const { error: auditError } = await supabase
      .from('device_audit_logs')
      .insert({
        admin_user_id: adminUser.id,
        action_type: 'quarantine',
        target_device_id: device_id,
        target_user_id: deviceInfo.user_id,
        reason: reason || 'Admin quarantine',
        metadata: {
          device_name: deviceInfo.device_name,
          platform: deviceInfo.platform,
          user_email: deviceInfo.users?.email,
          ip_address: request.headers.get('x-forwarded-for') || 'unknown'
        },
        timestamp,
        ip_address: request.headers.get('x-forwarded-for') || 'unknown'
      });

    if (auditError) {
      console.error('Audit log error:', auditError);
      // Continue even if audit log fails
    }

    return Response.json({
      success: true,
      device_id,
      status: 'quarantined',
      quarantined_at: timestamp,
      reason: reason || 'Admin quarantine'
    });

  } catch (error) {
    console.error('Error in device quarantine API:', error);
    return Response.json({ error: 'Internal server error' }, { status: 500 });
  }
}