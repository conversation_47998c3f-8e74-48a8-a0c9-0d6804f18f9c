import { SecurityAnalytics } from '@/lib/services/securityAnalytics';
import { createServiceRoleClient } from '@/utils/supabase/service-role';

jest.mock('@/utils/supabase/service-role');

const mockCreateServiceRoleClient = createServiceRoleClient as jest.MockedFunction<typeof createServiceRoleClient>;

describe('SecurityAnalytics', () => {
  let securityAnalytics: SecurityAnalytics;
  let mockSupabase: any;

  beforeEach(() => {
    mockSupabase = {
      from: jest.fn().mockReturnThis(),
      select: jest.fn().mockReturnThis(),
      insert: jest.fn().mockReturnThis(),
      eq: jest.fn().mockReturnThis(),
      single: jest.fn()
    };

    mockCreateServiceRoleClient.mockReturnValue(mockSupabase);
    securityAnalytics = new SecurityAnalytics();
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('analyzeDeviceActivity', () => {
    it('should return empty array if no recent activity', async () => {
      mockSupabase.single.mockResolvedValueOnce({ data: null, error: { message: 'Not found' } });

      const anomalies = await securityAnalytics.analyzeDeviceActivity('device-123');

      expect(anomalies).toEqual([]);
    });

    it('should detect location anomalies', async () => {
      const deviceData = { user_id: 'user-123', last_seen_at: new Date().toISOString() };
      mockSupabase.single
        .mockResolvedValueOnce({ data: deviceData, error: null })
        .mockResolvedValueOnce({ data: [], error: null });

      // Mock the private method behavior by testing the public interface
      const anomalies = await securityAnalytics.analyzeDeviceActivity('device-123');

      // Should analyze and potentially find anomalies
      expect(Array.isArray(anomalies)).toBe(true);
    });

    it('should handle errors gracefully', async () => {
      mockSupabase.single.mockRejectedValueOnce(new Error('Database error'));

      const anomalies = await securityAnalytics.analyzeDeviceActivity('device-123');

      expect(anomalies).toEqual([]);
    });
  });

  describe('calculateDeviceRiskScore', () => {
    it('should return low risk score for devices with no anomalies', async () => {
      mockSupabase.single.mockResolvedValueOnce({ data: null, error: { message: 'Not found' } });

      const riskScore = await securityAnalytics.calculateDeviceRiskScore('device-123');

      expect(riskScore).toBe(10); // Base score for normal devices
    });

    it('should return default medium risk on error', async () => {
      mockSupabase.single.mockRejectedValueOnce(new Error('Database error'));

      const riskScore = await securityAnalytics.calculateDeviceRiskScore('device-123');

      expect(riskScore).toBe(50);
    });

    it('should calculate higher risk scores for devices with anomalies', async () => {
      const deviceData = { user_id: 'user-123', last_seen_at: new Date().toISOString() };
      mockSupabase.single
        .mockResolvedValueOnce({ data: deviceData, error: null })
        .mockResolvedValueOnce({ data: [], error: null });

      const riskScore = await securityAnalytics.calculateDeviceRiskScore('device-123');

      expect(riskScore).toBeGreaterThanOrEqual(10);
      expect(riskScore).toBeLessThanOrEqual(100);
    });
  });

  describe('createSecurityAlert', () => {
    it('should create security alerts for detected anomalies', async () => {
      mockSupabase.insert.mockResolvedValueOnce({ error: null });

      const anomalies = [
        {
          type: 'location_anomaly' as const,
          severity: 'high' as const,
          confidence: 85,
          details: { unusual_location: 'Tokyo' }
        }
      ];

      await securityAnalytics.createSecurityAlert('device-123', 'user-123', anomalies);

      expect(mockSupabase.from).toHaveBeenCalledWith('suspicious_activity_alerts');
      expect(mockSupabase.insert).toHaveBeenCalledWith(
        expect.objectContaining({
          device_id: 'device-123',
          user_id: 'user-123',
          alert_type: 'location_anomaly',
          severity: 'high',
          status: 'pending'
        })
      );
    });

    it('should handle errors when creating alerts', async () => {
      mockSupabase.insert.mockRejectedValueOnce(new Error('Database error'));

      const anomalies = [
        {
          type: 'location_anomaly' as const,
          severity: 'high' as const,
          confidence: 85,
          details: { unusual_location: 'Tokyo' }
        }
      ];

      // Should not throw error
      await expect(securityAnalytics.createSecurityAlert('device-123', 'user-123', anomalies))
        .resolves.toBeUndefined();
    });
  });

  describe('runAutomatedAnomalyDetection', () => {
    it('should process all active devices', async () => {
      const mockDevices = [
        { device_id: 'device-1', user_id: 'user-1' },
        { device_id: 'device-2', user_id: 'user-2' }
      ];

      mockSupabase.select.mockResolvedValueOnce({ data: mockDevices, error: null });
      mockSupabase.single.mockResolvedValue({ data: null, error: { message: 'Not found' } });
      mockSupabase.insert.mockResolvedValue({ error: null });
      mockSupabase.from('devices').update = jest.fn().mockReturnThis();
      mockSupabase.eq = jest.fn().mockResolvedValue({ error: null });

      await securityAnalytics.runAutomatedAnomalyDetection();

      expect(mockSupabase.from).toHaveBeenCalledWith('devices');
      expect(mockSupabase.select).toHaveBeenCalled();
    });

    it('should handle errors gracefully during automated detection', async () => {
      mockSupabase.select.mockRejectedValueOnce(new Error('Database error'));

      // Should not throw error
      await expect(securityAnalytics.runAutomatedAnomalyDetection())
        .resolves.toBeUndefined();
    });

    it('should continue processing other devices if one fails', async () => {
      const mockDevices = [
        { device_id: 'device-1', user_id: 'user-1' },
        { device_id: 'device-2', user_id: 'user-2' }
      ];

      mockSupabase.select.mockResolvedValueOnce({ data: mockDevices, error: null });
      
      // First device analysis fails, second succeeds
      mockSupabase.single
        .mockRejectedValueOnce(new Error('Analysis error'))
        .mockResolvedValueOnce({ data: null, error: { message: 'Not found' } });

      await securityAnalytics.runAutomatedAnomalyDetection();

      // Should attempt to process both devices
      expect(mockSupabase.select).toHaveBeenCalled();
    });
  });
});