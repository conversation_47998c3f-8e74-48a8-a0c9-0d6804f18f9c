'use client';

import { useState, useEffect } from 'react';
import { <PERSON>alog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  Smartphone, 
  Shield, 
  ShieldOff, 
  Clock, 
  MapPin, 
  AlertTriangle,
  Activity,
  TrendingUp,
  Info,
  AlertCircle,
  XCircle
} from 'lucide-react';

interface DeviceEvent {
  timestamp: string;
  event_type: 'registration' | 'login' | 'logout' | 'revocation' | 'quarantine' | 'risk_update' | 'alert';
  description: string;
  severity?: 'info' | 'warning' | 'critical';
  metadata?: any;
}

interface DeviceDetails {
  device_id: string;
  user_id: string;
  user_email: string;
  device_name: string;
  platform: 'ios' | 'android';
  app_version: string;
  registered_at: string;
  last_activity: string;
  is_revoked: boolean;
  is_quarantined?: boolean;
  risk_score?: number;
  events: DeviceEvent[];
  riskHistory: { date: string; score: number }[];
  locationHistory: { date: string; location: string; ip: string }[];
  analytics: {
    totalSessions: number;
    avgSessionDuration: string;
    lastLocation: string;
    flaggedActivities: number;
  };
}

interface DeviceDetailModalProps {
  device: DeviceDetails | null;
  isOpen: boolean;
  onClose: () => void;
  onRevoke?: (deviceId: string) => void;
  onQuarantine?: (deviceId: string) => void;
}

export default function DeviceDetailModal({ 
  device, 
  isOpen, 
  onClose, 
  onRevoke, 
  onQuarantine 
}: DeviceDetailModalProps) {
  const [loading, setLoading] = useState(false);

  if (!device) return null;

  const handleRevoke = async () => {
    if (!onRevoke) return;
    setLoading(true);
    try {
      await onRevoke(device.device_id);
      onClose();
    } catch (error) {
      console.error('Error revoking device:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleQuarantine = async () => {
    if (!onQuarantine) return;
    setLoading(true);
    try {
      await onQuarantine(device.device_id);
      onClose();
    } catch (error) {
      console.error('Error quarantining device:', error);
    } finally {
      setLoading(false);
    }
  };

  const getEventIcon = (eventType: string, severity?: string) => {
    switch (eventType) {
      case 'registration': return <Smartphone className="h-4 w-4 text-blue-500" />;
      case 'login': return <Shield className="h-4 w-4 text-green-500" />;
      case 'logout': return <Shield className="h-4 w-4 text-gray-500" />;
      case 'revocation': return <ShieldOff className="h-4 w-4 text-red-500" />;
      case 'quarantine': return <AlertTriangle className="h-4 w-4 text-yellow-500" />;
      case 'alert': 
        return severity === 'critical' ? <XCircle className="h-4 w-4 text-red-500" /> :
               severity === 'warning' ? <AlertCircle className="h-4 w-4 text-yellow-500" /> :
               <Info className="h-4 w-4 text-blue-500" />;
      default: return <Activity className="h-4 w-4 text-gray-500" />;
    }
  };

  const getEventColor = (severity?: string) => {
    switch (severity) {
      case 'critical': return 'border-l-red-500 bg-red-50';
      case 'warning': return 'border-l-yellow-500 bg-yellow-50';
      case 'info': return 'border-l-blue-500 bg-blue-50';
      default: return 'border-l-gray-500 bg-gray-50';
    }
  };

  const getRiskBadgeColor = (score?: number) => {
    if (!score) return 'secondary';
    if (score >= 80) return 'destructive';
    if (score >= 50) return 'default';
    return 'secondary';
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-hidden">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Smartphone className="h-5 w-5" />
            {device.device_name || 'Unknown Device'}
          </DialogTitle>
          <DialogDescription>
            Device details and activity timeline for {device.user_email}
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* Device Overview */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">Platform</CardTitle>
              </CardHeader>
              <CardContent>
                <Badge variant="outline" className="capitalize">
                  {device.platform}
                </Badge>
                <p className="text-xs text-muted-foreground mt-1">
                  Version {device.app_version}
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">Status</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex gap-2">
                  {device.is_revoked ? (
                    <Badge variant="destructive">
                      <ShieldOff className="h-3 w-3 mr-1" />
                      Revoked
                    </Badge>
                  ) : device.is_quarantined ? (
                    <Badge variant="default">
                      <AlertTriangle className="h-3 w-3 mr-1" />
                      Quarantined
                    </Badge>
                  ) : (
                    <Badge variant="secondary">
                      <Shield className="h-3 w-3 mr-1" />
                      Active
                    </Badge>
                  )}
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">Risk Score</CardTitle>
              </CardHeader>
              <CardContent>
                {device.risk_score ? (
                  <Badge variant={getRiskBadgeColor(device.risk_score)}>
                    {device.risk_score}/100
                  </Badge>
                ) : (
                  <span className="text-sm text-muted-foreground">Not assessed</span>
                )}
              </CardContent>
            </Card>
          </div>

          {/* Action Buttons */}
          {!device.is_revoked && (
            <div className="flex gap-2">
              <Button 
                variant="destructive" 
                onClick={handleRevoke}
                disabled={loading}
              >
                <ShieldOff className="h-4 w-4 mr-2" />
                Revoke Device
              </Button>
              {!device.is_quarantined && (
                <Button 
                  variant="outline" 
                  onClick={handleQuarantine}
                  disabled={loading}
                >
                  <AlertTriangle className="h-4 w-4 mr-2" />
                  Quarantine
                </Button>
              )}
            </div>
          )}

          {/* Detailed Information Tabs */}
          <Tabs defaultValue="timeline" className="w-full">
            <TabsList className="grid w-full grid-cols-4">
              <TabsTrigger value="timeline">Activity Timeline</TabsTrigger>
              <TabsTrigger value="analytics">Analytics</TabsTrigger>
              <TabsTrigger value="locations">Locations</TabsTrigger>
              <TabsTrigger value="risk">Risk Analysis</TabsTrigger>
            </TabsList>

            <TabsContent value="timeline" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Clock className="h-4 w-4" />
                    Activity Timeline
                  </CardTitle>
                  <CardDescription>
                    Chronological view of all device events and activities
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <ScrollArea className="h-64">
                    <div className="space-y-3">
                      {device.events.map((event, index) => (
                        <div
                          key={index}
                          className={`border-l-4 pl-4 pb-3 ${getEventColor(event.severity)}`}
                        >
                          <div className="flex items-start justify-between">
                            <div className="flex items-center gap-2">
                              {getEventIcon(event.event_type, event.severity)}
                              <div>
                                <p className="text-sm font-medium">
                                  {event.description}
                                </p>
                                <p className="text-xs text-muted-foreground">
                                  {formatDate(event.timestamp)}
                                </p>
                              </div>
                            </div>
                            {event.severity && (
                              <Badge variant="outline" className="text-xs">
                                {event.severity}
                              </Badge>
                            )}
                          </div>
                          {event.metadata && Object.keys(event.metadata).length > 0 && (
                            <div className="mt-2 p-2 bg-white/50 rounded text-xs">
                              <pre className="whitespace-pre-wrap">
                                {JSON.stringify(event.metadata, null, 2)}
                              </pre>
                            </div>
                          )}
                        </div>
                      ))}
                    </div>
                  </ScrollArea>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="analytics" className="space-y-4">
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-sm">Total Sessions</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p className="text-2xl font-bold">{device.analytics.totalSessions}</p>
                  </CardContent>
                </Card>
                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-sm">Avg Duration</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p className="text-2xl font-bold">{device.analytics.avgSessionDuration}</p>
                  </CardContent>
                </Card>
                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-sm">Last Location</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p className="text-sm">{device.analytics.lastLocation}</p>
                  </CardContent>
                </Card>
                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-sm">Flagged Activities</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p className="text-2xl font-bold text-red-600">
                      {device.analytics.flaggedActivities}
                    </p>
                  </CardContent>
                </Card>
              </div>

              <Card>
                <CardHeader>
                  <CardTitle className="text-base">Device Information</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div>
                      <span className="font-medium">Device ID:</span>
                      <p className="font-mono text-xs break-all">{device.device_id}</p>
                    </div>
                    <div>
                      <span className="font-medium">User ID:</span>
                      <p className="font-mono text-xs break-all">{device.user_id}</p>
                    </div>
                    <div>
                      <span className="font-medium">Registered:</span>
                      <p>{formatDate(device.registered_at)}</p>
                    </div>
                    <div>
                      <span className="font-medium">Last Activity:</span>
                      <p>{formatDate(device.last_activity)}</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="locations" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <MapPin className="h-4 w-4" />
                    Location History
                  </CardTitle>
                  <CardDescription>
                    Recent login locations and IP addresses
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <ScrollArea className="h-64">
                    <div className="space-y-2">
                      {device.locationHistory.map((location, index) => (
                        <div key={index} className="flex justify-between items-center p-2 border rounded">
                          <div>
                            <p className="text-sm font-medium">{location.location}</p>
                            <p className="text-xs text-muted-foreground">{location.date}</p>
                          </div>
                          <div className="text-right">
                            <p className="text-xs font-mono">{location.ip}</p>
                          </div>
                        </div>
                      ))}
                    </div>
                  </ScrollArea>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="risk" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <TrendingUp className="h-4 w-4" />
                    Risk Analysis
                  </CardTitle>
                  <CardDescription>
                    Risk score history and contributing factors
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div>
                      <h4 className="font-medium mb-2">Current Risk Score</h4>
                      <div className="flex items-center gap-2">
                        <Badge variant={getRiskBadgeColor(device.risk_score)} className="text-lg px-3 py-1">
                          {device.risk_score || 0}/100
                        </Badge>
                        <span className="text-sm text-muted-foreground">
                          {(device.risk_score || 0) >= 80 ? 'High Risk' : 
                           (device.risk_score || 0) >= 50 ? 'Medium Risk' : 'Low Risk'}
                        </span>
                      </div>
                    </div>

                    <div>
                      <h4 className="font-medium mb-2">Risk Factors</h4>
                      <div className="space-y-2 text-sm">
                        {device.is_quarantined && (
                          <div className="flex items-center gap-2 text-yellow-600">
                            <AlertTriangle className="h-3 w-3" />
                            Device is currently quarantined
                          </div>
                        )}
                        {device.analytics.flaggedActivities > 0 && (
                          <div className="flex items-center gap-2 text-red-600">
                            <AlertCircle className="h-3 w-3" />
                            {device.analytics.flaggedActivities} flagged activities detected
                          </div>
                        )}
                        {device.locationHistory.length > 5 && (
                          <div className="flex items-center gap-2 text-blue-600">
                            <MapPin className="h-3 w-3" />
                            Multiple location access patterns
                          </div>
                        )}
                      </div>
                    </div>

                    {device.riskHistory.length > 0 && (
                      <div>
                        <h4 className="font-medium mb-2">Risk History</h4>
                        <div className="space-y-1">
                          {device.riskHistory.map((history, index) => (
                            <div key={index} className="flex justify-between text-sm">
                              <span>{history.date}</span>
                              <Badge variant="outline">{history.score}/100</Badge>
                            </div>
                          ))}
                        </div>
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </div>
      </DialogContent>
    </Dialog>
  );
}