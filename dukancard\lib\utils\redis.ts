import { Redis } from '@upstash/redis';

// Initialize Redis client
const redis = new Redis({
  url: process.env.UPSTASH_REDIS_REST_URL!,
  token: process.env.UPSTASH_REDIS_REST_TOKEN!,
});

/**
 * Redis utilities for HMAC validation and replay protection
 */

/**
 * Generate a unique nonce for request tracking
 * @param deviceId Device identifier
 * @param timestamp Request timestamp
 * @param signature Request signature (partial for uniqueness)
 * @returns Unique nonce string
 */
export function generateNonce(deviceId: string, timestamp: string, signature: string): string {
  const partialSig = signature.substring(0, 16); // Use first 16 chars for uniqueness
  return `nonce:${deviceId}:${timestamp}:${partialSig}`;
}

/**
 * Check if a nonce has been used (replay attack detection)
 * @param nonce Unique request nonce
 * @returns Promise<boolean> true if nonce has been used, false if new
 */
export async function isNonceUsed(nonce: string): Promise<boolean> {
  try {
    const result = await redis.get(nonce);
    return result !== null;
  } catch (error) {
    console.error('Redis nonce check failed:', error);
    // Fail secure - assume nonce is used if <PERSON><PERSON> is unavailable
    return true;
  }
}

/**
 * Mark a nonce as used to prevent replay attacks
 * @param nonce Unique request nonce
 * @param ttlSeconds Time-to-live in seconds (default: 300 = 5 minutes)
 */
export async function markNonceUsed(nonce: string, ttlSeconds: number = 300): Promise<void> {
  try {
    await redis.setex(nonce, ttlSeconds, '1');
  } catch (error) {
    console.error('Redis nonce marking failed:', error);
    // Non-critical failure - log but don't block request
  }
}

/**
 * Cleanup expired nonces (called by background job)
 * Note: Redis automatically expires keys with TTL, but this provides manual cleanup
 * @param pattern Pattern to match nonces (default: "nonce:*")
 * @returns Number of keys cleaned up
 */
export async function cleanupExpiredNonces(pattern: string = 'nonce:*'): Promise<number> {
  try {
    const keys = await redis.keys(pattern);
    if (keys.length === 0) return 0;
    
    // Redis will automatically expire these, but we can check TTL
    let cleanedCount = 0;
    for (const key of keys) {
      const ttl = await redis.ttl(key);
      if (ttl === -2) { // Key doesn't exist
        cleanedCount++;
      }
    }
    
    return cleanedCount;
  } catch (error) {
    console.error('Redis cleanup failed:', error);
    return 0;
  }
}

/**
 * Get Redis client instance for advanced operations
 */
export function getRedisClient(): Redis {
  return redis;
}

/**
 * Store security event data for analysis
 * @param eventKey Unique event identifier
 * @param eventData Security event data
 * @param ttlSeconds Time-to-live for the event (default: 86400 = 24 hours)
 */
export async function storeSecurityEvent(
  eventKey: string, 
  eventData: object, 
  ttlSeconds: number = 86400
): Promise<void> {
  try {
    await redis.setex(`security:${eventKey}`, ttlSeconds, JSON.stringify(eventData));
  } catch (error) {
    console.error('Failed to store security event:', error);
  }
}

/**
 * Increment security failure counter for monitoring
 * @param counterKey Counter identifier (e.g., "hmac_failures", "timestamp_expired")
 * @param ttlSeconds Window for the counter (default: 3600 = 1 hour)
 * @returns Current counter value
 */
export async function incrementSecurityCounter(
  counterKey: string, 
  ttlSeconds: number = 3600
): Promise<number> {
  try {
    const key = `counter:${counterKey}`;
    const count = await redis.incr(key);
    
    // Set TTL only on first increment
    if (count === 1) {
      await redis.expire(key, ttlSeconds);
    }
    
    return count;
  } catch (error) {
    console.error('Failed to increment security counter:', error);
    return 0;
  }
}

/**
 * Health check for Redis connection
 * @returns Promise<boolean> true if Redis is accessible
 */
export async function checkRedisHealth(): Promise<boolean> {
  try {
    const result = await redis.ping();
    return result === 'PONG';
  } catch (error) {
    console.error('Redis health check failed:', error);
    return false;
  }
}