# Project Brief: DukanCard API Refactoring Initiative

## Executive Summary

**DukanCard API Refactoring Initiative** aims to consolidate all database interactions through secure, centralized API routes, eliminating direct Supabase calls across both web and mobile applications while implementing enterprise-grade security to protect customer and business privacy.

The project addresses the current mixed architecture where business logic is duplicated between web server actions (`lib/actions/`) and mobile direct Supabase calls, creating security vulnerabilities, maintenance overhead, and inconsistent feature parity across platforms. The solution delivers enhanced security through HMAC-authenticated mobile-only API access, reduced technical debt via centralized business logic, privacy protection through controlled API access, and improved maintainability with a single source of truth for all database operations.

## Problem Statement

### Current State and Pain Points

The DukanCard polyrepo architecture currently operates with a fragmented approach:

- **Web Application (dukancard/)**: Uses Next.js server actions in `lib/actions/` for database operations
- **Mobile Application (dukancard-app/)**: Makes direct Supabase client calls, bypassing centralized business logic
- **Security Gaps**: Direct Supabase access from mobile creates potential for reverse engineering and unauthorized data access
- **Code Duplication**: Identical business logic maintained separately across platforms
- **Inconsistent Feature Parity**: Different implementations can lead to behavioral differences between web and mobile

### Impact of the Problem

- **Security Risk**: Social commerce platform handling sensitive customer and business data is vulnerable to external attacks, data scraping, and privacy breaches
- **Development Inefficiency**: Maintaining duplicate business logic increases development time by ~40% and creates higher bug risk
- **Compliance Concerns**: Direct database access makes it difficult to implement consistent privacy controls and audit trails
- **Scalability Issues**: Mixed architecture prevents implementation of centralized rate limiting, caching, and monitoring

### Why Existing Solutions Fall Short

Current architecture lacks:
- Unified authentication and authorization layer
- Centralized business rule enforcement
- Consistent error handling and logging
- Protection against external API abuse
- Standardized response formats

### Urgency and Importance

As a social commerce platform, customer trust is paramount. Recent growth and feature expansion has exposed these architectural weaknesses, making immediate consolidation critical for:
- Maintaining customer confidence in data privacy
- Supporting regulatory compliance requirements
- Enabling rapid feature development without security compromises
- Preventing potential data breaches that could destroy platform reputation

## Proposed Solution

### Core Concept and Approach

Implement a unified API-first architecture where all database operations flow through secure Next.js API routes:

```
Mobile App → HMAC + JWT → API Routes → RLS-Protected Supabase
Web App   → CSRF + JWT → API Routes → RLS-Protected Supabase  
Admin     → Service Key → API Routes → Direct Supabase (bypass RLS)
```

### Key Differentiators

1. **HMAC Security**: Device-specific secrets prevent APK reverse engineering attacks
2. **Centralized Business Logic**: Single source of truth eliminates duplication
3. **Privacy-First Design**: Mobile-only API access prevents external data harvesting
4. **Zustand Caching**: Smart client-side caching reduces API calls and improves performance
5. **RLS Preservation**: Maintain Supabase Row Level Security as backup protection layer

### Why This Solution Will Succeed

- **Builds on Existing Foundation**: Leverages current device registration system and security infrastructure
- **Gradual Migration Path**: Allows phased implementation without breaking existing functionality
- **Performance Optimization**: Zustand stores provide intelligent caching to offset API latency
- **Enterprise Security**: HMAC + JWT provides bank-level authentication security

### High-Level Product Vision

Create the most secure and maintainable social commerce platform architecture that serves as a model for privacy-conscious business discovery and customer engagement.

## Target Users

### Primary User Segment: Development Team

**Profile**: Full-stack developers, mobile developers, and DevOps engineers working on DukanCard platform

**Current Behaviors**: 
- Maintaining duplicate business logic across web and mobile codebases
- Implementing security measures reactively rather than proactively
- Spending significant time debugging platform-specific issues

**Specific Needs**:
- Unified development patterns and APIs
- Consistent error handling and debugging tools
- Reduced cognitive load when implementing features
- Clear security guidelines and enforcement

**Goals**: 
- Faster feature development cycles
- Reduced bug rates and maintenance overhead
- Confidence in security implementation
- Easier onboarding of new team members

### Secondary User Segment: Platform Users (Businesses & Customers)

**Profile**: Businesses creating digital storefronts and customers discovering local businesses

**Current Behaviors**:
- Expecting consistent experience across web and mobile platforms
- Increasing concern about data privacy and security
- Relying on platform reliability for business operations

**Specific Needs**:
- Fast, reliable platform performance
- Consistent features across all access points
- Trust in data privacy and security
- Seamless user experience

**Goals**:
- Secure business operations and customer interactions
- Reliable platform availability
- Consistent feature access regardless of device

## Goals & Success Metrics

### Business Objectives

- **Reduce Security Vulnerabilities**: Eliminate external API access within 3 months, achieving zero unauthorized data access attempts
- **Decrease Development Time**: Reduce feature development time by 35% through centralized business logic
- **Improve Code Quality**: Achieve 95% test coverage across all API routes and eliminate code duplication
- **Enhance Platform Trust**: Implement comprehensive audit logging and privacy controls for regulatory compliance

### User Success Metrics

- **Developer Productivity**: Reduce time to implement new features by 40% 
- **Platform Reliability**: Maintain 99.9% uptime with consistent behavior across web and mobile
- **Security Confidence**: Zero security incidents related to API access within 6 months
- **Performance Consistency**: Ensure web and mobile response times differ by less than 100ms

### Key Performance Indicators (KPIs)

- **API Security**: 100% of mobile requests authenticated via HMAC within 6 weeks
- **Code Consolidation**: Eliminate 80% of duplicate business logic within 8 weeks
- **Test Coverage**: Achieve 95% unit test coverage for all API routes within 10 weeks
- **Performance Impact**: Maintain sub-200ms response times for 95% of API calls
- **Migration Progress**: Complete 100% of Supabase call elimination within 12 weeks

## MVP Scope

### Core Features (Must Have)

- **Security Infrastructure**: Complete HMAC authentication enforcement for all mobile API calls with device-specific secret management
- **Authentication APIs**: Unified login, registration, and device management endpoints replacing direct Supabase auth calls
- **User Profile APIs**: Centralized business and customer profile management with consistent validation and business rules
- **Content Management APIs**: Post creation, editing, and deletion with media upload handling and social interaction tracking
- **Admin Separation**: Service-key authenticated admin routes separate from regular user operations

### Out of Scope for MVP

- Advanced caching strategies (Redis, CDN)
- Real-time WebSocket connections
- Advanced analytics and metrics collection
- Third-party integrations and webhooks
- Performance optimization beyond basic caching

### MVP Success Criteria

MVP is successful when:
1. 100% of mobile app database operations go through API routes
2. All API endpoints require proper authentication (HMAC for mobile, CSRF for web)
3. Zero direct Supabase calls remain in either codebase
4. Existing functionality maintains identical behavior
5. Security testing confirms no external API access possible

## Post-MVP Vision

### Phase 2 Features

- **Advanced Caching**: Redis implementation for high-frequency data and session management
- **Real-time Features**: WebSocket integration for live notifications and updates
- **Analytics Integration**: Comprehensive usage tracking and business intelligence APIs
- **Performance Optimization**: Request batching, compression, and advanced query optimization
- **Enhanced Monitoring**: Full observability with metrics, logging, and alerting

### Long-term Vision

Transform DukanCard into the most secure and developer-friendly social commerce platform architecture, serving as an open-source reference implementation for privacy-conscious business platforms.

### Expansion Opportunities

- **API Marketplace**: Public APIs for third-party integrations with businesses
- **White-label Platform**: Architecture template for other social commerce implementations
- **Security-as-a-Service**: HMAC authentication middleware for other mobile applications
- **Developer Tools**: SDK and CLI tools for rapid social commerce platform development

## Technical Considerations

### Platform Requirements

- **Target Platforms**: Next.js 14+ (web), React Native with Expo (iOS/Android)
- **Browser/OS Support**: Modern browsers (Chrome 90+, Safari 14+, Firefox 88+), iOS 14+, Android 8+
- **Performance Requirements**: Sub-200ms API response times, 99.9% uptime, support for 10K+ concurrent users

### Technology Preferences

- **Frontend**: Continue with Next.js 14 and React Native, enhance Zustand stores for API caching
- **Backend**: Next.js API routes with TypeScript, maintain Supabase PostgreSQL database
- **Database**: Supabase with enhanced RLS policies, consider read replicas for performance
- **Hosting/Infrastructure**: Current hosting maintained, add API monitoring and logging

### Architecture Considerations

- **Repository Structure**: Maintain polyrepo structure, add shared API types and utilities
- **Service Architecture**: API-first design with clear separation between public and admin endpoints
- **Integration Requirements**: Preserve existing Supabase Auth, Storage, and Real-time integrations
- **Security/Compliance**: HMAC authentication, CSRF protection, comprehensive audit logging, GDPR compliance preparation

## Constraints & Assumptions

### Constraints

- **Budget**: Development team time only, no additional infrastructure costs for MVP
- **Timeline**: 12-week implementation window to minimize business disruption
- **Resources**: 2-3 developers available, maintain feature development velocity
- **Technical**: Must maintain backward compatibility, cannot introduce breaking changes to existing user experience

### Key Assumptions

- Existing device registration system is stable and secure
- Current HMAC middleware implementation is production-ready
- Zustand stores can effectively cache API responses without major refactoring
- Supabase RLS policies are correctly configured and will remain as backup security
- Mobile app users will accept any minor performance impact from API layer
- Development team has sufficient API development experience

## Risks & Open Questions

### Key Risks

- **Performance Impact**: API layer introduces additional latency that could degrade user experience, particularly on slower mobile connections
- **Migration Complexity**: Simultaneous refactoring of web and mobile codebases increases risk of introducing bugs or breaking existing functionality
- **Security Implementation**: HMAC authentication complexity could introduce subtle security vulnerabilities if not implemented correctly
- **Development Velocity**: Large-scale refactoring may slow new feature development during transition period
- **Testing Coverage**: Comprehensive testing of all migration scenarios may be time-intensive and require significant QA resources

### Open Questions

- Should we implement gradual rollout with feature flags or migrate entire modules at once?
- What is the acceptable performance impact threshold before we need caching optimization?
- How will we handle offline scenarios and request queuing in the mobile app?
- Should admin routes use separate domain/subdomain for additional security isolation?
- What monitoring and alerting do we need for the new API endpoints?

### Areas Needing Further Research

- Optimal Zustand caching strategies for different data types and update frequencies
- Impact of HMAC computation on mobile device battery life and performance
- Best practices for API versioning and backward compatibility during migration
- Security testing methodologies for HMAC authentication implementation
- Performance benchmarking tools for measuring API response times under load

## Next Steps

### Immediate Actions

1. **Infrastructure Setup**: Configure monitoring, logging, and testing frameworks for API routes
2. **Security Implementation**: Complete HMAC middleware enhancement and testing across all planned endpoints
3. **Migration Planning**: Create detailed sequence plan for eliminating direct Supabase calls, starting with authentication flows
4. **Team Alignment**: Conduct technical design review session with development team to validate approach and identify potential issues
5. **Testing Strategy**: Establish unit testing patterns and integration testing framework for API routes

### PM Handoff

This Project Brief provides the full context for DukanCard API Refactoring Initiative. The project is ready for detailed technical planning and implementation. Key success factors include maintaining security as the highest priority, ensuring zero functionality regression during migration, and establishing clear testing and rollback procedures for each migration phase.

---

## Mobile App Authentication & API Security Architecture

### 🔒 Current Infrastructure Analysis ✅

Excellent foundations in place:
- **Device Registration**: `devices` table with device-specific secrets
- **Refresh Token Management**: `refresh_tokens` table linked to devices
- **HMAC Implementation**: Production-ready middleware with replay protection
- **Auth Context**: Unified authentication handling for web/mobile

### 📱 Complete Mobile Authentication Flow

#### Phase 1: Device Registration & Initial Authentication

```typescript
// Mobile App: Initial Registration
const registerDevice = async () => {
  // 1. User completes login with Supabase Auth
  const { data: { session } } = await supabase.auth.signInWithPassword({ email, password });
  
  // 2. Register device with backend
  const deviceData = await api.post('/devices/register', {
    deviceName: await Device.getDeviceNameAsync(),
    platform: Platform.OS,
    appSignatureHash: await getAppSignature(), // Your app signature
  });
  
  // 3. Store secrets securely
  await SecureStore.setItemAsync('deviceId', deviceData.deviceId);
  await SecureStore.setItemAsync('deviceSecret', deviceData.deviceSecret);
  await SecureStore.setItemAsync('hmacKey', deviceData.hmacKey);
};
```

#### Phase 2: HMAC-Authenticated API Calls

```typescript
// Enhanced apiClient.ts with HMAC
export async function apiCall<T>(endpoint: string, options: ApiRequestOptions = {}) {
  const { method = 'GET', body } = options;
  
  // 1. Get device credentials
  const deviceId = await SecureStore.getItemAsync('deviceId');
  const hmacKey = await SecureStore.getItemAsync('hmacKey');
  const session = await supabase.auth.getSession();
  
  if (!deviceId || !hmacKey || !session.data.session) {
    throw new Error('Device not registered or user not authenticated');
  }
  
  // 2. Generate HMAC signature
  const timestamp = Date.now().toString();
  const bodyString = body ? JSON.stringify(body) : '';
  const signature = generateHMACSignature(method, endpoint, timestamp, bodyString, hmacKey);
  
  // 3. Make authenticated request
  const response = await fetch(`${BACKEND_CONFIG.baseUrl}${endpoint}`, {
    method,
    headers: {
      'Authorization': `Bearer ${session.data.session.access_token}`,
      'X-Device-Id': deviceId,
      'X-Timestamp': timestamp,
      'X-Signature': signature,
      'X-Client-Info': 'dukancard-mobile-client',
      'Content-Type': 'application/json',
    },
    body: bodyString || undefined,
  });
  
  return response.json();
}
```

### 🛡️ API Route Security Implementation

#### Enhanced Route Protection Middleware

```typescript
// lib/middleware/enhancedRouteProtection.ts
export interface SecurityOptions {
  requireHMAC: boolean;         // Mobile HMAC required
  requireCSRF: boolean;         // Web CSRF required  
  adminOnly: boolean;           // Service key required
  rateLimitTier: 'low' | 'medium' | 'high';
  allowedPlatforms: ('web' | 'ios' | 'android')[];
}

export function withEnhancedSecurity(
  handler: RouteHandler,
  options: SecurityOptions
) {
  return async (req: NextRequest, ...args: any[]) => {
    // 1. Rate limiting & brute force protection
    await rateLimitCheck(req, options.rateLimitTier);
    
    // 2. Platform validation
    await validatePlatform(req, options.allowedPlatforms);
    
    // 3. Admin route handling
    if (options.adminOnly) {
      return await handleAdminRoute(req, handler, ...args);
    }
    
    // 4. Standard authentication
    const context = await getAuthContext(req);
    if (!isAuthenticated(context)) {
      return unauthorizedResponse();
    }
    
    // 5. Platform-specific security
    if (context.isMobile && options.requireHMAC) {
      await verifyHMACMiddleware(req, true);
    }
    
    if (context.isWeb && options.requireCSRF) {
      await csrfMiddleware(req, context.userId);
    }
    
    return handler(req, context, ...args);
  };
}
```

#### Admin Route Separation

```typescript
// lib/middleware/adminRoutes.ts
async function handleAdminRoute(req: NextRequest, handler: RouteHandler) {
  // 1. Verify service key authentication
  const serviceKey = req.headers.get('Authorization')?.replace('Bearer ', '');
  if (serviceKey !== process.env.SUPABASE_SERVICE_ROLE_KEY) {
    return new NextResponse('Forbidden', { status: 403 });
  }
  
  // 2. Create admin context with service client
  const adminContext = {
    user: null,
    userId: 'system',
    isAuthenticated: true,
    isWeb: true,
    isMobile: false,
    isAdmin: true,
    supabase: createServiceRoleClient(),
  };
  
  return handler(req, adminContext);
}
```

### 🔄 Token Management Strategy

#### Refresh Token Implementation

```typescript
// app/api/auth/refresh/route.ts
export const POST = withEnhancedSecurity(async (req: NextRequest, context: AuthContext) => {
  const { refreshToken, deviceId } = await req.json();
  
  // 1. Verify refresh token in database
  const { data: tokenData, error } = await context.supabase
    .from('refresh_tokens')
    .select('*, devices!inner(*)')
    .eq('token_hash', hashToken(refreshToken))
    .eq('device_id', deviceId)
    .eq('devices.revoked', false)
    .single();
  
  if (error || !tokenData) {
    return new Response('Invalid refresh token', { status: 401 });
  }
  
  // 2. Check expiration
  if (new Date(tokenData.expires_at) < new Date()) {
    return new Response('Refresh token expired', { status: 401 });
  }
  
  // 3. Generate new access token
  const newAccessToken = await generateAccessToken(tokenData.user_id);
  
  // 4. Rotate refresh token
  const newRefreshToken = await rotateRefreshToken(tokenData.id);
  
  return Response.json({
    accessToken: newAccessToken,
    refreshToken: newRefreshToken,
    expiresIn: 3600,
  });
}, {
  requireHMAC: true,
  requireCSRF: false,
  adminOnly: false,
  rateLimitTier: 'medium',
  allowedPlatforms: ['ios', 'android'],
});
```

### ⚡ Security Features Implementation

#### 1. Device Revocation System

```typescript
// app/api/admin/devices/revoke/route.ts
export const POST = withEnhancedSecurity(async (req: NextRequest, context: AuthContext) => {
  const { deviceId, reason } = await req.json();
  
  // Revoke device and all associated tokens
  await context.supabase
    .from('devices')
    .update({ revoked: true, revoked_at: new Date().toISOString(), revocation_reason: reason })
    .eq('device_id', deviceId);
    
  await context.supabase
    .from('refresh_tokens')
    .delete()
    .eq('device_id', deviceId);
    
  return Response.json({ success: true });
}, {
  requireHMAC: false,
  requireCSRF: true,
  adminOnly: true,
  rateLimitTier: 'low',
  allowedPlatforms: ['web'],
});
```

#### 2. Suspicious Activity Detection

```typescript
// lib/security/activityMonitoring.ts
export async function detectSuspiciousActivity(req: NextRequest, context: AuthContext) {
  const indicators = [
    await checkRapidRequests(context.userId, context.deviceId),
    await checkGeolocationAnomaly(req.ip, context.userId),
    await checkDeviceFingerprinting(req, context.deviceId),
    await checkUnusualEndpoints(req.url, context.userId),
  ];
  
  const riskScore = indicators.reduce((sum, score) => sum + score, 0);
  
  if (riskScore > RISK_THRESHOLD) {
    await logSecurityEvent('SUSPICIOUS_ACTIVITY', {
      userId: context.userId,
      deviceId: context.deviceId,
      riskScore,
      indicators,
      ip: req.ip,
      userAgent: req.headers.get('user-agent'),
    });
    
    // Consider device quarantine for high risk scores
    if (riskScore > HIGH_RISK_THRESHOLD) {
      await quarantineDevice(context.deviceId);
    }
  }
}
```

#### 3. API Rate Limiting

```typescript
// lib/middleware/rateLimiting.ts
const RATE_LIMITS = {
  low: { requests: 100, window: 900 },    // 100/15min for admin
  medium: { requests: 1000, window: 900 }, // 1000/15min for auth
  high: { requests: 5000, window: 900 },   // 5000/15min for general API
};

export async function rateLimitCheck(req: NextRequest, tier: keyof typeof RATE_LIMITS) {
  const key = `rate_limit:${req.ip}:${tier}`;
  const limit = RATE_LIMITS[tier];
  
  // Use Redis or in-memory store for production
  const current = await redis.incr(key);
  if (current === 1) {
    await redis.expire(key, limit.window);
  }
  
  if (current > limit.requests) {
    throw new Error('Rate limit exceeded');
  }
}
```

### 🎯 Implementation Priorities

#### Week 1-2: Core Security Enhancement
1. **Enhanced HMAC Enforcement** - Apply to all existing API routes
2. **Admin Route Separation** - Implement service key authentication  
3. **Token Management** - Complete refresh token rotation system
4. **Security Monitoring** - Basic activity logging and device tracking

#### Week 3-4: Mobile App Integration
1. **Enhanced API Client** - HMAC signature generation in mobile app
2. **Secure Storage** - Device credentials management with SecureStore
3. **Error Handling** - Graceful handling of revoked devices and expired tokens
4. **Offline Support** - Request queuing and sync when online

#### Week 5-6: Advanced Security Features
1. **Suspicious Activity Detection** - Implement risk scoring and device quarantine
2. **Rate Limiting** - Per-device and per-IP rate limiting
3. **Security Dashboard** - Admin panel for device management and security monitoring
4. **Compliance Features** - Audit logging and privacy controls

---

## Parallel Development Roadmap - Web & Mobile API Refactoring

### 🏗️ Phase-Based Implementation Strategy

#### Phase 1: Foundation & Authentication (Weeks 1-2)

##### 🔐 Authentication Infrastructure

**Web Platform:**
- **Fix existing auth issues** - Debug broken routes in current auth flow
- **Enhance API routes** - `/api/auth/login`, `/api/auth/register`, `/api/auth/refresh`
- **CSRF protection** - Add to all web auth endpoints
- **Error handling** - Standardize auth error responses

**Mobile Platform:**
- **Create auth store** - `src/stores/authStore.ts` (matching web pattern)
- **HMAC integration** - Enhance `apiClient.ts` with signature generation
- **Secure storage** - Device credentials & token management
- **Auth screens** - Login, Register, OTP verification

**Shared:**
- **API Routes** - Complete auth endpoint refactoring
- **Business Logic** - Centralized auth validation & user management
- **Testing** - Unit tests for auth flows on both platforms

#### Phase 2: User Role Selection & Onboarding (Weeks 3-4)

##### 🎯 Role Selection & Initial Setup

**Web Platform:**
- **Role selection page** - Choose business vs customer
- **Onboarding flow** - Multi-step wizard refactoring
- **Fix existing issues** - Debug broken onboarding routes
- **Store enhancement** - Expand existing stores for onboarding data

**Mobile Platform:**
- **Create onboarding stores** - `src/stores/onboardingStore.ts`
- **Role selection screen** - Native UI matching web flow
- **Multi-step onboarding** - Business details, address, plan selection
- **Progress tracking** - Step completion and validation

**Shared:**
- **API Routes** - `/api/onboarding/*`, `/api/user/profile`, `/api/business/profile`
- **Validation Logic** - Centralized form validation and business rules
- **File Upload** - Profile images, business logos via API routes

#### Phase 3: Dashboard & Profile Management (Weeks 5-6)

##### 📊 Core Platform Features

**Web Platform:**
- **Dashboard refactoring** - Fix existing dashboard issues
- **Profile management** - Business/customer profile editing
- **Analytics integration** - Dashboard metrics via API routes
- **Store optimization** - Performance improvements for existing stores

**Mobile Platform:**
- **Create dashboard stores** - `src/stores/businessStore.ts`, `src/stores/customerStore.ts`
- **Dashboard screens** - Business metrics, customer feed
- **Profile screens** - Edit business/customer profiles
- **Navigation** - Bottom tabs and drawer navigation

**Shared:**
- **API Routes** - `/api/business/dashboard`, `/api/customer/profile`, `/api/analytics/*`
- **Business Logic** - Profile validation, dashboard data aggregation
- **Real-time** - Live updates for dashboard metrics

#### Phase 4: Product Management (Weeks 7-8)

##### 🛍️ Product Catalog & Management

**Web Platform:**
- **Product management** - Add/edit/delete products
- **Inventory tracking** - Stock management features
- **Image handling** - Product gallery management
- **Bulk operations** - Import/export functionality

**Mobile Platform:**
- **Create product stores** - `src/stores/productStore.ts`
- **Product screens** - Add, edit, view products
- **Camera integration** - Product photo capture
- **Inventory management** - Mobile-friendly stock tracking

**Shared:**
- **API Routes** - `/api/products/*`, `/api/inventory/*`, `/api/storage/upload`
- **Business Logic** - Product validation, pricing rules, inventory management
- **Search & Filtering** - Product search algorithms

#### Phase 5: Business Card & Settings (Weeks 9-10)

##### 💳 Digital Business Card & Configuration

**Web Platform:**
- **Card management** - Business card customization
- **Settings page** - Account preferences, privacy settings
- **QR code generation** - Card sharing functionality
- **Theme customization** - Business card styling

**Mobile Platform:**
- **Create settings stores** - `src/stores/settingsStore.ts`
- **Card management** - Mobile card editor
- **Settings screen** - App preferences, notifications
- **QR scanner** - Card discovery via QR codes

**Shared:**
- **API Routes** - `/api/business/card`, `/api/settings/*`, `/api/qr/*`
- **Business Logic** - Card validation, settings management
- **Template System** - Card template management

#### Phase 6: Discovery & Search (Weeks 11-12)

##### 🔍 Business Discovery & Customer Experience

**Web Platform:**
- **Discovery page** - Local business search
- **Advanced filters** - Category, location, rating filters
- **Map integration** - Location-based discovery
- **Search optimization** - Performance improvements

**Mobile Platform:**
- **Create discovery stores** - `src/stores/discoveryStore.ts`
- **Discovery screen** - Native search and filters
- **Location services** - GPS-based business discovery
- **Map integration** - Native map components

**Shared:**
- **API Routes** - `/api/discovery/*`, `/api/search/*`, `/api/location/*`
- **Search Algorithms** - Relevance ranking, location-based search
- **Caching Strategy** - Optimized search result caching

#### Phase 7: Single Business & Product Views (Weeks 13-14)

##### 📱 Detailed Views & Social Features

**Web Platform:**
- **Business profile pages** - Public business cards
- **Product detail pages** - Full product information
- **Social features** - Reviews, likes, comments
- **SEO optimization** - Meta tags, structured data

**Mobile Platform:**
- **Create social stores** - `src/stores/socialStore.ts` (matching web)
- **Business profile screens** - Full business information
- **Product detail screens** - Product galleries, specifications
- **Social interactions** - Native like, comment, share

**Shared:**
- **API Routes** - `/api/business/[id]`, `/api/products/[id]`, `/api/social/*`
- **Social Logic** - Likes, comments, reviews, follow systems
- **Content Management** - Rich media handling

### 📋 Store Architecture Parallel Development

#### Web Stores (Existing + Enhancements)
```typescript
// Current: dukancard/lib/stores/
likeCommentStore.ts ✅ (exists)

// To Add:
authStore.ts
onboardingStore.ts  
businessStore.ts
customerStore.ts
productStore.ts
discoveryStore.ts
settingsStore.ts
```

#### Mobile Stores (New Creation)
```typescript
// Target: dukancard-app/src/stores/
authStore.ts         // Phase 1
onboardingStore.ts   // Phase 2  
businessStore.ts     // Phase 3
customerStore.ts     // Phase 3
productStore.ts      // Phase 4
settingsStore.ts     // Phase 5
discoveryStore.ts    // Phase 6
socialStore.ts       // Phase 7
```

### 🔄 Parallel Development Workflow

#### Each Phase Process:
1. **API Routes First** - Create/fix backend endpoints with proper security
2. **Web Integration** - Fix existing issues + integrate with new API routes
3. **Mobile Implementation** - Create stores + screens using same API routes
4. **Cross-Platform Testing** - Ensure identical behavior across platforms
5. **Performance Optimization** - Caching, error handling, offline support

#### Quality Gates:
- ✅ **API Route Tests** - 95%+ coverage for each endpoint
- ✅ **Cross-Platform Parity** - Identical features and behavior
- ✅ **Security Validation** - HMAC, CSRF, RLS properly implemented
- ✅ **Performance Benchmarks** - Sub-200ms response times maintained

### 🚀 Implementation Strategy

**Immediate Next Steps:**

1. **Start with Phase 1** - Authentication foundation (most critical)
2. **Fix Web Issues** - Debug existing broken auth routes
3. **Create Mobile Auth Store** - Match web store patterns
4. **Security Implementation** - HMAC + CSRF enforcement

**Implementation Options:**

1. **Begin Phase 1 Implementation** - Fix web auth issues + create mobile auth store
2. **Create Store Templates** - Boilerplate for consistent store patterns
3. **Design Cross-Platform Testing** - Validation strategy for feature parity
4. **Deep Dive Specific Phase** - Detailed planning for particular phase

This roadmap ensures both platforms evolve together with identical functionality while addressing current web issues and building proper mobile infrastructure.

---

*Document updated: $(date)*
*Version: 2.0*
*Status: Ready for Implementation with Comprehensive Security & Parallel Development Plan*