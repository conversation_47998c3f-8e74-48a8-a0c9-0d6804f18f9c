import { createServiceRoleClient } from '@/utils/supabase/service-role';
import { createHash } from 'crypto';

/**
 * Token management service for handling refresh token and JWT blacklisting
 */
export class TokenService {
  private supabase = createServiceRoleClient();

  /**
   * Hash a token for secure storage
   */
  private hashToken(token: string): string {
    return createHash('sha256').update(token).digest('hex');
  }

  /**
   * Blacklist refresh tokens for a specific device
   */
  async blacklistDeviceTokens(deviceId: string, reason: string, adminUserId: string): Promise<void> {
    try {
      // Get device info
      const { data: device, error: deviceError } = await this.supabase
        .from('devices')
        .select('user_id')
        .eq('device_id', deviceId)
        .single();

      if (deviceError || !device) {
        throw new Error('Device not found');
      }

      // Invalidate all refresh tokens for this device
      // Note: This is a simplified implementation. In a real system, you'd need to track
      // refresh tokens more carefully or integrate with your auth system's token store
      const expiresAt = new Date(Date.now() + 7 * 24 * 60 * 60 * 1000); // 7 days from now

      await this.supabase
        .from('refresh_token_blacklist')
        .insert({
          user_id: device.user_id,
          device_id: deviceId,
          token_hash: 'device-revocation', // Placeholder - would be actual token hashes
          reason,
          admin_user_id: adminUserId,
          expires_at: expiresAt.toISOString()
        });

      // Force logout by invalidating auth session
      // This would integrate with your auth system
      await this.invalidateUserSessions(device.user_id, deviceId);

    } catch (error) {
      console.error('Error blacklisting device tokens:', error);
      throw error;
    }
  }

  /**
   * Blacklist a specific JWT token
   */
  async blacklistJWT(jti: string, userId: string, deviceId: string | null, reason: string, adminUserId: string): Promise<void> {
    try {
      // Calculate expiry based on JWT standard expiry (e.g., 1 hour)
      const expiresAt = new Date(Date.now() + 60 * 60 * 1000); // 1 hour from now

      await this.supabase
        .from('jwt_blacklist')
        .insert({
          jti,
          user_id: userId,
          device_id: deviceId,
          reason,
          admin_user_id: adminUserId,
          expires_at: expiresAt.toISOString()
        });

    } catch (error) {
      console.error('Error blacklisting JWT:', error);
      throw error;
    }
  }

  /**
   * Check if a JWT token is blacklisted
   */
  async isJWTBlacklisted(jti: string): Promise<boolean> {
    try {
      const { data, error } = await this.supabase
        .from('jwt_blacklist')
        .select('id')
        .eq('jti', jti)
        .gt('expires_at', new Date().toISOString())
        .single();

      return !error && data !== null;
    } catch (error) {
      console.error('Error checking JWT blacklist:', error);
      return false; // Fail open for availability
    }
  }

  /**
   * Check if a refresh token is blacklisted
   */
  async isRefreshTokenBlacklisted(tokenHash: string): Promise<boolean> {
    try {
      const { data, error } = await this.supabase
        .from('refresh_token_blacklist')
        .select('id')
        .eq('token_hash', tokenHash)
        .gt('expires_at', new Date().toISOString())
        .single();

      return !error && data !== null;
    } catch (error) {
      console.error('Error checking refresh token blacklist:', error);
      return false; // Fail open for availability
    }
  }

  /**
   * Invalidate user sessions (integrate with auth system)
   */
  private async invalidateUserSessions(userId: string, deviceId?: string): Promise<void> {
    try {
      // Force session invalidation in Supabase Auth
      await this.supabase.auth.admin.signOut(userId);
      
      // Additional device-specific session cleanup would go here
      // This might involve clearing session data, websocket connections, etc.
      
    } catch (error) {
      console.error('Error invalidating user sessions:', error);
      // Don't throw - session invalidation is best effort
    }
  }

  /**
   * Bulk blacklist tokens for multiple devices
   */
  async bulkBlacklistDeviceTokens(deviceIds: string[], reason: string, adminUserId: string): Promise<void> {
    for (const deviceId of deviceIds) {
      try {
        await this.blacklistDeviceTokens(deviceId, reason, adminUserId);
      } catch (error) {
        console.error(`Error blacklisting tokens for device ${deviceId}:`, error);
        // Continue with other devices
      }
    }
  }

  /**
   * Cleanup expired blacklisted tokens
   */
  async cleanupExpiredTokens(): Promise<void> {
    try {
      const now = new Date().toISOString();

      await Promise.all([
        this.supabase
          .from('refresh_token_blacklist')
          .delete()
          .lt('expires_at', now),
        
        this.supabase
          .from('jwt_blacklist')
          .delete()
          .lt('expires_at', now)
      ]);

    } catch (error) {
      console.error('Error cleaning up expired tokens:', error);
    }
  }

  /**
   * Get blacklist statistics
   */
  async getBlacklistStats(): Promise<{
    activeRefreshTokens: number;
    activeJWTs: number;
    totalBlacklisted: number;
  }> {
    try {
      const now = new Date().toISOString();

      const [refreshTokenCount, jwtCount] = await Promise.all([
        this.supabase
          .from('refresh_token_blacklist')
          .select('*', { count: 'exact', head: true })
          .gt('expires_at', now),
        
        this.supabase
          .from('jwt_blacklist')
          .select('*', { count: 'exact', head: true })
          .gt('expires_at', now)
      ]);

      const activeRefreshTokens = refreshTokenCount.count || 0;
      const activeJWTs = jwtCount.count || 0;

      return {
        activeRefreshTokens,
        activeJWTs,
        totalBlacklisted: activeRefreshTokens + activeJWTs
      };

    } catch (error) {
      console.error('Error getting blacklist stats:', error);
      return {
        activeRefreshTokens: 0,
        activeJWTs: 0,
        totalBlacklisted: 0
      };
    }
  }
}

export const tokenService = new TokenService();