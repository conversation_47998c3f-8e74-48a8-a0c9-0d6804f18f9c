import {
  registerDevice,
  getDeviceById,
  getUserDevices,
  validateDeviceCredentials,
  getDeviceHMACKey,
  updateDeviceActivity,
  revokeDevice,
  isDeviceActive,
} from '@/lib/services/deviceService';

// Mock dependencies
jest.mock('@/utils/supabase/service-role', () => ({
  createServiceRoleClient: jest.fn(),
}));

jest.mock('@/lib/security/hashing', () => ({
  hashSecret: jest.fn().mockResolvedValue('hashed-secret'),
  compareSecret: jest.fn(),
}));

jest.mock('crypto', () => ({
  randomBytes: jest.fn(() => ({
    toString: jest.fn(() => 'generated-device-secret-hex'),
  })),
}));

import { createServiceRoleClient } from '@/utils/supabase/service-role';
import { hashSecret, compareSecret } from '@/lib/security/hashing';
import crypto from 'crypto';

describe('DeviceService', () => {
  let mockSupabase: any;

  beforeEach(() => {
    jest.clearAllMocks();
    
    // Setup mock Supabase client with proper chaining
    const mockSingle = jest.fn();
    const mockOrder = jest.fn(() => ({ data: [], error: null }));
    const mockEq = jest.fn(() => ({ single: mockSingle, order: mockOrder }));
    const mockSelect = jest.fn(() => ({ 
      single: mockSingle,
      eq: mockEq,
      order: mockOrder,
    }));
    const mockInsert = jest.fn(() => ({ select: mockSelect }));
    const mockUpdate = jest.fn(() => ({ eq: mockEq }));
    const mockFrom = jest.fn(() => ({ 
      insert: mockInsert,
      select: mockSelect,
      update: mockUpdate,
    }));
    
    mockSupabase = {
      from: mockFrom,
      mockFrom,
      mockInsert,
      mockSelect,
      mockSingle,
      mockUpdate,
      mockEq,
    };
    
    (createServiceRoleClient as jest.Mock).mockReturnValue(mockSupabase);
  });

  describe('registerDevice', () => {
    it('should successfully register a device', async () => {
      const mockDeviceId = 'test-device-id';
      mockSupabase.mockSingle.mockResolvedValue({
        data: { device_id: mockDeviceId },
        error: null,
      });

      const deviceData = {
        userId: 'test-user-id',
        deviceName: 'iPhone 15',
        platform: 'ios' as const,
        appVersion: '1.0.0',
        appSignatureHash: 'test-signature',
      };

      const result = await registerDevice(deviceData);

      expect(result.deviceId).toBe(mockDeviceId);
      expect(result.deviceSecret).toBe('generated-device-secret-hex');
      expect(result.hmacKey).toBe('generated-device-secret-hex');
      
      expect(mockSupabase.mockFrom).toHaveBeenCalledWith('devices');
      expect(mockSupabase.mockInsert).toHaveBeenCalledWith({
        user_id: 'test-user-id',
        device_name: 'iPhone 15',
        platform: 'ios',
        app_version: '1.0.0',
        device_secret_hash: 'hashed-secret',
        hmac_key_hash: 'generated-device-secret-hex',
        app_signature_hash: 'test-signature',
      });
      
      expect(hashSecret).toHaveBeenCalledWith('generated-device-secret-hex');
      expect(crypto.randomBytes).toHaveBeenCalledWith(32);
    });

    it('should handle database errors', async () => {
      mockSupabase.mockSingle.mockResolvedValue({
        data: null,
        error: { message: 'Database error' },
      });

      const deviceData = {
        userId: 'test-user-id',
        deviceName: 'iPhone 15',
        platform: 'ios' as const,
        appVersion: '1.0.0',
      };

      await expect(registerDevice(deviceData)).rejects.toThrow('Failed to register device');
    });
  });

  describe('getDeviceById', () => {
    it('should return device data when device exists', async () => {
      const mockDevice = {
        device_id: 'test-device-id',
        user_id: 'test-user-id',
        device_name: 'iPhone 15',
        platform: 'ios',
        revoked: false,
      };
      
      mockSupabase.mockSingle.mockResolvedValue({
        data: mockDevice,
        error: null,
      });

      const result = await getDeviceById('test-device-id');

      expect(result).toEqual(mockDevice);
      expect(mockSupabase.mockFrom).toHaveBeenCalledWith('devices');
    });

    it('should return null when device does not exist', async () => {
      mockSupabase.mockSingle.mockResolvedValue({
        data: null,
        error: { code: 'PGRST116' }, // No rows returned
      });

      const result = await getDeviceById('non-existent-id');

      expect(result).toBeNull();
    });

    it('should throw error for database errors', async () => {
      mockSupabase.mockSingle.mockResolvedValue({
        data: null,
        error: { message: 'Database error' },
      });

      await expect(getDeviceById('test-device-id')).rejects.toThrow('Failed to fetch device');
    });
  });

  describe('getUserDevices', () => {
    it('should return user devices', async () => {
      const mockDevices = [{
        device_id: 'device-1',
        user_id: 'test-user-id',
        device_name: 'iPhone 15',
        platform: 'ios',
        app_version: '1.0.0',
        revoked: false,
        created_at: '2023-01-01T00:00:00Z',
        last_seen_at: '2023-01-02T00:00:00Z',
      }];
      
      mockSupabase.from.mockReturnValue({
        select: jest.fn(() => ({
          eq: jest.fn(() => ({
            order: jest.fn(() => ({
              data: mockDevices,
              error: null,
            })),
          })),
        })),
      });

      const result = await getUserDevices('test-user-id');

      expect(result).toHaveLength(1);
      expect(result[0]).toEqual({
        deviceId: 'device-1',
        userId: 'test-user-id',
        deviceName: 'iPhone 15',
        platform: 'ios',
        appVersion: '1.0.0',
        isRevoked: false,
        registeredAt: '2023-01-01T00:00:00Z',
        lastActivity: '2023-01-02T00:00:00Z',
      });
    });

    it('should handle database errors', async () => {
      mockSupabase.from.mockReturnValue({
        select: jest.fn(() => ({
          eq: jest.fn(() => ({
            order: jest.fn(() => ({
              data: null,
              error: { message: 'Database error' },
            })),
          })),
        })),
      });

      await expect(getUserDevices('test-user-id')).rejects.toThrow('Failed to fetch user devices');
    });
  });

  describe('validateDeviceCredentials', () => {
    it('should return true for valid credentials', async () => {
      const mockDevice = {
        device_id: 'test-device-id',
        device_secret_hash: 'hashed-secret',
        revoked: false,
      };
      
      mockSupabase.mockSingle.mockResolvedValue({
        data: mockDevice,
        error: null,
      });
      
      (compareSecret as jest.Mock).mockResolvedValue(true);

      const result = await validateDeviceCredentials('test-device-id', 'test-secret');

      expect(result).toBe(true);
      expect(compareSecret).toHaveBeenCalledWith('test-secret', 'hashed-secret');
    });

    it('should return false for revoked device', async () => {
      const mockDevice = {
        device_id: 'test-device-id',
        device_secret_hash: 'hashed-secret',
        revoked: true,
      };
      
      mockSupabase.mockSingle.mockResolvedValue({
        data: mockDevice,
        error: null,
      });

      const result = await validateDeviceCredentials('test-device-id', 'test-secret');

      expect(result).toBe(false);
    });

    it('should return false for non-existent device', async () => {
      mockSupabase.mockSingle.mockResolvedValue({
        data: null,
        error: { code: 'PGRST116' },
      });

      const result = await validateDeviceCredentials('non-existent-id', 'test-secret');

      expect(result).toBe(false);
    });
  });

  describe('getDeviceHMACKey', () => {
    it('should return HMAC key for active device', async () => {
      const mockDevice = {
        device_id: 'test-device-id',
        hmac_key_hash: 'test-hmac-key',
        revoked: false,
      };
      
      mockSupabase.mockSingle.mockResolvedValue({
        data: mockDevice,
        error: null,
      });

      const result = await getDeviceHMACKey('test-device-id');

      expect(result).toBe('test-hmac-key');
    });

    it('should return null for revoked device', async () => {
      const mockDevice = {
        device_id: 'test-device-id',
        hmac_key_hash: 'test-hmac-key',
        revoked: true,
      };
      
      mockSupabase.mockSingle.mockResolvedValue({
        data: mockDevice,
        error: null,
      });

      const result = await getDeviceHMACKey('test-device-id');

      expect(result).toBeNull();
    });
  });

  describe('updateDeviceActivity', () => {
    it('should update device activity timestamp', async () => {
      const mockUpdate = jest.fn().mockResolvedValue({ error: null });
      const mockEq = jest.fn().mockReturnValue(mockUpdate);
      mockSupabase.mockUpdate.mockReturnValue({ eq: mockEq });

      await updateDeviceActivity('test-device-id');

      expect(mockSupabase.mockUpdate).toHaveBeenCalledWith({
        last_seen_at: expect.any(String),
      });
      expect(mockEq).toHaveBeenCalledWith('device_id', 'test-device-id');
    });
  });

  describe('revokeDevice', () => {
    it('should revoke device with reason', async () => {
      const mockUpdate = jest.fn().mockResolvedValue({ error: null });
      const mockEq = jest.fn().mockReturnValue(mockUpdate);
      mockSupabase.mockUpdate.mockReturnValue({ eq: mockEq });

      await revokeDevice('test-device-id', 'Security breach');

      expect(mockSupabase.mockUpdate).toHaveBeenCalledWith({
        revoked: true,
        revoked_at: expect.any(String),
        revocation_reason: 'Security breach',
      });
      expect(mockEq).toHaveBeenCalledWith('device_id', 'test-device-id');
    });

    it('should handle database errors', async () => {
      mockSupabase.from.mockReturnValue({
        update: jest.fn(() => ({
          eq: jest.fn(() => ({
            data: null,
            error: { message: 'Database error' },
          })),
        })),
      });

      await expect(revokeDevice('test-device-id')).rejects.toThrow('Failed to revoke device');
    });
  });

  describe('isDeviceActive', () => {
    it('should return true for active device', async () => {
      const mockDevice = {
        device_id: 'test-device-id',
        revoked: false,
      };
      
      mockSupabase.mockSingle.mockResolvedValue({
        data: mockDevice,
        error: null,
      });

      const result = await isDeviceActive('test-device-id');

      expect(result).toBe(true);
    });

    it('should return false for revoked device', async () => {
      const mockDevice = {
        device_id: 'test-device-id',
        revoked: true,
      };
      
      mockSupabase.mockSingle.mockResolvedValue({
        data: mockDevice,
        error: null,
      });

      const result = await isDeviceActive('test-device-id');

      expect(result).toBe(false);
    });

    it('should return false for non-existent device', async () => {
      mockSupabase.mockSingle.mockResolvedValue({
        data: null,
        error: { code: 'PGRST116' },
      });

      const result = await isDeviceActive('non-existent-id');

      expect(result).toBe(false);
    });
  });
});